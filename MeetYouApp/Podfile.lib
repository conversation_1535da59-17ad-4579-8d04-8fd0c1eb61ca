# 本地库
$ZZIMYMain = true
if $ZZIMY<PERSON>ain
pod 'ZZIMYMain', :path => '../ZZIMYMain'
end

$IMYBaseKit = true
if $IMYBaseKit
pod 'IMYBaseKit', :path => '../IMYBaseKit'
end

$MeAccount = false
if $MeAccount
pod 'IMYMe', :path => '../IMYMe'
pod 'IMYAccount', :path => '../IMYAccount'
end

$IMYCommonKit = false
if $IMYCommonKit
pod 'IMYCommonKit', :path => '../IMYCommonKit'
end

# 消息模块
$IMYMSG = false
if $IMYMSG
pod 'IMYMSG', :path => '../IMYMSG'
end

# 柚聊模块
$CHATAI = false
if $CHATAI
pod 'ChatAI', :path => '../ChatAI'
end

# 记录模块
$IMYRecord = false
if $IMYRecord
pod 'IMYRecord', :path => '../IMYRecord'
end

# 资讯
$IMYNews = false
if $IMYNews
pod 'IMYNews', :path => '../IMYNews'
end

# 社区
$IMYTTQ = false
if $IMYTTQ
pod 'IMYTTQ', :path => '../IMYTTQ'
end

# 大社区公共控件
$IMYUGC = false
if $IMYUGC
pod 'IMYUGC', :path => '../IMYUGC'
end

# 问答
$IMYAnswer = false
if $IMYAnswer
pod 'IMYAnswer', :path => '../IMYAnswer'
end

# 问答
$IMYTools_Swift = false
if $IMYTools_Swift
pod 'IMYTools_Swift', :path => '../IMYTools_Swift'
end

# 意见反馈
$IMYFeedback = false
if $IMYFeedback
pod 'IMYFeedback', :path => '../IMYFeedback'
end

# 判断环境变量是否为true
def env_true?(env)
  return false unless ENV[env]
  return false if ["no", "false", "off", "0"].include?(ENV[env].to_s)
  return true
end

# 如CODE_COVERAGE_ENABLED环境变量为true, 则添加代码覆盖率插桩库列表
if env_true?('CODE_COVERAGE_ENABLED')
    $MeetyouAppLibs = [
        "ZZIMYMain",
        "IOC-Protocols",
        "IMYBaseKit",
        "IMYCommonKit",
        "IMYLaunchController",
        "IMYReactNative",
        "IMYCCAPI",
        "IMYMWPhotoBrowser",
        "IMYFeedback",
        "IMYSwift",
        "ChatAI",
        "IMYMPN",
        "IMYSVR",
        "IMYTTQ",
        "IMYNews",
        "IMYKnowledge",
        "IMYUGC",
        "IMYAnswer",
        "IMYEBPublic",
        "IMYEBViewKit",
        "IMYEBSearch",
        "IMY_EBusiness",
        "IMYEBLiveBroadcast",
        "IMYFHBusiness",
        "IMYFHPublic",
        "IMYYoupin",
        "IMYRecord",
        "IMYYunyuHome",
        "IMYYunyuChange",
        "IMYYQBasicServices",
        "IMYYQHome",
        "IMYTools",
        "IMYLamaHome",
        "IMYTools_Swift",
        "IMYYunyuReport",
        "IMYPostpartumRecovery",
        "IMYECE",
        "YBBTools",
        "IMYThousandDays",
        "IMYEduAide",
        "IMYBabyFeed",
        "IMYGravidityCheck",
        "BBJPhotoRecognition",
        "BBJBabyHome",
        "BBJViewKit",
        "IMYMe",
        "IMYAccount",
        "IMYAdvertisement",
        "IMYMiniProgram",
        "IMYMSG",
        "IMYMSGJump",
        "Poseidon",
        "HarryAPM"
    ]
else
    $MeetyouAppLibs = []
end

