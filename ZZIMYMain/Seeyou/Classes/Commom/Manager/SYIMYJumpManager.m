//
//  SYIMYJumpManager.m
//  Seeyou
//
//  Created by ljh on 15/6/4.
//  Copyright (c) 2015年 linggan. All rights reserved.
//

#import "SYIMYJumpManager.h"
#import "IMY_ViewKit.h"
#import "SYCMsgVC.h"
#if   __has_include(<IMYEBJumpManager.h>)
#import <IMYEBJumpManager.h>
#endif

#if   __has_include("TTQJumpType.h")
#import "TTQJumpType.h"
#import "TTQMyTopicModel.h"
#import "TTQTopicModel.h"
#endif

#import "SYGlobalMacros.h"
#import "SYSystemMessageVC.h"

#import "IMYYQTipDetailControllerV1_3.h"
#import "SYDynamicModel.h"
#import "SYDynamicUserInfoHelper.h"
#import "SYPublishDynamicViewController.h"
#import "SYPushHandle.h"

#import <IMYAccountBindService.h>
#import <IMYYQTipModel.h>

@interface SYIMYJumpManager () <IMYJumpManagerInterceptDelegate>
@end

@implementation SYIMYJumpManager

IMY_KYLIN_FUNC(IMY_KYLIN_STAGE_PREMAIN, 200, IMY_KYLIN_QUEUE_ASYNC) {
    [[IMYJumpManager shareJumpManager] interceptJumpWithObserver:[SYIMYJumpManager shareManager] forMap:nil andTypes:0, nil];
}

+ (instancetype)shareManager {
    static id instance;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[self alloc] init];
#if   __has_include("TTQJumpType.h")
        [[[NSNotificationCenter defaultCenter] rac_addObserverForName:TTQAddFocusUserArray object:nil] subscribeNext:^(NSNotification *notification) {
            NSArray *models = notification.object;
            [SYDynamicUserInfoHelper addFocusUserIDs:models];

        }];

        [[[NSNotificationCenter defaultCenter] rac_addObserverForName:TTQPwdErrorAndNotUser object:nil] subscribeNext:^(NSNotification *notification) {
            NSData *responseData = notification.object;
            [SYPublicFun pwdErrorAndNotUser:[NSData imy_dictionaryWithJSONData:responseData]];
        }];
        [[[NSNotificationCenter defaultCenter] rac_addObserverForName:TTQAddDynamicModel object:nil] subscribeNext:^(NSNotification *notification) {
            NSDictionary *dic = notification.object;
            //填充数据到动态
            NSString *value = dic[@"did"];
            NSString *topicid = dic[@"topic_id"];
            NSString *imageUrl = dic[@"image"];
            if (value) {
                SYDynamicModel *publish = [[SYDynamicModel alloc] init];
                publish.type = SYDynamicModelTypeShareTopic;
                publish.content = [NSString filterBlankAndBlankLines:dic[@"content"]];
                publish.created_time = [[NSDate date] imy_getDateTimeString];
                publish.screen_name = [SYUserHelper sharedHelper].screen_name;
                if ([SYUserHelper sharedHelper].headImageURL) {
                    publish.avatar = @{@"large": [SYUserHelper sharedHelper].headImageURL};
                }
                publish.user_id = [SYUserHelper sharedHelper].userid.integerValue;
                if (imageUrl.length > 0) {
                    publish.images = @[imageUrl];
                }
                publish.d_id = value.integerValue;
                publish.allow_operate = YES;
                publish.topic_id = topicid.intValue;
                publish.publisher = [SYUserHelper sharedHelper].screen_name;
                [publish saveToDB];
            }

        }];
#endif
       
    });
    return instance;
}

- (BOOL)imy_shouldJumptoPageWithType:(NSInteger)type currentVC:(UIViewController *)currentVC params:(NSDictionary *)params map:(NSString *)mapKey {
    if (type == IMYJumpManagerGoLogin) {
        [[IMYURIManager shareURIManager] runActionWithString:@"login"];
        return YES;
    }
#if   __has_include("TTQJumpType.h")
    //    NSString* varString = params[@"varString"];
    NSInteger varID = [params[@"varID"] integerValue];
    if ([mapKey isEqualToString:@"eb"]) {
        do {
            if (type == IMYEBJumpTypeYouBi) {
                //柚币专场跳转
                NSInteger brand_area_id = varID;
                NSMutableDictionary *paramsDic = [NSMutableDictionary dictionaryWithDictionary:params];
                paramsDic[@"brand_area_id"] = @(brand_area_id);

                [[IMYURIManager shareURIManager] runActionWithPath:@"youbi/session" params:paramsDic info:nil];
                break;
            } else if (type == IMYEBJumpTypeTheme) {
                [[IMYURIManager shareURIManager] runActionWithPath:@"theme" params:nil info:nil];
                break;
            } else if (type == IMYEBJumpTypeThemeDetail) {
                if (varID > 0) // 皮肤ID
                {
                    NSInteger themeID = varID;
                    id vc = [NSClassFromString(@"SYThemePreviewViewController") new];
                    [vc setValue:@(themeID) forKey:@"themeID"];
                    [currentVC imy_push:vc];
                }
                break;
            } else if (type == IMYEBJumpTypeSpentUCoin) {
                ///发柚币

                [[IMYURIManager shareURIManager] runActionWithPath:@"youbi" params:params info:nil];
                break;
            }

            return NO;
        } while (0);

        return YES;
    } else if ([mapKey isEqualToString:TTQJumpMapKey]) {
        if (type == TTQJumpTypeBindMobile) {
            //            [currentVC push:[SYBindMobileVC new] animated:YES];
            [currentVC imy_push:[IMYAccountPhoneBindVC new] animated:YES];
            return YES;
        } else if (type == TTQJumpTypeEditNickname) {
            [currentVC imy_push:[SYEditNicknameVC new] animated:YES];
            return YES;
        } else if (type == TTQJumpTypeUserIntroduce) {
            NSUInteger userId = [params[@"userid"] integerValue];
            NSUInteger fromeType = [params[@"fromType"] unsignedIntegerValue];

            [[IMYURIManager shareURIManager] runActionWithPath:@"dynamic/homePage"
                                                        params:@{ @"userId": @(userId),
                                                                  @"fromType": @(fromeType) }
                                                          info:nil];

            return YES;
        } else if (type == TTQJumpTypeSystemMessage) {
            [currentVC imy_push:[SYSystemMessageVC new] animated:YES];
            return YES;
        } else if (type == TTQJumpTypeMessage) {
            [currentVC imy_push:[SYCMsgVC new] animated:YES];
            return YES;
        } else if (type == TTQJumpTypeBindSina) {
            [[IMYAccountBindService loginAndBindWithType:IMYAccountSecureBindVMTypeSina] subscribeNext:^(id x){

            }];
            return YES;
        } else if (type == TTQJumpTypeDynamicUserInfo) {
            [[IMYURIManager shareURIManager] runActionWithPath:@"dynamic/homePage"
                                                        params:@{ @"userId": @([params[@"userID"] integerValue]),
                                                                  @"fromType": @([params[@"fromType"] integerValue]) }
                                                          info:nil];
            return YES;
        } else if (type == TTQJumpTypeHonorList) {
            return YES;
        } else if (type == TTQJumpTypeDynamicShare) {
            NSString *defaultIcon = params[@"defaultIcon"];
            TTQTopicModel *shareModel = [params[@"shareModel"] toModel:[TTQTopicModel class]];
            NSInteger fromType = [params[@"fromType"] integerValue];
            SYPublishDynamicViewController *pbvc = [[SYPublishDynamicViewController alloc] initWithShareModel:shareModel defaultIcon:defaultIcon type:SYPublicDynamicTypeTopic];
            if (fromType > 0) {
                pbvc.fromType = fromType;
            }
            [currentVC imy_present:pbvc];
            return YES;
        } else if (type == TTQJumpTypeThemePreview) {
            id themeID = params[@"themeid"];
            UIViewController *vc = [NSClassFromString(@"SYThemePreviewViewController") new];
            [vc setValue:themeID forKey:@"themeID"];
            [currentVC imy_push:vc];
            return YES;
        } else if (type == TTQJumpTypeThemeGroup) {
            [[IMYURIManager shareURIManager] runActionWithPath:@"theme" params:nil info:nil];
            return YES;
        } else if (type == TTQJumpTypeDynamic) {
            [[IMYURIManager shareURIManager] runActionWithPath:@"dynamic/detail" params:@{@"dynamicID": params[@"dynamicID"]} info:nil];
            return YES;
        }

    }
#endif
  
//    else if ([mapKey isEqualToString:IMYIndividuationJumpMapKey]) {
//        if (type == IMYIJumpTypeEmoticonList) {
//            IMYIEmoticonListVM *vm = [IMYIEmoticonListVM new];
//            IMYIEmoticonListVC *vc = [IMYIEmoticonListVC new];
//            vc.viewModel = vm;
//            vc.title = params[@"title"];
//            [currentVC imy_push:vc animated:YES];
//            return YES;
//        } else if (type == IMYIJumpTypeThemeCategoryList) {
//            UIViewController *vc = [NSClassFromString(@"SYThemeCategoryVC") new];
//            vc.title = params[@"title"];
//            [currentVC imy_push:vc animated:YES];
//            return YES;
//        } else if (type == IMYIJumpTypeThemeDetail) {
//            id themeID = params[@"themeID"];
//            UIViewController *vc = [NSClassFromString(@"SYThemePreviewViewController") new];
//            vc.title = params[@"title"];
//            [vc setValue:themeID forKey:@"themeID"];
//            [currentVC imy_push:vc];
//            return YES;
//        } else if (type == IMYIJumpTypeSubjectList) {
//            UIViewController *subjectVC = [NSClassFromString(@"SYSubjectListVC") new];
//            id subjectVM = [NSClassFromString(@"SYSubjectListVM") new];
//            [subjectVC setValue:subjectVM forKey:@"viewModel"];
//            subjectVC.title = params[@"title"];
//            [currentVC imy_push:subjectVC animated:YES];
//            return YES;
//        } else if (type == IMYIJumpTypeSubjectDetail) {
//            id subjectID = params[@"subjectID"];
//            UIViewController *vc = [NSClassFromString(@"SYSubjectDetailVC") new];
//            id vm = [NSClassFromString(@"SYSubjectDetailVM") new];
//            vc.title = params[@"title"];
//            [vm setValue:subjectID forKey:@"subjectID"];
//            [vc setValue:vm forKey:@"viewModel"];
//            [currentVC imy_push:vc animated:YES];
//            return YES;
//        }
//    }
    return NO;
}


@end
