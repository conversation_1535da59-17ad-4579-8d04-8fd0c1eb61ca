//
//  SYPushHandle.m
//  Seeyou
//
//  Created by <PERSON><PERSON> on 14/12/25.
//  Copyright (c) 2014年 linggan. All rights reserved.
//

#import "SYPushHandle.h"
#import "IMYJumpManager.h"
#import "SYGlobalMacros.h"
#import "IMYAccountChangePasswordVC.h"
#import "NSArray+SY.h"
#import "SYDynamicUserListVC.h"
#import "SYMyThemesViewController.h"
#import "SYPublishDynamicViewController.h"
#import "SYRatingHelper.h"
#import <IMYMe/SYSingleSelectViewController.h>
#import "SYThemePreviewViewController.h"
#import "SYUserInfoVC_V2.h"
#if   __has_include(<IMYEBHeaders.h>)
#import <IMYEBBrandItemModel.h>
#import <IMYEBHeaders.h>
#endif

#if   __has_include(<IMYYou<PERSON>DetailVC.h>)
#import <IMYYoubiDetailVC.h>
#endif

#if   __has_include(<IMYTTQ.h>)
#import <IMYTTQ.h>
#endif

#if   __has_include(<IMYMPN/IMYMPN.h>)
#import <IMYMPN/IMYMPN.h>
#endif

#import <IMYReactNative.h>
#import <IMYYunyuChange/IMYYYPregnancyChangeCVC.h>
//#import <IMYYQMotherChangeContainerVC.h>
#import <IMYYQTipDetailControllerV1_3.h>
#import <IMYYQTipModel.h>

@implementation SYPushHandle
+ (void)pushWithType:(SYPushType)type {
    [self pushWithType:type withId:0 text:nil path:nil];
}
+ (void)pushWithType:(SYPushType)type withId:(NSInteger)tid text:(NSString *)text {
    [self pushWithType:type withId:tid text:text path:nil];
}
+ (void)pushWithType:(SYPushType)type withId:(NSInteger)tid text:(NSString *)text path:(IMYClickPath *)path {
    [[LKAppManager sharedAppManager] runBlockInRootIsTabbar:^{
        [self sy_safePushWithType:type withId:tid text:text path:path];
    }];
}

+ (void)sy_safePushWithType:(SYPushType)type withId:(NSInteger)tid text:(NSString *)text path:(IMYClickPath *)path {

    if (path == nil) {
        path = [IMYClickPath pathWithSourceID:0 paramsID:nil];
    }
    if (path.modelID == 0) {
        path.modelID = 1;
    }
    if (path.positionID == 0) {
        path.positionID = 1;
    }

    UIViewController *topVC = [UIViewController imy_currentTopViewController];
    if ([topVC isKindOfClass:[SYPublishDynamicViewController class]]) {
        SYPublishDynamicViewController *publishDynamicViewController = (SYPublishDynamicViewController *)topVC;
        [publishDynamicViewController dismissAndSaveDraft];
    } else if ([topVC isKindOfClass:NSClassFromString(@"MJPhotoBrowser")] || [topVC isKindOfClass:NSClassFromString(@"IMYPhotoBrowser")]) {
        if ([topVC respondsToSelector:@selector(dismiss)]) {
            [topVC performSelectorOnMainThread:@selector(dismiss) withObject:nil waitUntilDone:YES];
        }
    } else if ([topVC.navigationController isKindOfClass:[UIImagePickerController class]]) {
        [topVC dismissViewControllerAnimated:NO completion:nil];
    } else if ([topVC isKindOfClass:NSClassFromString(@"IMYAssetsCollectionViewController")]) {
        [topVC.parentViewController dismissViewControllerAnimated:NO completion:nil];
    }
    
    SYBaseTabBarController * const tabVC = [SYBaseTabBarController shareTabbarController];
    UINavigationController * const currentNav = tabVC.selectedViewController;
    if (!currentNav) {
        return;
    }

    ///如果当前显示的  不是最后一个 vc
    //    UIViewController* currentVisibleVC = currentNav.visibleViewController;
    UIViewController *currentLastVC = currentNav.viewControllers.lastObject;
    if ([topVC isKindOfClass:NSClassFromString(@"IMYNewsPhotosSetViewController")]) {
        currentLastVC = topVC;
    }
    //    if(currentVisibleVC != currentLastVC)
    //    {
    //        return;
    //    }

    //关掉所有的HomeActionSheet
    [[currentNav.view imy_findSubviewWithClass:IMYActionSheet.class] removeFromSuperview];

    if (type == SYPushTypeMyTopics ||
        type == SYPushTypePublishDynamic ||
        type == SYPushTypePublish ||
        type == SYPushTypeCollection ||
        type == SYPushTypeAccountBind ||
        type == SYPushTypeAddress ||
        type == SYPushTypeYoubiDetail ||
        type == SYPushTypeMessageVC ||
        type == SYPushTypeBabyMPNWorks ||
        type == SYPushTypeMyExchange) {
        loginHandle;
    }

    switch (type) {
        case 1: //话题详情
        {
#if   __has_include(<IMYTTQ/TTQTopicDetailViewControllerV2.h>)
            if ([currentNav.topViewController isKindOfClass:[TTQTopicDetailViewControllerV2 class]]) {
                TTQTopicDetailViewControllerV2 *topicDetailViewController = (id)currentNav.topViewController;
                if (topicDetailViewController.viewModel.topic_id == tid) {
                    return;
                }
            }
#endif
            if (tid > 0) {
                //应用未打开时，通过push跳转话题详情，如果有她她圈首页，就需要先跳转至她她圈首页，再进入详情
                IMYAppDelegate *appDelegate = (IMYAppDelegate *)[[UIApplication sharedApplication] delegate];
                IMYSwitchModel *ttqSwitch = [[IMYDoorManager sharedManager] switchForType:@"circle_home_tab"];
                BOOL hasTTQ = (!ttqSwitch || ttqSwitch.data.value == 1 || [IMYPublicAppHelper shareAppHelper].userMode == IMYVKUserModePregnancy);
                if (hasTTQ && appDelegate.appInfo.isFromFinishLaunching) {
                    appDelegate.appInfo.isFromFinishLaunching = NO;
                    if (tabVC.selectedTabIndexType != SYTabBarIndexTypeCircle) {
                        tabVC.selectedTabIndexType = SYTabBarIndexTypeCircle;
                    }
                }
#if   __has_include(<IMYTTQ/TTQTopicDetailViewControllerV2.h>)
                TTQTopicDetailViewControllerV2 *vc = [[TTQTopicDetailViewControllerV2 alloc] initWithViewModel:[[TTQTopicViewModel alloc] initWithTopicID:tid]];
                [currentNav pushViewController:vc animated:YES];
#endif
            }
        } break;
        case 2: //圈子
        {
            if (tid > 0) {
                NSMutableDictionary *params = [@{ @"groupID": @(tid) } mutableCopy];
                if (currentLastVC) {
                    IMYWeakObject *weakObj = [IMYWeakObject weakObject:currentLastVC];
                    [params setObject:weakObj forKey:@"pushBasedViewController"];
                }
                [[IMYURIManager shareURIManager] runActionWithPath:@"circles/group" params:params info:nil];
            }
        } break;
        case 3: {
            //外链
            if (text.length > 0) {
                [[UIApplication sharedApplication] openURL:[NSURL URLWithString:text] options:@{} completionHandler:^(BOOL success) {
                    // ...
                }];
            }
        } break;
        case 4: {
            if (text.length > 0) {
                //  如果是小视频
                if ([text rangeOfString:@"news/short_video"].length) {
                    IMYURI *shortVideoUri = [IMYURI uriWithURIString:text];
                    if (shortVideoUri) {
                        [shortVideoUri appendingParams:@{ @"position": @(8) }];
                        text = shortVideoUri.uri;
                    }
                }
                if (!imy_isBlankString([IMYRNURIRegister coverToRNLink:text])) {
                    [[IMYURIManager shareURIManager] runActionWithPath:@"web" params:@{@"url": text} info:nil];
                } else {
                    SYEasyWebVC *vc = [[SYEasyWebVC alloc] initWithUrlString:text navTitleString:nil];
                    [currentLastVC imy_push:vc];
                }
            }
        } break;
        case 5: {
            //我的柚币
            [currentLastVC imy_push:[NSClassFromString(@"IMYYoubiDetailVC") new] animated:YES];
        } break;
        case 6: {
            //从外部跳转到她她圈的，一律不显示插屏
//            [IMYPublicAppHelper shareAppHelper].isCloseChapingAds = YES;
            [IMYPublicAppHelper shareAppHelper].isCloseHomeChapingAds = YES;
            tabVC.selectedTabIndexType = SYTabBarIndexTypeHome;
            [[tabVC getRootVCWithTabIndexType:SYTabBarIndexTypeHome] popToRootViewControllerAnimated:YES];
        } break;
        case 7: {
            [[IMYURIManager shareURIManager] runActionWithString:@"todaySuggestion"];
        } break;
        case 8: {
            if (tabVC.selectedTabIndexType == SYTabBarIndexTypeRecord) {
                [currentNav popToRootViewControllerAnimated:NO];
            } else {
                tabVC.selectedTabIndexType = SYTabBarIndexTypeRecord;
            }
        } break;
        case 9: {
            [[IMYURIManager shareURIManager] runActionWithString:kURIIMYRecordAnalyze];
        } break;
        case 10: {//月经分析
            [[IMYURIManager shareURIManager] runActionWithString:kURIIMYRecordAnalyzeMenses];
        } break;
        case 11: {//体重分析
            [[IMYURIManager shareURIManager] runActionWithString:kURIIMYRecordAnalyzeWeight];
        } break;
        case 12: { //爱爱分析
            [[IMYURIManager shareURIManager] runActionWithString:kURIIMYRecordAnalyzeOOXX];
        } break;
        case 13: { //体温分析
            [[IMYURIManager shareURIManager] runActionWithString:kURIIMYRecordAnalyzeTemperature];
        } break;
        case 14: {//习惯分析
            [[IMYURIManager shareURIManager] runActionWithString:kURIIMYRecordAnalyzeHabit];
        } break;
        case 15: {//症状分析
            [[IMYURIManager shareURIManager] runActionWithString:kURIIMYRecordAnalyzeSymptom];
        } break;
        case 16: {
            //从外部跳转到她她圈的，一律不显示插屏
//            [IMYPublicAppHelper shareAppHelper].isCloseChapingAds = YES;
            [IMYPublicAppHelper shareAppHelper].isCloseTTQChapingAds = YES;
            tabVC.selectedTabIndexType = SYTabBarIndexTypeCircle;
            [[tabVC getRootVCWithTabIndexType:SYTabBarIndexTypeCircle] popToRootViewControllerAnimated:YES];
        } break;
        case 17: {
            //没有任务了，去掉了
        } break;
        case 18: {
            [[LKAppManager sharedAppManager] runBlockInRootIsTabbar:^{
                [tabVC imy_present:[NSClassFromString(@"SYCustomNotifyVC") new]];
            }];
        } break;
        case 19: {
            //没有订阅贴士了
        } break;
        case 20: {
            if (!HasLogin) {
                // 获取当前VC
                UIViewController *currentVC = [UIViewController imy_currentViewControlloer];
                // present 登录页面
                [[IMYURIManager shareURIManager] runActionWithPath:@"login" params:nil info:nil];
                imy_asyncMainBlock(0.3, ^{
                    SYUserInfoVC_V2 *vc = [[SYUserInfoVC_V2 alloc] init];
                    [currentVC imy_push:vc animated:NO];
                });
            } else {
                SYUserInfoVC_V2 *vc = [[SYUserInfoVC_V2 alloc] init];
                [[UIViewController imy_currentViewControlloer] imy_push:vc animated:YES];
            }
        } break;
        case 21: {
            tabVC.selectedTabIndexType = SYTabBarIndexTypeMine;
            UINavigationController *nav = [tabVC getRootVCWithTabIndexType:SYTabBarIndexTypeMine];
            [nav popToRootViewControllerAnimated:NO];
            //             [attr_id 1->跳到当前身份对应设置页, 2->跳到经期设置页面, 3->跳到备孕设置页面, 4->跳到怀孕设置页面, 5->跳到辣妈设置页面]
            imy_asyncMainBlock(0.2, ^{
                id vc = [NSClassFromString(@"SYModeDetailViewController") new];
                [nav pushViewController:vc animated:YES];
            });
        } break;
        case 22: {
            [[IMYURIManager shareURIManager] runActionWithPath:@"user/address" params:@{ @"callFromNative": @(YES) } info:nil];
        } break;
        case 23: {
            [[IMYURIManager shareURIManager] runActionWithString:kURIIMYRecordMoodDiaryList];
        } break;
        case 24: {
            // 跳新版的收藏
            [[IMYURIManager shareURIManager] runActionWithPath:@"circles/collect" params:nil info:nil];
        } break;
        case 25: {
#if   __has_include(<IMYTTQ/TTQMyTopicVC.h>)
            [currentLastVC imy_push:[[TTQMyTopicVC alloc] initWithViewModel:[TTQMyTopicVM new]] animated:YES];
#endif
        } break;
        case 26: {
        } break;
        case 27: {
            [SYPublicFun setIsFeekback:NO];
            [[NSNotificationCenter defaultCenter] postNotificationName:SYBaseTabBarFB object:nil];
            [currentLastVC imy_push:[NSClassFromString(@"IMYFeedbackVC") new] animated:YES];
        } break;
        case 29: {
            [currentLastVC imy_push:[NSClassFromString(@"IMYAccountSecureVC") new] animated:YES];
        } break;
        case 30: {
            loginHandle;
            [[IMYURIManager shareURIManager] runActionWithPath:@"dynamic/homePage" params:@{ @"userId": @(tid) } info:nil];
        } break;
        case 31: //SYPushTypeAddForcus
        {
            loginHandle;
            [currentLastVC imy_push:[NSClassFromString(@"SYDynamicAddMainVC") new] animated:YES];
        } break;
        case 32: {
            [[LKAppManager sharedAppManager] runBlockInRootIsTabbar:^{
                [tabVC imy_present:[NSClassFromString(@"SYPublishDynamicViewController") new]];
            }];
        } break;
        case 33: {
            //有id跳对应主题预览

            if (tid > 0) {
                if ([topVC isKindOfClass:[SYThemePreviewViewController class]]) {
                    SYThemePreviewViewController *vc = (SYThemePreviewViewController *)topVC;
                    if (vc.themeID != tid) {
                        SYThemePreviewViewController *vc = [[SYThemePreviewViewController alloc] initWithThemeID:tid];
                        [currentLastVC imy_push:vc];
                    }
                } else {
                    SYThemePreviewViewController *vc = [[SYThemePreviewViewController alloc] initWithThemeID:tid];
                    [currentLastVC imy_push:vc];
                }
            } else {
                if (![topVC isKindOfClass:NSClassFromString(@"SYThemeGroupVC")] || ![topVC isKindOfClass:NSClassFromString(@"IMYFlutterWrapperViewController")]) {
                    [[IMYURIManager shareURIManager] runActionWithPath:@"theme" params:nil info:nil];
                }
            }
        } break;
        case 34: {
            //产检
            //            [currentLastVC push:[NSClassFromString(@"SYAntenatalCareVC") new] animated:YES];
        } break;
        case 35: {
            ///跳转到签到页
            [[IMYURIManager shareURIManager] runActionWithPath:@"sale/sign" params:@{ @"mobPath": @[path] } info:nil];
        } break;
        case 36: {
            //更多圈子
            tabVC.selectedTabIndexType = SYTabBarIndexTypeCircle;
#if   __has_include(<IMYTTQ/TTQHomeController.h>)
            [TTQHomeController takeMoreForums:[SYBaseTabBarController shareTabbarController].viewControllers[2]];
#endif
        } break;
        case 37: {
            //搜索
            [[IMYURIManager shareURIManager] runActionWithString:@"circles/search"];
        } break;
#if   __has_include(<IMYTTQ/TTQForumHelper.h>)
        case 38: {
            //发话题
            NSInteger groupid = tid;
            if (groupid > 0) {
                NSTimeInterval duration = [[NSDate date] timeIntervalSince1970];
                [[[TTQForumHelper updateForumByID:groupid] deliverOnMainThread] subscribeNext:^(TTQForumModel *groupModel) {
                    NSTimeInterval timeDiff = [[NSDate date] timeIntervalSince1970] - duration;
                    BOOL isEqual = [currentLastVC isEqual:[SYBaseTabBarController shareTabbarController].selectedViewController];
                    if (groupModel.is_joined && isEqual && (timeDiff <= 3)) {
                        [[IMYURIManager shareURIManager] runActionWithPath:@"circles/publish" params:@{ @"forum_id": @(groupid) } info:nil];
                    }
                }];
            }
        } break;
#endif
        case 39: {
            //设置
            [[IMYURIManager shareURIManager] runActionWithPath:@"setting" params:nil info:nil];
        } break;
        case 40: {
#if   __has_include(<IMY_EBusiness/IMYYoubiDetailVC.h>)
            IMYYoubiDetailVC *vc = [[IMYYoubiDetailVC alloc] init];
            [currentLastVC imy_push:vc animated:YES];
#endif
        } break;
        case 41: {
            [currentLastVC imy_push:[[NSClassFromString(@"SYSecretSwitchVC") alloc] init]];
        } break;
        case 42: {
            IMYPublicBaseViewController *vc = [NSClassFromString(@"SYPushSettingVC") new];
            [currentLastVC imy_push:vc];
        } break;
        case 43: {
            [currentLastVC imy_push:[NSClassFromString(@"SYSetPrivacyVC") new]];
        } break;
        case 44: {
            [currentLastVC imy_push:[NSClassFromString(@"SYMyNotifyViewController") new]];
        } break;
        case 45: {
            SYEasyWebVC *webVC = [[SYEasyWebVC alloc] initWithUrlString:WebURL小工具首页 navTitleString:IMYString(@"小工具")];
            [currentLastVC imy_push:webVC];
        } break;
        case 46: {
        } break;
        case SYPushTypeDynamicList: {
            loginHandle;
//            [currentLastVC imy_push:[NSClassFromString(@"IMYMyDynamicViewController") new]];
        } break;
        case SYPushTypeMessageVC:
        case SYPushTypeMessage: {
            [[IMYURIManager shareURIManager] runActionWithString:@"msg/entrance"];
        } break;
        case SYPushTypeMyExchange: {
#if   __has_include(<IMY_EBusiness/IMYYoubiDetailVC.h>)
            IMYYoubiDetailVC *vc = [[IMYYoubiDetailVC alloc] init];
            [currentLastVC imy_push:vc animated:YES];
#endif
        } break;
        case SYPushTypeIM: {
            [[IMYURIManager shareURIManager] runActionWithString:@"msg/entrance"];
        } break;
        case SYPushTypeEBSelling: {
            ///跳转到特卖页
            [[IMYURIManager shareURIManager] runActionWithPath:@"sale/home"
                                                        params:@{ @"gotoShowID": @(tid),
                                                                  @"gotoShowType": @(text.integerValue),
                                                                  @"mobPath": @[path] }
                                                          info:nil];
            break;
        }
#if   __has_include(<IMY_EBusiness/IMYEBBrandItemModel.h>)
        case SYPushTypeActiveMall: {
            IMYEBBrandItemModel *virtualModel = [[IMYEBBrandItemModel alloc] init];
            virtualModel.link_type = IMYEBJumpTypeSession;
            if (tid > 0) {
                virtualModel.link_value = @(tid).stringValue;
            } else {
                virtualModel.link_value = text;
            }
            [IMYEBJumpManager jumptoPageWithLinkModel:virtualModel];
            break;
        }
#endif
        case SYPushTypeYoubiGoods: {
            //            IMYEBBrandItemModel* virtualModel = [[IMYEBBrandItemModel alloc] init];
            //            virtualModel.link_type = IMYEBJumpTypeYouBi;
            //            virtualModel.brand_area_id = tid;
            //            [IMYEBJumpManager jumptoPageWithLinkModel:virtualModel mobPath:@[path]];
            [[IMYURIManager shareURIManager] runActionWithPath:@"youbi/session"
                                                        params:@{ @"brand_area_id": @(tid),
                                                                  @"mobPath": @[path] }
                                                          info:nil];
            break;
        }
        case SYPushTypeEBDetail: {
#if   __has_include(<IMY_EBusiness/IMYEBBrandItemModel.h>)
            IMYEBBrandItemModel *virtualModel = [[IMYEBBrandItemModel alloc] init];
            virtualModel.link_type = IMYEBJumpTypeProductWebVC;
            virtualModel.link_value = text;
            [IMYEBJumpManager jumptoPageWithLinkModel:virtualModel];
#endif
            break;
        }
        case SYPushTypeUseYoubi: {
            [[IMYURIManager shareURIManager] runActionWithPath:@"youbi" params:nil info:nil];

            break;
        }
        case SYPushTypeTTQSpecial: {
            NSInteger category = 0;
            if (text.length) {
                category = text.integerValue;
            }
            [[IMYURIManager shareURIManager] runActionWithPath:@"news/special"
                                                        params:@{ @"specialid": @(tid),
                                                                  @"categoryId": @(category) }
                                                          info:nil];
            //            TTQSpecialTopicViewModel *vm = [TTQSpecialTopicViewModel initWithSpecial:tid category:category];
            //            [currentNav imy_push:[[TTQSpecialTopicViewController alloc] initWithViewModel:vm]];
            break;
        }

        case SYPushTypeThemeActive: {
            if (tid > 0) {
                id vc = [NSClassFromString(@"SYThemesViewController") new];
                [vc setValue:@(tid) forKey:@"keyNum"];
                [vc setValue:@(3) forKey:@"gbpType"];
                [vc setValue:text forKey:@"navTitle"];
                [currentNav pushViewController:vc animated:YES];
            } else {
                if (![topVC isKindOfClass:NSClassFromString(@"SYThemeGroupVC")] || ![topVC isKindOfClass:NSClassFromString(@"IMYFlutterWrapperViewController")]) {
                    [[IMYURIManager shareURIManager] runActionWithPath:@"theme" params:@{@"initIndex":@(1)} info:nil];
                }
            }
            break;
        }
        case SYPushTypeThemeCategory: {
            if (tid > 0) {
                id vc = [NSClassFromString(@"SYThemesViewController") new];
                [vc setValue:@(tid) forKey:@"keyNum"];
                [vc setValue:@(2) forKey:@"gbpType"];
                [vc setValue:text forKey:@"navTitle"];
                [currentNav pushViewController:vc animated:YES];
            } else {
                if (![topVC isKindOfClass:NSClassFromString(@"SYThemeGroupVC")] || ![topVC isKindOfClass:NSClassFromString(@"IMYFlutterWrapperViewController")]) {
                    [[IMYURIManager shareURIManager] runActionWithPath:@"theme" params:@{@"initIndex":@(1)} info:nil];
                }
            }
            break;
        }
        case SYPushTypeEBHome: {
            [[IMYURIManager shareURIManager] runActionWithPath:@"sale"
                                                        params:@{ @"gotoShowID": @(tid),
                                                                  @"gotoShowType": @(text.integerValue),
                                                                  @"mobPath": @[path] }
                                                          info:nil];
            break;
        }
        case SYPushTypeEBHomeSigning: {
            [[IMYURIManager shareURIManager] runActionWithPath:@"sale/sign"
                                                        params:@{ @"gotoShowID": @(tid),
                                                                  @"gotoShowType": @(text.integerValue),
                                                                  @"mobPath": @[path] }
                                                          info:nil];
            break;
        }
#if   __has_include(<IMY_EBusiness/IMYEBBrandItemModel.h>)
        case SYPushTypeEBSession: {
            IMYEBBrandItemModel *virtualModel = [[IMYEBBrandItemModel alloc] init];
            virtualModel.link_type = IMYEBJumpTypeSession;
            if (tid > 0) {
                virtualModel.link_value = @(tid).stringValue;
            } else {
                virtualModel.link_value = text;
            }
            [IMYEBJumpManager jumptoPageWithLinkModel:virtualModel];
            break;
        }
        case SYPushTypeEBActivity: {
            IMYEBBrandItemModel *virtualModel = [[IMYEBBrandItemModel alloc] init];
            virtualModel.link_type = IMYEBJumpTypeActivity;
            if (tid > 0) {
                virtualModel.link_value = @(tid).stringValue;
            } else {
                virtualModel.link_value = text;
            }
            [IMYEBJumpManager jumptoPageWithLinkModel:virtualModel];
            break;
        }
        case SYPushTypeEBCategory: {
            IMYEBBrandItemModel *virtualModel = [[IMYEBBrandItemModel alloc] init];
            virtualModel.link_type = IMYEBJumpTypeCategory;
            if (tid > 0) {
                virtualModel.link_value = @(tid).stringValue;
            } else {
                virtualModel.link_value = text;
            }
            [IMYEBJumpManager jumptoPageWithLinkModel:virtualModel];
            break;
        }
        case SYPushTypeEBCustomURL: {
            IMYEBBrandItemModel *virtualModel = [[IMYEBBrandItemModel alloc] init];
            virtualModel.link_type = IMYEBJumpTypeProductWebVC;
            virtualModel.link_value = text;
            [IMYEBJumpManager jumptoPageWithLinkModel:virtualModel];
            break;
        }
        case SYPushTypeEBProductSKU: {
            IMYEBBrandItemModel *virtualModel = [[IMYEBBrandItemModel alloc] init];
            virtualModel.link_type = IMYEBJumpTypeProductDetail;
            virtualModel.redirect_type = IMYEBRedirectTypeTAENativeApi;
            virtualModel.link_value = text;
            [IMYEBJumpManager jumptoPageWithLinkModel:virtualModel];
            break;
        }
        case SYPushTypeEBFeatures: {
            break;
        }
        case SYPushTypeEBTaobaoURL: {
            IMYEBBrandItemModel *virtualModel = [[IMYEBBrandItemModel alloc] init];
            virtualModel.link_type = IMYEBJumpTypeTaobaoWebVC;
            virtualModel.link_value = text;
            [IMYEBJumpManager jumptoPageWithLinkModel:virtualModel];
            break;
        }
        case SYPushTypeEBTmallProductSKU: {
            IMYEBBrandItemModel *virtualModel = [[IMYEBBrandItemModel alloc] init];
            virtualModel.link_type = IMYEBJumpTypeProductDetail;
            virtualModel.redirect_type = IMYEBRedirectTypeTAENativeApi;
            virtualModel.link_value = text;
            virtualModel.shop_type = 2;
            [IMYEBJumpManager jumptoPageWithLinkModel:virtualModel];
            break;
        }
        case SYPushTypeEBMyFavor: {
            IMYEBBrandItemModel *virtualModel = [[IMYEBBrandItemModel alloc] init];
            virtualModel.link_type = IMYEBJumpTypeMyFavor;
            [IMYEBJumpManager jumptoPageWithLinkModel:virtualModel];
            break;
        }
#endif
        case SYPushTypeEmotionList: {
            //            [[IMYJumpManager shareJumpManager] jumptoPageWithType:IMYIJumpTypeEmoticonList currentVC:currentNav params:nil map:IMYIndividuationJumpMapKey];
            break;
        }
        case SYPushTypeEmotionDetail: {
            //            [[IMYJumpManager shareJumpManager] jumptoPageWithType:IMYIJumpTypeEmoticonDetail currentVC:currentNav params:@{@"emoticonID": @(tid)} map:IMYIndividuationJumpMapKey];
            break;
        }
//        case SYPushTypeSubjectDetail: {
//            [[IMYJumpManager shareJumpManager] jumptoPageWithType:IMYIJumpTypeSubjectDetail currentVC:currentNav params:@{ @"subjectID": @(tid) } map:IMYIndividuationJumpMapKey];
//            break;
//        }
//        case SYPushTypeSubjectList: {
//            [[IMYJumpManager shareJumpManager] jumptoPageWithType:IMYIJumpTypeSubjectList currentVC:currentNav params:nil map:IMYIndividuationJumpMapKey];
//            break;
//        }

        case TTQPushTypeHelpZone: {
            [[IMYURIManager shareURIManager] runActionWithURI:[IMYURI uriWithPath:@"circles/helpzone"
                                                                           params:@{ @"forum_id": @(tid),
                                                                                     @"mobPath": @[path] }
                                                                             info:nil]];
            break;
        }
        case TTQPushTypeHonorHall: {
            /// 已经没有原生达人堂业务了，现在走h5
            break;
        }
        case SYPushTypeFriendList: {
            loginHandle;
            SYDynamicUserListVC *vc = [[SYDynamicUserListVC alloc] init];
            vc.isMyUserInfo = YES;
            [currentNav imy_push:vc];
            break;
        }
        case SYPushTypeHelpAndReportList: {
            [[IMYURIManager shareURIManager] runActionWithPath:@"helper" params:nil info:nil];
            break;
        }
        case SYPushTypeMyTheme: {
            SYMyThemesViewController *vc = [SYMyThemesViewController new];
            [currentNav imy_push:vc];
            break;
        }
        case SYPushTypeChangePassword: {
            loginHandle;
            if ([IMYAccount defaultAccount].bind.isBindMobile || [IMYAccount defaultAccount].bind.isBindEmail) {
                IMYAccountChangePasswordVC *vc = [[IMYAccountChangePasswordVC alloc] init];
                vc.completion = ^(BOOL success, NSDictionary *payload) {
                    if (success) {
                        [UIAlertController imy_showAlertViewWithTitle:IMYString(@"提示")
                                                        message:IMYString(@"成功修改密码")
                                              cancelButtonTitle:nil
                                              otherButtonTitles:@[IMYString(@"确定")]
                                                        handler:^(UIAlertController *alertController, NSInteger buttonIndex) {
                                                            [currentNav imy_pop:YES];
                                                        }];
                    }
                };
                [currentNav imy_push:vc];
            } else {
                [UIAlertController imy_showAlertViewWithTitle:IMYString(@"请先绑定手机号码哦~") message:nil cancelButtonTitle:IMYString(@"好的") otherButtonTitles:nil handler:nil];
            }

            break;
        }
#if   __has_include(<IMYEBPublic/IMYEBSwitchModel.h>)
        case SYPushTypeMyOrder: {
            IMYEBSwitchModel *switchModel = [[IMYEBDoorManager sharedManager] ebSwitchForType:@"kepler_switch"];
            //            NSString *taeTitle = model.title;
            //            NSDictionary *params = [NSDictionary dictionaryWithObject:taeTitle forKey:@"taeTitle"];
            if (switchModel && switchModel.status) {
                //            [[IMYURIManager shareURIManager] runActionWithString:@"my/orderSelect"];
                [[IMYURIManager shareURIManager] runActionWithPath:@"my/orderSelect" params:@{ @"mobPath": @[path] } info:nil];
            } else {
                [[IMYURIManager shareURIManager] runActionWithPath:@"my/order" params:@{ @"mobPath": @[path] } info:nil];
            }
        } break;
#endif
        case SYPushTypeYoubiCommodity:
            //产品确认，93没使用了
            break;
        case SYPushTypeAppstore:
            [[IMYPublicAppHelper shareAppHelper] goRating];
            break;
        case SYPushTypeBaoBaoFaYu: {
            IMYPregnanceModel *model = [IMYPregnanceModel getLastPregnancyModel];
            NSInteger daydiff = [model.startDate getDayDiff:[NSDate imy_today]];
            CGFloat week = daydiff / 7.0;
            NSMutableDictionary *params = [NSMutableDictionary new];
            [params imy_setNonNilObject:@(2) forKey:@"location"];
            [params imy_setNonNilObject:@(week) forKey:@"week"];
            [params imy_setNonNilObject:@(daydiff) forKey:@"day"];
            IMYYYPregnancyChangeCVC *vc = [IMYYYPregnancyChangeCVC new];
            [vc imy_setPropertyWithDictionary:params];
            [currentNav imy_push:vc];
        } break;
        case SYPushTypeMaMaBianHua: {
            IMYPregnanceModel *model = [IMYPregnanceModel getLastPregnancyModel];
            NSInteger daydiff = [model.startDate getDayDiff:[NSDate imy_today]];
            CGFloat week = daydiff / 7.0;
            NSMutableDictionary *params = [NSMutableDictionary new];
            [params imy_setNonNilObject:@(3) forKey:@"location"];
            [params imy_setNonNilObject:@(week) forKey:@"week"];
            [params imy_setNonNilObject:@(daydiff) forKey:@"day"];
            IMYYYPregnancyChangeCVC *vc = [IMYYYPregnancyChangeCVC new];
            [vc imy_setPropertyWithDictionary:params];
            [currentNav imy_push:vc];
        } break;
        case SYPushTypeMyNickName:
            [currentNav imy_push:[NSClassFromString(@"SYEditNicknameVC") new]];
            break;
        case SYPushTypeAccountSecurity:
            [currentNav imy_push:[NSClassFromString(@"IMYAccountSecureVC") new]];
            break;
        case SYPushTypeMarrageStatus: {
            __block NSArray *array = @[IMYString(@"未婚"), IMYString(@"已婚")];
            NSUInteger index = [SYUserHelper sharedHelper].bMarry ? 1 : 0;
            SYSingleSelectViewController *con = [[SYSingleSelectViewController alloc] initWithItems:array atIndex:index];
            con.navigationItem.title = IMYString(@"婚姻状况");
            [con setSelectedBlock:^(NSUInteger blockIndex) {
                [SYUserHelper sharedHelper].bMarry = blockIndex == 0 ? NO : YES;
                [[SYUserHelper sharedHelper] saveToDB];
            }];
            [currentNav imy_push:con];
        } break;
        case SYPushTypePickCity: {
            IMYCityPickVC *vc = [[IMYCityPickVC alloc] init];
            [vc setCompletion:^(NSString *cityOrCountry, NSInteger code) {
                //保存数据
                [SYUserHelper sharedHelper].city = cityOrCountry;
                [[SYUserHelper sharedHelper] saveToDB];
                //同步数据
                [IMYPregnanceModel refreshOtherViews];
                [SYPublicFun uploadUserInfoData];
            }];
            [currentNav imy_push:vc];
        } break;
        case SYPushTypeModeChange:
            [currentNav imy_push:[NSClassFromString(@"SYUserModeSelectViewController") new]];
            break;
        case SYPushTypeYouYouTip: {
            SYEasyWebVC *webVC = [SYEasyWebVC vcWithUrlString:WebURL温馨提示 navTitleString:IMYString(@"柚柚温馨提醒")];
            [currentNav imy_push:webVC];
        } break;
        case SYPushTypeMore_Commmon:
            [currentNav imy_push:[NSClassFromString(@"SYAllUseVC") new]];
            break;
        case SYPushTypeYouzijieHelp:
            [[IMYURIManager shareURIManager] runActionWithPath:@"helper" params:nil info:nil];
            //            [[IMYURIManager shareURIManager] runActionWithString:@"helper/youzijie"];
            break;
        case SYPushTypeMore_AboutMeetyou:
            [currentNav imy_push:[NSClassFromString(@"SYAboutViewCtl") new]];
            break;
        case SYPushTypeDataSet:
            if ([IMYPublicAppHelper shareAppHelper].userMode == IMYVKUserModePregnancy) {
                [currentNav imy_push:[NSClassFromString(@"SYModeConfirmViewController") new]];
            } else {
                [currentNav imy_push:[NSClassFromString(@"SYModeDetailViewController") new]];
            }
            break;
        case SYPushTypeTipsDetail: {
            IMYYQTipModel *model = [IMYYQTipModel new];
            model.tip_id = tid;
            model.tip_url = text;
            IMYYQTipDetailControllerV1_3 *vc = [[IMYYQTipDetailControllerV1_3 alloc] init];
            vc.modelArray = @[model];
            vc.currentIndex = 0;
            vc.userMode = 0;
            vc.usingWKWebView = YES;

            UIViewController *lastVC = [UIViewController imy_currentTopViewController];
            [lastVC imy_push:vc animated:YES];
            break;
        }
        case SYPushTypeEBBrand: {
            NSMutableDictionary *userInfo = [NSMutableDictionary dictionary];
            userInfo[@"type"] = text;
            [[IMYURIManager shareURIManager] runActionWithPath:@"brand" params:userInfo info:nil];
            break;
        }
        case SYPushTypeMenseKnowledge: {
            if (tid && text) {
                NSDictionary *params = @{ @"categoryID": @(tid),
                                          @"title": text,
                                          @"userMode": @(0) };
                [[IMYURIManager shareURIManager] runActionWithPath:@"yunqi/tip/category" params:params info:nil];
            } else {
                [[IMYURIManager shareURIManager] runActionWithPath:@"yunqi/tip/menstruationKnowledge" params:nil info:nil];
            }
            break;
        }
        case SYPushTypeTodayAdvice: {
            [[IMYURIManager shareURIManager] runActionWithString:@"todaySuggestion"];
            break;
        }
        case SYPushTypeOvulation: {
            [[IMYURIManager shareURIManager] runActionWithString:@"record/ovulate"];
            break;
        }
        case SYPushTypeDiary: {
            [[IMYURIManager shareURIManager] runActionWithString:@"record/moodDiary"];
            break;
        }
        case SYPushTypeDadu: {
            [[IMYURIManager shareURIManager] runActionWithString:@"record/dadu/home"];
            break;
        }
        case SYPushTypeProductionInspection: {
            [[IMYURIManager shareURIManager] runActionWithString:@"record/chanjian"];
            break;
        }
        case SYPushTypeBUltrasonic: {
            [[IMYURIManager shareURIManager] runActionWithString:@"tools/buExplain"];
            break;
        }
        case SYPushTypeCountFetalMovement: {
            [[IMYURIManager shareURIManager] runActionWithString:@"record/taidong"];
            break;
        }
        case SYPushTypeBuRu: {
            [[IMYURIManager shareURIManager] runActionWithString:@"record/buru/home"];
            break;
        }
        case SYPushTypeChouChou: {
            [[IMYURIManager shareURIManager] runActionWithString:@"record/chouchou/home"];
            break;
        }
        case SYPushTypeGrowthCurve: {
            [[IMYURIManager shareURIManager] runActionWithString:@"record/bgc/home"];
            break;
        }
        case SYPushTypeBabyUncomfortable: {
            [[IMYURIManager shareURIManager] runActionWithString:@"record/babySymptom"];
            break;
        }
        case SYPushTypeBuRuAnalyze: {
            [[IMYURIManager shareURIManager] runActionWithString:@"record/analyze/buru"];
            break;
        }
        case SYPushTypeChouChouAnalyze: {
            [[IMYURIManager shareURIManager] runActionWithString:@"record/analyze/chouchou"];
            break;
        }
        case SYPushTypeGrowthCurveAnalyze: {
            [[IMYURIManager shareURIManager] runActionWithString:@"record/analyze/bgc"];
            break;
        }
        case SYPushTypeBabyUncomfortableAnalyze: {
            [[IMYURIManager shareURIManager] runActionWithString:@"record/analyze/babySymptom"];
            break;
        }
        case SYPushTypeBabyMusic: {
            [[IMYURIManager shareURIManager] runActionWithString:@"yunqi/music"];
            break;
        }
        case SYPushTypeBabyMPNWorks: {
#if   __has_include(<IMYMPN/IMYMPN.h>)
            [[IMYURIManager shareURIManager] runActionWithString:kURIMPNDynamicList];
#endif
        } break;
        case SYPushTypeWebCool: {
            [[IMYURIManager shareURIManager] runActionWithPath:@"web/cool"
                                                        params:@{ @"url": text,
                                                                  @"navbarIsHide": @(1) }
                                                          info:nil];
        }

        default: {
            if (imy_isNotBlankString(text)) {
                [[IMYURIManager shareURIManager] runActionWithString:text];
            }
        }
            break;
    }
}

@end
