Pod::Spec.new do |s|
	s.name = 'IMYAnswer'
	s.version = '********'
	s.description = 'IMYAnswer'
	s.license = 'MIT'
	s.summary = 'IMYAnswer'
	s.homepage = 'https://www.meiyou.com'
	s.authors = { '' => '' }
	
	s.source = { :git => '*********************:iOS/IMYAnswer.git', :branch => 'release-8.96.0' }
	s.requires_arc = true
	s.ios.deployment_target = '11.0'
	s.source_files = 'IMYAnswer/Source/**/*.{h,m}'
	s.resources = 'IMYAnswer/Source/**/*.{db,xib,json,png,jpg,gif,js,plist,bundle,txt}','IMYAnswer/Bundles/*.{bundle,xcassets}'

	s.dependency 'IMYBaseKit'
	s.dependency 'TaeSDK/Core'
	s.dependency 'IMYAccount'
	s.dependency 'MXParallaxHeader'
	s.dependency 'IMYTTQ'
	s.dependency 'IMYLaunchController'
	s.dependency 'IMYReactNative'
	s.dependency 'IMYAdvertisement'
	s.dependency 'IMYVideoPlayer'
  	
end
