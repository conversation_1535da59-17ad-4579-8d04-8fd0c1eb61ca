//
//  IMYQAPublishPhotoView.m
//  IMYAnswer
//	❤️ 🧡 💛 💚 💙 💜 🖤 💔 ❣️ 💕 💞 💓 💗 💖 💘 💝 
//  Created by 🤩🤪👉黄训瑜👈💊💔💯 on 2019/9/6
//	❤️ 🧡 💛 💚 💙 💜 🖤 💔 ❣️ 💕 💞 💓 💗 💖 💘 💝 
//  Copyright © 2019年 Meetyou. All rights reserved.
//

#import "IMYQAPublishPhotoView.h"
#import <IMYBaseKit/IMY_ViewKit.h>
#import <IMYTTQ/TTQCameraButton.h>
#import <IMYTTQ/UIImage+TTQ.h>
#import <IMYTTQ/UIViewController+TTQ.h>
#import <AVFoundation/AVFoundation.h>
#import <IMYTTQ/TTQABTestConfig.h>
#import <UIImage+GIF.h>
#import <IMYTTQ/TTQCommonHelp.h>
#import "IMYQACameraButton.h"

#define IMG_W_H (SCREEN_WIDTH - 36) / 4
@interface IMYQAPublishPhotoView () <
    UINavigationControllerDelegate,
    UIImagePickerControllerDelegate,
    IMYAssetPickerControllerDelegate
>//newalbum
@property (nonatomic, assign) NSUInteger lastAssetSelectedIndex;
@property (nonatomic, strong) TTQCameraButton *activeCameraButton;
@property (nonatomic, assign) int cameraButtonTag;
@end

@implementation IMYQAPublishPhotoView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self commonInit];
    }
    return self;
}
- (instancetype)initWithCoder:(NSCoder *)coder {
    self = [super initWithCoder:coder];
    if (self) {
        [self commonInit];
    }
    return self;
}
- (void)commonInit {
    self.backgroundColor = IMY_COLOR_KEY(kISY_White);
    self.cameraButtonTag = 111;
    self.cameraButtons = [NSMutableArray array];
    self.selectedArray = [NSMutableArray arrayWithCapacity:10];//newalbum
}

+ (IMYQAPublishPhotoView *)viewWithLimit:(NSUInteger)count controller:controller {
    IMYQAPublishPhotoView *view = [[IMYQAPublishPhotoView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, IMG_W_H + 10)];
    view.limit = count;
    view.controller = controller;
    return view;
}
//预加载图片
- (void)startWithPhotos:(NSArray *)images urls:(NSArray<NSString *> *)urls {
    NSMutableArray *array = [NSMutableArray new];
    for (TTQCameraButton *btn in self.cameraButtons) {
        if ([btn.assetURL.absoluteString hasPrefix:@"http"]) {
            [array addObject:btn];
        } else {
            [btn removeFromSuperview];
        }
    }
    [self.cameraButtons removeAllObjects];
    [self.cameraButtons addObjectsFromArray:array];
    NSUInteger count = MAX(images.count, urls.count);
    for (int i = 0; i < count && i < _limit; i++) {
        IMYQACameraButton *cameraButton = [[IMYQACameraButton alloc] initWithFrame:CGRectMake(15 + (2 + IMG_W_H) * (i % 4), 5 + (2 + IMG_W_H) * (i / 4), IMG_W_H, IMG_W_H)];
        cameraButton.assetURL = [NSURL URLWithString:urls[i]];
        [cameraButton.deleteButton addTarget:self action:@selector(removeObjectFromCameraButtons:) forControlEvents:UIControlEventTouchUpInside];
        UIImage *image = images[i];
        if (image) {
            cameraButton.originalImage = image;
        } else {
            [cameraButton setupOriginalImage];
        }
        cameraButton.tag = self.cameraButtonTag;
        [cameraButton addTarget:self action:@selector(handleCameraButtonTouchUpInside:) forControlEvents:UIControlEventTouchUpInside];
        [self.cameraButtons addObject:cameraButton];
    }
    [self layoutViews];
}

- (void)layoutViews {
    [self.cameraButtons enumerateObjectsUsingBlock:^(TTQCameraButton *cameraButton, NSUInteger idx, BOOL *stop) {
        cameraButton.imy_origin = CGPointMake(15 + (2 + IMG_W_H) * (idx % 4), 5 + (2 + IMG_W_H) * (idx / 4));
        [self addSubview:cameraButton];
    }];
    TTQCameraButton *lastButton = self.cameraButtons.lastObject;
    CGFloat originX = self.cameraButtons.count % 4 == 0 ? 15 : lastButton.imy_right + 2;
    CGFloat originY = self.cameraButtons.count % 4 == 0 ? lastButton.imy_bottom + 2 : lastButton.imy_origin.y;
    if (lastButton == nil) {
        originY = 5;
    }
    self.imy_height = lastButton.imy_bottom;

    if (self.imageChangeBlock) {
        self.imageChangeBlock();
    }
}

- (void)handleCameraButtonTouchUpInside:(id)sender {
    if (self.touchBlock) {
        self.touchBlock(sender);
    }
    self.activeCameraButton = sender;
    if (self.activeCameraButton.thumbImage) {
        //大图
        [self showOriginalImage];
    } else {
        if (_cameraButtons.count > self.limit) {
            [UIWindow imy_showTextHUD:IMYString(@"只能添加%d张图片哦~"), self.limit];
            return;
        }
        [IMYEventHelper event:@"fb-tjtp"];
        [self takeImage];
    }
}

- (void)showOriginalImage {
    @weakify(self);
    NSMutableArray *photos = [[NSMutableArray alloc] init];
    NSMutableArray *images = [NSMutableArray array];
    [_cameraButtons bk_each:^(TTQCameraButton *sender) {
        if (sender.thumbImage) {
            IMYPhoto *photo = [[IMYPhoto alloc] init];
            if (sender.originalImage.size.height > 0) {
                photo.image = sender.originalImage;
                [images addObject:sender.originalImage];
            } else {
                photo.image = sender.thumbImage;
                [images addObject:sender.thumbImage];
            }
            photo.disableAnimation = YES;
            [photos addObject:photo];
        }
    }];
    
    IMYPhotoBrowser *browser = [[IMYPhotoBrowser alloc] init];
    browser.showType = IMYBrowserTypePublish;
    browser.bMultipleToolbar = YES;
    browser.photos = photos;
    browser.currentPhotoIndex = [_cameraButtons indexOfObject:self.activeCameraButton];
    [browser setDeleteBlock:^(NSUInteger index) {
        @strongify(self);
        TTQCameraButton *targetButton = self.cameraButtons[index];
        [targetButton removeFromSuperview];
        [self.cameraButtons removeObjectAtIndex:index];
        if (self.selectedArray.count) {
            [self.selectedArray removeObjectAtIndex:index];
        }
        [self layoutViews];
    }];
    // 浏览照片点击返回
    [browser setCompleteBlock:^{
        @strongify(self);
        if (self.imageClickBlock) {
            self.imageClickBlock(NO);
        }
    }];
    if (self.imageClickBlock) {
        self.imageClickBlock(YES);
    }
    [[UIViewController imy_currentViewControlloer] imy_present:browser animated:YES];
}

- (void)removeObjectFromCameraButtons:(UIButton *)sender {
    IMYQACameraButton *object = sender.superview;
    NSInteger index = [self.cameraButtons indexOfObject:object];
    [object removeFromSuperview];
    [self.cameraButtons removeObject:object];
    if (self.selectedArray.count) {
        [self.selectedArray removeObjectAtIndex:index];
    }
    [self layoutViews];
}

- (void)takeImageEventHelper {
    if (self.relay) {
        [IMYEventHelper event:@"hf-tjtp"];
    } else {
        [IMYEventHelper event:@"b-tjtp"];
    }
}

- (void)takeImageFromPregnancyAlbum {
    [self takeImageEventHelper];
    @weakify(self);
    [[IMYURIManager shareURIManager]
     runActionWithURI:[IMYURI uriWithPath:@"tools/childbirthBagList"
                                   params:@{@"selectCallback":^(NSDictionary *dic){
         NSArray *draft = dic[@"draft"];
         if ([draft isKindOfClass:[NSArray class]]) {
             NSDictionary *firstDraft = draft.firstObject;
             if ([firstDraft isKindOfClass:[NSDictionary class]]) {
                 @strongify(self);
                 NSString *str = firstDraft[@"content"];
                 self.activeCameraButton.content = str;
                 NSArray *urls = firstDraft[@"urls"];
                 if (urls.count > 0) {
                     self.activeCameraButton.assetURL = [NSURL URLWithString:urls[0]];
                     [self.activeCameraButton setupOriginalImage];
                     self.activeCameraButton.tag = self.cameraButtonTag;
                     [self.cameraButtons addObject:self.activeCameraButton];
                     [self layoutViews];
                 }
             }
             
         }
     }
                                            }
                                     info:nil]];
}

- (void)takeImageFromAlbum {
    [self takeImageEventHelper];
    if (self.relay) {
        [IMYEventHelper event:@"xtxcdy" attributes:@{@"来源": @"回复"}];
    } else {
        [IMYEventHelper event:@"xtxcdy" attributes:@{@"来源": @"其他"}];
    }
    @weakify(self);
    if (![IMYAssetsManager isAccessible]) {
        [UIWindow imy_showTextHUD:IMYString(@"相册照片无法显示啦，请在系统设置-隐私-照片中打开美柚开关")];
    } else {
        IMYAssetPickerController *vc = [[IMYAssetPickerController alloc] init];
        vc.allowsMultipleSelection = YES;
        vc.showSelectCountTipLabel = YES;
        vc.styleType = IMYAssetPickerUITypeNew;
        vc.selectedAssetArray = [self.selectedArray mutableCopy];
        vc.trackEventPrefix = @"ttq";
        vc.delegate = self;
        NSMutableArray *selectedAssetURLs = [NSMutableArray new];
        for (TTQCameraButton *button in self.cameraButtons) {
            if (button.assetURL && [button.assetURL.absoluteString hasPrefix:@"assets"]) {
                [selectedAssetURLs addObject:button.assetURL];
            }
        }
        vc.maximumNumberOfSelection = self.limit - (self.cameraButtons.count - selectedAssetURLs.count);
        [self.controller imy_present:vc animated:YES];
    }
}

- (void)takeImageFromCamera {
    [self takeImageEventHelper];
    UIImagePickerController *imagePickerController = [UIViewController ttq_imagePickerController];
    imagePickerController.delegate = self;
    [self.controller presentViewController:imagePickerController
                                  animated:YES
                                completion:^{
                                    [UIApplication sharedApplication].statusBarStyle = UIStatusBarStyleDefault;
                                }];
}

- (void)takeImage {
    if (!self.activeCameraButton) {
        self.activeCameraButton = self.cameraButtons.firstObject;
        if (!self.activeCameraButton) {
            self.activeCameraButton = [self viewWithTag:self.cameraButtonTag + 1];
        }
    }
    
    [self takeImageFromAlbumWithStyle:IMYAssetPickerUITypeNew];
}
//newalbum
- (void)takeImageFromAlbumWithStyle:(IMYAssetPickerUIType)style{
    [IMYEventHelper event:@"tjtp" label:@"系统相册"];
    if (self.relay) {
        [IMYEventHelper event:@"xtxcdy" attributes:@{@"来源": @"回复"}];
    } else {
        [IMYEventHelper event:@"xtxcdy" attributes:@{@"来源": @"其他"}];
    }

    IMYAssetPickerController *vc = [[IMYAssetPickerController alloc] init];
    vc.allowsMultipleSelection = YES;
    vc.showSelectCountTipLabel = YES;
    vc.styleType = style;
    vc.selectedAssetArray = [self.selectedArray mutableCopy];
    vc.trackEventPrefix = @"ttq";
    vc.delegate = self;
    NSMutableArray *selectedAssetURLs = [NSMutableArray new];
    for (TTQCameraButton *button in self.cameraButtons) {
        if (button.assetURL && [button.assetURL.absoluteString hasPrefix:@"assets"]) {
            [selectedAssetURLs addObject:button.assetURL];
        }
    }
    vc.maximumNumberOfSelection = self.limit;
    [[UIViewController imy_currentViewControlloer] imy_present:vc animated:YES];
}

#pragma mark - IMYAssetPickerControllerDelegate

- (void)assetPickerController:(IMYAssetPickerController *)assetPickerController didSelectAssets:(NSArray *)assets {
    //    [UIView imy_showLoadingHUD];
    NSMutableArray *array = [NSMutableArray new];
    self.selectedArray = [NSMutableArray arrayWithArray:assets];
    for (TTQCameraButton *btn in self.cameraButtons) {
        if ([btn.assetURL.absoluteString hasPrefix:@"http"]) {
            [array addObject:btn];
        } else {
            [btn removeFromSuperview];
        }
    }
    [self.cameraButtons removeAllObjects];
    [self.cameraButtons addObjectsFromArray:array];
    
    [self addImageWithAssets:assets index:0];
    
    // 实验上报
    [self postUploadPicABTestData];
    // BI: 图片选择完成_评论_新方案
    [TTQCommonHelp GAEventForEventWithName:@"tpxzwc_pl_new" action:2];
}

- (void)addImageWithAssets:(NSArray *)assets index:(NSInteger)index {
    if (index >= assets.count) {
        return;
    }
    @weakify(self);
    IMYQACameraButton *cameraButton = [[IMYQACameraButton alloc] initWithFrame:CGRectMake(15 + (2 + IMG_W_H) * (index % 4), 5 + (2 + IMG_W_H) * (index / 4), IMG_W_H, IMG_W_H)];
    IMYAssetModel *asset = assets[index];
    [cameraButton.deleteButton addTarget:self action:@selector(removeObjectFromCameraButtons:) forControlEvents:UIControlEventTouchUpInside];
    if (asset ) {
        if (asset.assetSubType  == IMYAssetSubTypeGIF) {
            [asset requestImageData:^(NSData * _Nonnull imageData, NSDictionary<NSString *,id> * _Nonnull info, BOOL isGIF, BOOL isHEIC) {
                @strongify(self);
                imy_asyncMainBlock(^{
                    if (imageData) {
                        UIImage *image = [UIImage sd_animatedGIFWithData:imageData];
                        image = image.ttq_normalizedImage;
                        cameraButton.originalImage = image;
                        cameraButton.tag = self.cameraButtonTag;
                        [cameraButton addTarget:self action:@selector(handleCameraButtonTouchUpInside:) forControlEvents:UIControlEventTouchUpInside];
                        [self.cameraButtons addObject:cameraButton];
                        [self layoutViews];
                        [self addImageWithAssets:assets index:(index+1)];
                    }
                });
                
            }];
        } else {
            [asset requestFullScreenImageWithCompletion:^(UIImage * _Nonnull result, NSDictionary<NSString *,id> * _Nonnull info) {
                @strongify(self);
                imy_asyncMainBlock(^{
                    if (result) {
                        NSLog(@"asset.id = %@", asset.identifier);
                        UIImage *image = [self reProcessImageForLowerSize:result.ttq_normalizedImage];
                        cameraButton.originalImage = image;
                        cameraButton.tag = self.cameraButtonTag;
                        [cameraButton addTarget:self action:@selector(handleCameraButtonTouchUpInside:) forControlEvents:UIControlEventTouchUpInside];
                        [self.cameraButtons addObject:cameraButton];
                        [self layoutViews];
                        [self addImageWithAssets:assets index:(index+1)];
                    }
                });
            } andProgressHandler:nil];
        }
    }
}

- (UIImage *)reProcessImageForLowerSize:(UIImage *)originalImage {
    if (originalImage) {
        NSData *imageData = UIImageJPEGRepresentation(originalImage, 1);
        if (imageData.length > 1024 * 100) {
            imageData = UIImageJPEGRepresentation(originalImage, 0.5);
            return [UIImage imageWithData:imageData];
        } else {
            return originalImage;
        }
    } else {
        return nil;
    }
}

- (void)assetPickerControllerWillCancelling:(IMYAssetPickerController *)assetPickerController {
    //点击相册返回键
    if (self.imageChangeBlock) {
        self.imageChangeBlock();
    }
}
#pragma mark - UIImagePickerControllerDelegate
- (void)imagePickerController:(UIImagePickerController *)picker didFinishPickingMediaWithInfo:(NSDictionary *)info {
    @weakify(self);
    UIImage *image = info[UIImagePickerControllerOriginalImage];
    image = image.ttq_normalizedImage;
    if (image) {
        [UIView imy_showLoadingHUD];
        [[IMYAssetsManager sharedInstance] saveImage:image completionHandler:^(IMYAssetModel * _Nonnull model, NSError * _Nonnull error) {
            if (!error) {
                PHAsset *phAsset = [model fetchPhAsset];
                PHImageRequestOptions *requestOptions = [[PHImageRequestOptions alloc] init];
                requestOptions.synchronous = NO;
                requestOptions.networkAccessAllowed = YES;
                [[[IMYAssetsManager sharedInstance] cachingImageManager] requestImageDataForAsset:phAsset
                                                                                          options:requestOptions resultHandler:^(NSData * _Nullable imageData, NSString * _Nullable dataUTI, UIImageOrientation orientation, NSDictionary * _Nullable info) {
                    imy_asyncMainBlock(^{
                        @strongify(self);
                        if (info && info[@"PHImageFileURLKey"] != nil) {
                                NSURL *assetURL = info[@"PHImageFileURLKey"];
                                self.activeCameraButton.assetURL = assetURL;
                                self.activeCameraButton.originalImage = image;
                                self.activeCameraButton.tag = self.cameraButtonTag;
                                [self.cameraButtons addObject:self.activeCameraButton];
                                [self layoutViews];
                                [UIView imy_hideHUD];
                                [picker dismissViewControllerAnimated:YES completion:nil];
                           
                        } else {
                            [UIAlertView imy_showAlertViewWithTitle:IMYString(@"获取图片失败")
                                                            message:nil
                                                  cancelButtonTitle:IMYString(@"确定")
                                                  otherButtonTitles:nil
                                                            handler:nil];
                            [picker dismissViewControllerAnimated:YES completion:nil];
                        }
                    });
                }];
            } else {
                imy_asyncMainBlock(^{
                    [UIAlertView imy_showAlertViewWithTitle:IMYString(@"保存图片到相册失败")
                                                    message:nil
                                          cancelButtonTitle:IMYString(@"确定")
                                          otherButtonTitles:nil
                                                    handler:nil];
                    [picker dismissViewControllerAnimated:YES completion:nil];
                });
            }
        }];
    } else {
        [UIAlertView imy_showAlertViewWithTitle:IMYString(@"获取图片失败")
                                        message:nil
                              cancelButtonTitle:IMYString(@"确定")
                              otherButtonTitles:nil
                                        handler:nil];
        [picker dismissViewControllerAnimated:YES completion:nil];
    }
}

- (void)imagePickerControllerDidCancel:(UIImagePickerController *)picker {
    [[UIApplication sharedApplication] setStatusBarStyle:UIStatusBarStyleLightContent];
    [[UIApplication sharedApplication] setStatusBarOrientation:UIInterfaceOrientationPortrait];
    [self.controller.navigationController dismissViewControllerAnimated:YES completion:nil];
}

- (void)removeFromSuperview {
    if (self.removeBlock) {
        self.removeBlock(self.cameraButtons.count - 1);
    }
    [super removeFromSuperview];
}


// MARK: - AbTest

- (void)postUploadPicABTestData {
    // 实验上报
    IMYABTestExperiment *exp = [TTQABTestConfig uploadPicABTest];
    if (exp) {
        // action = 2 为自定义指标统计，构建相关上报参数
        // name 为指标key， 点击指标 一般 value 传 1， 其他指标看产品定啥，就传啥。
        IMYABTestPoster *poster = IMYABTestPostBuilder.action(2).experiment(exp).name(@"done_up").value(@"1").build();
        // 执行上报
        [[IMYABTestManager sharedInstance] postAction:poster];
    }
}
@end
