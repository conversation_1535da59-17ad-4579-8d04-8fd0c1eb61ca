//
//  IMYQATopicReferenceViewModel.h
//  IMYTTQ
//
//  Created by king on 15/7/16.
//  Copyright © 2015年 MeiYou. All rights reserved.
//

#import "TTQReferenceCommentModel.h"
#import "TTQTopicBaseViewModel.h"

@interface IMYQATopicReferenceViewModel : TTQTopicBaseViewModel
//从话题详情才会传过来的.为了回退的时候能进行刷新
@property (nonatomic, strong) TTQCommentModel *commentModel;
// 评论回调的block，不依赖commentModel属性
@property (nonatomic, copy) void (^commentResultBlock)(BOOL has_praise, NSInteger praise_num, NSInteger referenced_num, NSArray *commentDataDictArray);
@property (nonatomic, strong) TTQReferenceCommentModel *referenced;
@property (nonatomic, assign) TTQCommentBindTopicType topicType;
@property (nonatomic, assign) BOOL isNewCommuity; ///新社区
@property (nonatomic, assign) BOOL becomeFirstResponder;
@property (nonatomic, assign) BOOL hasMore;
@property (nonatomic, copy) NSString* cardShareUrlString;
@property (nonatomic, copy) NSString* topicTitle;
@property (nonatomic, assign) NSInteger requestType;//0下拉刷新 或者是刚进去的时候的刷新
/// 跳转至详情页要展示二级评论
@property (nonatomic, assign) NSInteger animationCommentIdWhenAppear;
@property (nonatomic, assign) BOOL v887Style;
@property (nonatomic, assign) NSInteger bi_replyPublicKey;///< 是否是从评论icon点击进入的

- (instancetype)initWithTopicID:(NSInteger)topic_id referenced_id:(NSInteger)referenced_id;
@end
