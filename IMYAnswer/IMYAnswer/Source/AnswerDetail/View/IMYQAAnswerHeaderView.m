//
//  IMYQAAnswerHeaderView.m
//  IMYAnswer
//
//  Created by 林云峰 on 2025/3/6.
//

#import "IMYQAAnswerHeaderView.h"
#import <IMYBaseKit/IMYBaseKit.h>
#import <IMYCommonKit/IMYCKFollowButton.h>
#import "IMYQARelateQuestionView.h"
#import <IMYTTQ/IMYSelectableAttributedLabel.h>
#import "UIView+TTQ.h"
#import "IMYCKFeedsAuthenticationView.h"
#import "UIImageView+TTQ.h"
#import "NSString+TTQ.h"
#import "TTQTopicRichWithParserView.h"
#import "TTQABTestConfig.h"
#import "TTQPublisherModel+IMYQACommentCellTag.h"
#import "TTQTagIcon.h"

@interface IMYQAAnswerHeaderView ()

@property (nonatomic, strong) TTQCommentModel *data;
@property (nonatomic, strong) IMYAvatarImageView *avatarButton;
@property (nonatomic, strong) IMYSelectableAttributedLabel *contentLabel;
@property (nonatomic, strong) UIControl *userInfoControl;

@property (nonatomic, strong) UILabel *nicknameLabel;
@property (nonatomic, strong) UILabel *ipInfoLabel;// ip
@property (nonatomic, strong) UILabel *publishTimeLabel;// 时间
@property (nonatomic, strong) UILabel *authorInfoLabel;   /// 作者信息
@property (nonatomic, strong) IMYCKFollowButton *followButton;
@property (nonatomic, strong) UILabel *authThanksIcon;  /// 作者感谢
@property (nonatomic, strong) UILabel *thanksLabel; /// xxx人感谢
@property (nonatomic, strong) IMYQARelateQuestionView *articleView;   /// 原问题内容
@property (nonatomic, assign) CGFloat contentStartY;
@property (strong, nonatomic) NSMutableArray *commentImageViews;
@property (nonatomic, strong) UILabel *copyRightLabel;
@property (nonatomic, strong) UIView *authView;   /// 创作者标识
@end

@implementation IMYQAAnswerHeaderView


- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        self.commentImageViews = [NSMutableArray array];
        [self imy_setBackgroundColorForKey:kCK_White_AN];
        [self initViews];
    }
    return self;
}

- (void)initViews {
    [self addSubview:self.avatarButton];
    [self addSubview:self.nicknameLabel];
    [self addSubview:self.authorInfoLabel];
    [self addSubview:self.publishTimeLabel];
    [self addSubview:self.ipInfoLabel];
    [self addSubview:self.followButton];
    [self addSubview:self.thanksLabel];
    [self addSubview:self.authThanksIcon];
    [self addSubview:self.contentLabel];
    [self addSubview:self.articleView];
    [self addSubview:self.copyRightLabel];
    [self addSubview:self.authView];

    self.userInfoControl = [[UIControl alloc] initWithFrame:CGRectMake(12, 16, 0, 36)];
    [self addSubview:self.userInfoControl];
    @weakify(self);
    [[self.userInfoControl rac_signalForControlEvents:UIControlEventTouchUpInside] subscribeNext:^(__kindof UIControl * _Nullable x) {
        @strongify(self);
        TTQPublisherModel *user = self.data.publisher;
        [self gotoUserIntroduce:user];
    }];
}

- (void)updateWithData:(TTQCommentModel *)data {
    self.data = data;
    [self updateUser];
    [self updateContent];
}

#pragma mark - 作者区
- (void)updateUser {
    // 封号也要显示关注
    BOOL isShow = (self.data.publisher.error == 0 || self.data.publisher.error == 2 || self.data.publisher.error == 3) && self.data.publisher.userID != [[IMYPublicAppHelper shareAppHelper].userid integerValue];
    [self.avatarButton setAvatarWithURLString:self.data.publisher.user_avatar.large placeholder:[UIImage imy_imageForKey:@"all_usericon"]];
    [self.avatarButton ttq_showCrownLevel:self.data.publisher.rank];
    [self.avatarButton imy_showPendant:[self.data.publisher hasRankPendant]? nil :self.data.publisher.pendant_url];
    [self updatefollowButtonShow:isShow status:self.data.publisher.is_followed];
    NSString *babyInfoStr = [self.data.publisher babyInfoOrExpertInfo];
//    if (self.data.publisher.title) {
//        babyInfoStr = self.data.publisher.title;
//    }
    if (imy_isNotEmptyString(babyInfoStr)) {
        self.authorInfoLabel.hidden = NO;
        self.authorInfoLabel.text = babyInfoStr;
        CGFloat maxRight = self.followButton.hidden?self.imy_width : self.followButton.imy_left;
        self.authorInfoLabel.imy_width = maxRight - 12 - self.authorInfoLabel.imy_left;
        self.publishTimeLabel.imy_top = self.authorInfoLabel.imy_bottom;
        self.followButton.imy_top = 30;
    } else {
        self.authorInfoLabel.hidden = YES;
        self.publishTimeLabel.imy_top = self.authorInfoLabel.imy_top;
        self.followButton.imy_centerY = self.avatarButton.imy_centerY;
    }
    self.publishTimeLabel.text = [[self.data.updated_date imy_getDateTime] imy_getDisplayTimeStringYear];
    [self.publishTimeLabel sizeToFit];
    self.publishTimeLabel.imy_height = 16;
    if (imy_isNotEmptyString(self.data.ip_region)) {
        self.ipInfoLabel.hidden = NO;
        self.ipInfoLabel.text = [NSString stringWithFormat:@"发布于 %@",self.data.ip_region];
        [self.ipInfoLabel sizeToFit];
        self.ipInfoLabel.imy_height = 16;
        self.ipInfoLabel.imy_centerY = self.publishTimeLabel.imy_centerY;
        self.ipInfoLabel.imy_left = self.publishTimeLabel.imy_right + 8;

    } else {
        self.ipInfoLabel.hidden = YES;
    }
    if (self.data.praise_num) {
        self.thanksLabel.hidden = NO;
        self.thanksLabel.text = [NSString stringWithFormat:@"%ld 姐妹表达感谢",self.data.praise_num];
        self.thanksLabel.imy_top = self.publishTimeLabel.imy_bottom + 12;
        if (self.data.praised_by_author) {
            self.authThanksIcon.hidden = NO;
            self.authThanksIcon.imy_centerY = self.thanksLabel.imy_centerY;
            self.thanksLabel.imy_left = self.authThanksIcon.imy_right + 8;
        } else {
            self.thanksLabel.imy_left = 12;
            self.authThanksIcon.hidden = YES;
        }
        self.contentStartY = self.thanksLabel.imy_bottom + 12;
    } else {
        self.authThanksIcon.hidden = self.thanksLabel.hidden = YES;
        self.contentStartY = self.publishTimeLabel.imy_bottom + 12;
    }
    self.userInfoControl.imy_width = self.nicknameLabel.imy_right - 12;
}

- (void)updatefollowButtonShow:(BOOL)show status:(NSInteger)followStatus {
    if (!show) {
        self.followButton.hidden = YES;
        [self updateBadgesWithModel:self.data];
        return;
    }
    self.followButton.hidden = NO;
    [self.followButton setRelation:followStatus fullColorStyle:YES];
    [self layoutIfNeeded];
    [self updateBadgesWithModel:self.data];
}


-(void)updateBadgesWithModel:(TTQCommentModel *)model{
    self.nicknameLabel.text = model.publisher.screen_name;;
    self.nicknameLabel.imy_left = 56;
    CGFloat nameWidth = ceilf([self.nicknameLabel sizeThatFits:CGSizeMake(MAXFLOAT, 20)].width);
    self.nicknameLabel.imy_left = 56;
    self.nicknameLabel.imy_width = nameWidth;
    
    //蓝V>专家>达人>职业创作者>专业创作者
    //专家标识从 qaTagsForPublisherOwned 中获取
    self.authView.hidden = YES;
    CGFloat authWidth = 0;
    NSArray<TTQTagIconModel *> *iconModels = [self.data.publisher qaTagsForPublisherOwned:self.data.topic_user_id];
    if (model.publisher.badges.count > 0 || iconModels.count > 0) {
        self.authView.hidden = NO;
        [self.authView imy_removeAllSubviews];
        UIView *lastItemView = nil;
        IMYMixHomeUserBadge *blueVBadge = [model.publisher.badges bk_match:^BOOL(IMYMixHomeUserBadge *obj) {
            return [obj.type isEqualToString: @"blue-v"];
        }];
        if (blueVBadge) {
            IMYCKFeedsAuthenticationView *blueVView = [[IMYCKFeedsAuthenticationView alloc] initWithFrame:CGRectMake(0, 0, 0, 16)];
            [self.authView addSubview:blueVView];
            [blueVView updateWithBadge:blueVBadge];
            blueVView.imy_left = authWidth;
            authWidth += blueVView.imy_width; + 2;
        }
        
        if (iconModels.count > 0) {
            for (TTQTagIconModel *iconModel in iconModels) {
                TTQTagIcon *iconView = [TTQTagIcon new];
                iconView.semicircleCornerRadius = YES;
                iconView.tagModel = iconModel;
                [self.authView addSubview:iconView];
                iconView.imy_left = authWidth;
                authWidth += iconView.imy_width + 2;
            }
        }
        
        NSArray<IMYMixHomeUserBadge *> *otherBadges = [model.publisher.badges bk_select:^BOOL(IMYMixHomeUserBadge *obj) {
            return ![obj.type isEqualToString:@"blue-v"];
        }];
        for (IMYMixHomeUserBadge *badge in otherBadges) {
            IMYCKFeedsAuthenticationView *itemView = [[IMYCKFeedsAuthenticationView alloc] initWithFrame:CGRectMake(0, 0, 0, 16)];
            [self.authView addSubview:itemView];
            [itemView updateWithBadge:badge];
            itemView.imy_left = authWidth;
            authWidth += itemView.imy_width + 2;
        }
        
    }
    
    CGFloat nameMaxWidth = SCREEN_WIDTH - 56 - 12;
    if (!self.followButton.isHidden) {
        nameMaxWidth -= 8 + self.followButton.imy_width;
    }
    if (authWidth > 0) {
        nameMaxWidth -= 4 + authWidth; // 留出标识的宽度
    }
    
    if (nameWidth > nameMaxWidth) {
        self.nicknameLabel.imy_width = nameMaxWidth;
        self.authView.imy_left = self.nicknameLabel.imy_right + 4;
    }
    self.authView.hidden = (authWidth == 0);
    self.authView.imy_width = authWidth;
    self.authView.imy_height = 16;
    self.authView.imy_left = self.nicknameLabel.imy_right + 2;
    self.authView.imy_centerY = self.nicknameLabel.imy_centerY;

    [self layoutIfNeeded];
}

#pragma mark - 正文区

- (void)updateContent {
    self.contentLabel.imy_top = self.contentStartY;
    [self.contentLabel imyr_setText:nil];
    [self.contentLabel imyr_setText:self.data.content];
    [self.contentLabel imyr_autoAdjustHeight];
    
    CGFloat bottom = [self updateImages];
    self.articleView.imy_top = bottom;
    [self.articleView updateWithData:self.data.topic];
    if (self.data.is_ai_reply) {
        self.copyRightLabel.hidden = NO;
        self.copyRightLabel.imy_top = self.articleView.imy_bottom + 24;
        self.imy_height = self.copyRightLabel.imy_bottom + 24;
    } else {
        self.copyRightLabel.hidden = YES;
        self.imy_height = self.articleView.imy_bottom + 24;
    }
}

- (CGFloat)updateImages {
    __block CGFloat imageTop = self.contentLabel.imy_bottom + 12;
    [self.commentImageViews bk_each:^(TTQTopicRichWithImgView *obj) {
        [obj removeFromSuperview];
        obj.imgView.image = nil;
        obj.imy_height = 0.0;
        [obj.imgView sd_cancelCurrentImageLoad];
        [obj.gestureRecognizers enumerateObjectsWithOptions:NSEnumerationReverse
                                                 usingBlock:^(__kindof UIGestureRecognizer *_Nonnull ges, NSUInteger idx, BOOL *_Nonnull stop) {
                                                     [obj removeGestureRecognizer:ges];
                                                 }];
    }];
    
    if (self.data.images.count) {
        NSMutableArray *photos = [NSMutableArray array];
        if (self.data.images.count > self.commentImageViews.count) {
            NSInteger leftNum = self.data.images_list.count - self.commentImageViews.count;
            for (NSInteger index = 0; index < leftNum; index++) {
                [self.commentImageViews addObject:[self createCommentImageView]];
            }
        }
        CGFloat imageWidth = SCREEN_WIDTH - 24;
        [self.data.images_list enumerateObjectsUsingBlock:^(NSDictionary *_Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
            TTQTopicRichParserImgModel *imgModel = [TTQTopicRichParserImgModel new];
            imgModel.src = obj[@"image_url"];
            imgModel.isLow = [obj[@"is_low"] boolValue];
            TTQTopicRichWithImgView *imageView = self.commentImageViews[idx];
            imageView.imgView.tag = idx;

            [imageView updateImageModel:imgModel addition:nil width:self.imy_width - 24 isStart:NO coverHeight:0];
            imageView.imy_top = imageTop;
            imageTop += (imageView.imy_height + 12);
            [self addSubview:imageView];
            
            if (imgModel.isLow) {
                [imageView hideImageForLowQuality];
            }
            @weakify(self);
            imageView.clickImgBlock = ^(UIImageView *imgView) {
                @strongify(self);
                [self showWithPhotos:photos atIndex:imgView.tag];
            };
            IMYPhoto *photo = [[IMYPhoto alloc] init];
            photo.url = [NSURL URLWithString:imgModel.src];
            photo.srcImageView = imageView.imgView;
            [photos addObject:photo];

        }];
    }
    return imageTop;
}

- (TTQTopicRichWithImgView *)createCommentImageView {
    TTQTopicRichWithImgView *commentImageView = [[TTQTopicRichWithImgView alloc] initWithFrame:CGRectMake(12, 0, self.imy_width - 24, 0)];
    commentImageView.imgView.imy_failureShowText = @"图片加载失败，点击重试";
    commentImageView.imgView.imy_placeholderImage = nil;
    return commentImageView;
}


- (BOOL)isAppUser:(NSUInteger)userId {
    if ([[IMYPublicAppHelper shareAppHelper].userid integerValue] == userId) {
        return YES;
    }
    return NO;
}

- (void)showWithPhotos:(NSMutableArray *)photos atIndex:(NSUInteger)index {
    NSMutableDictionary *tmpDic = [[NSMutableDictionary alloc] init];
    tmpDic[@"action"] = @(2);
    tmpDic[@"info_type"] = @(12);
    tmpDic[@"info_id"] = @(self.data.topic_id);
    tmpDic[@"fuid"] = @(self.data.publisher.userID);
    tmpDic[@"event"] = @"dsq_nrxqy_ztnrtp";
    [IMYGAEventHelper postWithPath:@"event" params:[tmpDic copy] headers:nil completed:nil];
    
    [IMYPhotoBrowser showWithPhotos:photos atIndex:index];
}

#pragma mark - 个人主页跳转
- (void)gotoUserIntroduce:(TTQPublisherModel *)publisher {
    if ([TTQABTestConfig disableInteractByType:TTQ_Interact_type_userPage]) {
        return;
    }
    if (publisher.userID > 0 && publisher.error == 0) {
        IMYURI *uri = [IMYURI uriWithPath:@"user/dynamic"
                                               params:@{ @"userID": @(publisher.userID),
                                                         @"source": @"话题详情" }
                                                 info:nil];
        [[IMYURIManager shareURIManager] runActionWithURI:uri];
        
    } else if (publisher.error == 1) {
        [UIView imy_showTextHUD:kStatusText_UserAnonymous];
    } else if (publisher.error == 2 || publisher.error == 3) {
        [UIView imy_showTextHUD:kStatusText_homePageNotOpen];
    }
    [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"dsq_nrxqy_tx",@"action":@2,@"comment_id":@(self.data.commentID),@"info_type":@12,@"info_id":@(self.data.topic_id),@"fuid":@(self.data.publisher.userID),@"public_type":@1} headers:nil completed:nil];
}

#pragma mark - UI

- (IMYAvatarImageView *)avatarButton {
    if (!_avatarButton) {
        _avatarButton = [[IMYAvatarImageView alloc] initWithFrame:CGRectMake(12, 16, 36, 36)];
        _avatarButton.layer.cornerRadius = 18;
        _avatarButton.layer.borderColor = [UIColor imy_colorForKey:kCK_Black_L].CGColor;
        _avatarButton.layer.borderWidth = 0.5f;
    }
    return _avatarButton;
}

- (UILabel *)nicknameLabel {
    if (!_nicknameLabel) {
        _nicknameLabel = [[UILabel alloc] initWithFrame:CGRectMake(56, 16, 100, 20)];
        _nicknameLabel.font = [UIFont systemFontOfSize:14];
        _nicknameLabel.lineBreakMode = NSLineBreakByTruncatingMiddle;
        [_nicknameLabel imy_setTextColorForKey:kCK_Black_A];
    }
    return _nicknameLabel;
}

- (UILabel *)ipInfoLabel {
    if (!_ipInfoLabel) {
        _ipInfoLabel = [[UILabel alloc] initWithFrame:CGRectMake(0, 0, 0, 16)];
        _ipInfoLabel.font = [UIFont systemFontOfSize:11];
        [_ipInfoLabel imy_setTextColorForKey:kCK_Black_B];
    }
    return _ipInfoLabel;
}

- (UILabel *)publishTimeLabel {
    if (!_publishTimeLabel) {
        _publishTimeLabel = [[UILabel alloc] initWithFrame:CGRectMake(56, 0, 0, 16)];
        _publishTimeLabel.font = [UIFont systemFontOfSize:11];
        
        [_publishTimeLabel imy_setTextColorForKey:kCK_Black_B];
    }
    return _publishTimeLabel;
}

- (UILabel *)authorInfoLabel {
    if (!_authorInfoLabel) {
        _authorInfoLabel = [[UILabel alloc] initWithFrame:CGRectMake(56, 36, 200, 16)];
        _authorInfoLabel.font = [UIFont systemFontOfSize:11];
        [_authorInfoLabel imy_setTextColorForKey:kCK_Black_B];
        _authorInfoLabel.lineBreakMode = NSLineBreakByTruncatingMiddle;
    }
    return _authorInfoLabel;
}

- (IMYCKFollowButton *)followButton {
    if (!_followButton) {
        _followButton = [[IMYCKFollowButton alloc] initWithFrame:CGRectMake(0, 0, 60, 26)];
        _followButton.imy_right = self.imy_width - 12;
        [_followButton.titleLabel setFont:[UIFont systemFontOfSize:12]];
        _followButton.imy_size = CGSizeMake(60, 26);
        _followButton.imy_centerY = self.avatarButton.imy_centerY;
        @weakify(self);
        [[[[_followButton rac_signalForControlEvents:UIControlEventTouchUpInside] deliverOnMainThread] takeUntil:self.rac_willDeallocSignal] subscribeNext:^(id x) {
            @strongify(self);
            if (self.tapFollowAction) {
                self.tapFollowAction(x);
            }
        }];

    }
    return _followButton;
}

- (UILabel *)authThanksIcon {
    if (!_authThanksIcon) {
        _authThanksIcon = [[UILabel alloc] initWithFrame:CGRectMake(12, 0, 52, 16)];
        _authThanksIcon.font = [UIFont systemFontOfSize:11];
        [_authThanksIcon imy_setTextColorForKey:kCK_Red_B];
        _authThanksIcon.textAlignment = NSTextAlignmentCenter;
        _authThanksIcon.text = @"作者感谢";
        _authThanksIcon.layer.cornerRadius = 4;
        _authThanksIcon.layer.borderColor = [UIColor imy_colorForKey:kCK_Red_B].CGColor;
        _authThanksIcon.layer.borderWidth = 1/SCREEN_SCALE;
    }
    return _authThanksIcon;
}

- (UILabel *)thanksLabel {
    if (!_thanksLabel) {
        _thanksLabel = [[UILabel alloc] initWithFrame:CGRectMake(56, 36, 200, 18)];
        _thanksLabel.font = [UIFont systemFontOfSize:13];
        [_thanksLabel imy_setTextColorForKey:kCK_Black_B];
    }
    return _thanksLabel;
}

- (IMYSelectableAttributedLabel *)contentLabel {
    if (!_contentLabel) {
        _contentLabel = [[IMYSelectableAttributedLabel alloc] initWithFrame:CGRectMake(12, 0, SCREEN_WIDTH - 24, 0)];
        [_contentLabel imy_setTextColorForKey:kCK_Black_A];
        _contentLabel.font = [UIFont systemFontOfSize:16];
        _contentLabel.newStyle = YES;
        _contentLabel.minLineHeight = 26;
        _contentLabel.imyr_emoticonSize = CGSizeMake(22, 22);
        _contentLabel.imyr_dynamicEmoticonSize = CGSizeMake(80, 80);
        _contentLabel.imyr_analysisDynamicEmoticon = NO;
        _contentLabel.textAlignment = kCTTextAlignmentLeft;
    }
    return _contentLabel;
}

- (IMYQARelateQuestionView *)articleView {
    if (!_articleView) {
        _articleView = [[IMYQARelateQuestionView alloc] initWithFrame:CGRectMake(12, 0, SCREEN_WIDTH - 24, 24)];
        _articleView.layer.cornerRadius = 8;
        _articleView.layer.masksToBounds = YES;
        @weakify(self);
        [[_articleView rac_signalForControlEvents:UIControlEventTouchUpInside] subscribeNext:^(__kindof UIControl * _Nullable x) {
            @strongify(self);
            if (self.articleTapAction) {
                self.articleTapAction();
            }
            [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"dsq_nrxqy_ytlj",@"action":@2,@"comment_id":@(self.data.commentID),@"info_type":@12,@"info_id":@(self.data.topic_id),@"fuid":@(self.data.publisher.userID)} headers:nil completed:nil];
        }];
        
        _articleView.imyut_eventInfo.eventName = [NSString stringWithFormat:@"answer_qusetion_%p",_articleView];
        [_articleView.imyut_eventInfo setExposuredBlock:^(__kindof UIView *view, NSDictionary *params) {
            @strongify(self);
            [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"dsq_nrxqy_ytlj",@"action":@1,@"comment_id":@(self.data.commentID),@"info_type":@12,@"info_id":@(self.data.topic_id),@"fuid":@(self.data.publisher.userID)} headers:nil completed:nil];
        }];
    }
    return _articleView;
}

- (UILabel *)copyRightLabel {
    if (!_copyRightLabel) {
        _copyRightLabel = [[UILabel alloc] initWithFrame:CGRectMake(12, 0, 300, 18)];
        _copyRightLabel.font = [UIFont systemFontOfSize:15];
        [_copyRightLabel imy_setTextColorForKey:kCK_Black_B];
        _copyRightLabel.text = @"以上回答包含AI生成内容";
        _copyRightLabel.hidden = YES;
    }
    return _copyRightLabel;
}
- (UIView *)authView {
    if (!_authView) {
        _authView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, 0, 16)];
    }
    return _authView;
}
@end


