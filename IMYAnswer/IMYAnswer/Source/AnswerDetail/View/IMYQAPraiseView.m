//
//  IMYQAPraiseView.m
//  IMYTTQ
//
//  Created by king on 16/9/5.
//  Copyright © 2016年 MeiYou. All rights reserved.
//

#import "IMYQAPraiseView.h"
#import "IMY_ViewKit.h"
#import "ReactiveCocoa.h"
#import "TTQABTestConfig.h"
#import "TTQAttachedUserInfoProtocol.h"
#import "TTQCheckService.h"
#import "TTQCommonHelp.h"
#import "TTQDoorConfig+IconConfig.h"
#import "TTQHttpHelper.h"
#import "TTQJumpType.h"
#import "TTQMacro.h"
#import "UIFont+TTQ.h"
#import "UIView+TTQ.h"
#import <IMYAccount/IMYAccountCheckService.h>
#import <IMYUGC/IMYUGC.h>
#import <IMYUGC/IMYCKFeedsHelper.h>
#import "IMYQATopicReferenceViewModel.h"

@interface IMYQAPraiseView ()

@property (nonatomic, weak) RACDisposable *disposable;
@property (nonatomic, strong) NSObject *model;
@property (nonatomic, strong) id viewModel;

@end
@implementation IMYQAPraiseView
- (void)prepareUI {
    [super prepareUI];
    if ([IMYCKFeedsHelper praiseStyleExp]) {
        self.placeholderText = @"感谢";
        self.normalImageKey = @"community_flow_icon_heart";
        self.selectedImageKey = @"community_flow_icon_heart_sel";
        [self updatePagResource:@"praise_thanks_166_comment" size:CGSizeMake(166, 166) rightDif:52 bottomDif:0];
    }

    @weakify(self);
    [self setTapActionBlock:^{
        @strongify(self);
        [self praiseAction:self.model viewModel:self.viewModel];
    }];
}

- (void)bindModel:(NSObject *)model viewModel:(id)viewModel {
    @weakify(self, model);
    [self.disposable dispose];
    self.disposable = [[[model rac_valuesForKeyPath:@"praise_num" observer:model_weak_] deliverOnMainThread] subscribeNext:^(id x) {
        @strongify(self, model);
        [self setupPraiseButton:model];
    }];
    self.model = model;
    self.viewModel = viewModel;
}

- (void)praiseAction:(NSObject *)model viewModel:(id)viewModel {
    TTQTopicCurrentUserInfo *currentUserInfo = nil;
    if ([viewModel conformsToProtocol:@protocol(TTQAttachedUserInfoProtocol)]) {
        currentUserInfo = ((id<TTQAttachedUserInfoProtocol>)viewModel).currentUserInfo;
    }

    // 点赞判断
    BOOL hasPraise = [[model valueForKey:@"has_praise"] boolValue];
    BOOL judgeBlocked = [self judgeBlocked:currentUserInfo];
    if (judgeBlocked) {
        return;
    }
    
    BOOL shouldPraise = [TTQCheckService checkShouldPraiseWithUserStatus:currentUserInfo.error hasPraised:hasPraise];
    if (!shouldPraise) {
        return;
    } else {
        [IMYEventHelper event:@"wdxqy_dz" attributes:@{@"mode": @([IMYPublicAppHelper shareAppHelper].userMode)}];
    }
    
    NSInteger from_community_home = 0;//从她她圈首页来的
    if ([viewModel isKindOfClass:NSClassFromString(@"TTQTopicBaseViewModel")]) {
        from_community_home = [[viewModel valueForKey:@"from_community_home"] integerValue];
    }

    // 处理点赞的逻辑
    BOOL has_praise = ![[model valueForKey:@"has_praise"] boolValue];
    NSInteger praise_num = [[model valueForKey:@"praise_num"] integerValue];
    if (has_praise) {// 展示点赞动画
        [model setValue:@(has_praise) forKey:@"has_praise"];
        [model setValue:@(praise_num + 1) forKey:@"praise_num"];
        [self startAnimating];
    } else {
        [model setValue:@(has_praise) forKey:@"has_praise"];
        [model setValue:@(praise_num - 1) forKey:@"praise_num"];
    }
    NSMutableDictionary *dic = @{@"is_praise": has_praise ? @1 : @0,
                                 @"forum_id": @(self.forum_id),
                                 @"owner_id": [model valueForKeyPath:@"publisher.userID"],
                                 @"topic_id": [model valueForKey:@"topic_id"],
                                 @"is_ask": @(self.is_ask)}
                                   .mutableCopy;
    NSInteger commentID = [[model valueForKey:@"commentID"] integerValue];
    if (commentID > 0) {
        dic[@"review_id"] = @(commentID);
    }
    /// 帖子点赞
    NSString *topicPraisePath = @"v2/user_praise_topic";
    /// 评论点赞
    NSString *recommendPraisePath = @"v2/user_praise_review";
    if (dic[@"review_id"]) {
        [IMYUGCRequest postReviewPraiseEvent:has_praise num:MAX(0, has_praise? (praise_num + 1): (praise_num -1)) reviewId:[dic[@"review_id"] integerValue] otherInfo:@{@"userid":[viewModel valueForKey:@"topicUserID"]?:@""}];
    }
    @weakify(self);
    [IMYUGCRequest requestPostPath:dic[@"review_id"] == nil ? topicPraisePath : recommendPraisePath host:circle_seeyouyima_com params:dic completion:^(id<IMYHTTPResponse>  _Nonnull resData, NSError * _Nonnull error) {
        if (error) {
            @strongify(self);
//            [model setValue:@(!has_praise) forKey:@"has_praise"];
//            [model setValue:@(praise_num) forKey:@"praise_num"];
        } else {
            [model setValue:@(has_praise) forKey:@"has_praise"];
            if (has_praise) {
                [model setValue:@(praise_num + 1) forKey:@"praise_num"];
            } else {
                [model setValue:@(praise_num - 1) forKey:@"praise_num"];
            }
            NSNumber *biPraise_num = [model valueForKey:@"praise_num"];
            if (!biPraise_num) {
                biPraise_num = @0;
            }
            if (from_community_home && dic[@"review_id"] != nil && has_praise) {
                NSString *eventName = @"ttq_tzxqy_dzpl";
                if ([viewModel isKindOfClass:NSClassFromString(@"IMYQATopicReferenceViewModel")]) {
                    eventName = @"ttq_tzplxqy_dzpl";
                }
                [TTQCommonHelp GAEventForEventWithName:eventName action:2];//点赞埋点
            }
            NSMutableDictionary *gaDic = [@{
                @"action":@2,
                @"info_type":@12,
                @"info_id":[model valueForKey:@"topic_id"]?:@"",
                @"fuid":[viewModel valueForKey:@"topicUserID"]?:@"",
                @"public_type":(has_praise?@21:@22),
                @"public_key":@1,
                @"comment_uid":[model valueForKeyPath:@"publisher.userID"]?:@"",
                @"comment_id":[model valueForKey:@"commentID"]?:@"",
                @"event":@"dsq_nrxqy_dz",
                @"public_info":@"内容详情页",
                @"interact_num":biPraise_num,
                @"is_on_period":@([IMYUGCEventHelper userIsInPhysiological]),@"redirect_url":[IMYUGCEventHelper currentPageRedirectUrl]
            } mutableCopy];
            if ([viewModel isKindOfClass:IMYQATopicReferenceViewModel.class]) {
                IMYQATopicReferenceViewModel *referenceViewModel = (IMYQATopicReferenceViewModel *)viewModel;
                gaDic[@"fuid"] = @(referenceViewModel.referenced.publisher.userID);
                gaDic[@"public_info"] = @"回答帖的评论";
            }
            [IMYGAEventHelper postWithPath:@"event" params:gaDic headers:nil completed:nil];

        }
    }];
}

- (void)judgeBlockedWithUserInfo:(TTQTopicCurrentUserInfo *)userInfo {
  //禁言处理
  NSString *message =
      userInfo.error == 3
          ? @"违反圈规被禁言"
          : (userInfo.error == 2 ? @"违反圈规被封号" : @"违反圈规被禁言");
  [UIAlertController
      imy_showAlertViewWithTitle:message
                         message:@"可以到\"帮助与反馈\"里申诉反馈"
               cancelButtonTitle:IMYString(@"取消")
               otherButtonTitles:@[ IMYString(@"去反馈") ]
                         handler:^(UIAlertController *alertController,
                                   NSInteger buttonIndex) {
                           if (buttonIndex == 1) {
                               [[IMYURIManager shareURIManager] runActionWithString:@"meiyou:///qiyu/chat?params=eyJncm91cElkIjogNDgwODE1ODc4fQ=="];
                           } else {
                             [self resignFirstResponder];
                           }
                         }];
}

- (BOOL)judgeBlocked:(TTQTopicCurrentUserInfo *)userInfo {
  //封号处理
    if (userInfo.error == 2) {
        [self judgeBlockedWithUserInfo:userInfo];
        return YES;
    }
    return NO;
}

- (BOOL)shouldPostPraiseRequestByModel:(NSObject *)model {
    BOOL shouldRequest = NO;
    NSDate *lastPraiseDate = [model valueForKey:@"lastTimePraiseDate"];
    NSTimeInterval interval = -[lastPraiseDate timeIntervalSinceNow];
    if (interval > 1.0 || lastPraiseDate == nil) {
        shouldRequest = YES;
    }
    [model setValue:[NSDate date] forKey:@"lastTimePraiseDate"];
    return shouldRequest;
}

- (void)setupPraiseButton:(NSObject *)model {
    NSInteger praise_num = [[model valueForKey:@"praise_num"] integerValue];
    BOOL has_praise = [[model valueForKey:@"has_praise"] boolValue];
    NSString *modelString = nil;
    if (praise_num == 0) {
        modelString = [IMYCKFeedsHelper praiseStyleExp] ? @"感谢": @"赞";
    } else {
        modelString = [NSString stringShowWithCount:praise_num];
    }
    [self updatePraise:has_praise count:praise_num];
    /// 这里是手动计算宽度
    CGFloat width = [IMYQAPraiseView getTagWidthWithText:modelString font:self.textLabel.font];
    CGFloat lastRight = self.imy_right;
    self.imy_width = width + 22;
    self.imy_right = lastRight;
}

+ (CGFloat)getTagWidthWithText:(NSString *)text font:(UIFont *)font {
    CGRect rect = [text boundingRectWithSize:CGSizeMake(MAXFLOAT, 18)
                                     options:NSStringDrawingUsesLineFragmentOrigin
                                  attributes:@{NSFontAttributeName: font}
                                     context:nil];
    return ceil(rect.size.width);
}

@end
