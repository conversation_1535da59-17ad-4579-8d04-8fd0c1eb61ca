//
//  IMYQAAnswerDetailViewController.m
//  IMYAnswer
//
//  Created by 林云峰 on 2025/3/5.
//

#import "IMYQAAnswerDetailViewController.h"
#import <IMYBaseKit/IMYBaseKit.h>
#import <IMYUGC/IMYCKBottomReplyBar.h>
#import "IMYQATopicReferenceViewModel.h"
#import "TTQTopicDetailPublisherTitleView.h"
#import "TTQRefreshBackNormalFooter.h"
#import <IMYCommonKit/IMYCRefreshHeader.h>
#import "IMYQAAnswerCommentAdapter.h"
#import "IMYQACommentHeaderAdapter.h"
#import "IMYQAAnswerHeaderView.h"
#import <IMYCommonKit/IMYCKFollowButton.h>
#import "TTQCheckService.h"
#import <IMYUGC/IMYCKInputWithStickerView.h>
#import <IMYAccount/IMYAccountCheckService.h>
#import <IMYAccount/IMYAccountServerURL.h>
#import "TTQMessageDetailViewModel.h"
#import "TTQABTestConfig.h"
#import "TTQCommentContentCacheManager.h"
#import "TTQRefreshAutoNormalFooter.h"
#import "IMYQAMessageDetailViewModel.h"
#import "TTQShareView.h"
#import "TTQShareView+TTQDetail.h"
#import "TTQDetailHelper.h"
#import <IMYBaseKit/IMYCoolShareSheet.h>
#import "TTQTopicTableView.h"
#import <IMYUGC/IMYUGCRequest.h>
#import <IMYUGC/IMYUGCEventHelper.h>
#import <IMYUGC/IMYUGCImageObject.h>
#import <IMYTTQ/MBProgressHUD+TTQ.h>
#import <IMYBaseKit/IMYVKStickerManager.h>
#if __has_include(<IMYEBPublic/IMYEBYoubiTaskManager.h>)
#import <IMYEBPublic/IMYEBYoubiTaskManager.h>
#endif

@interface IMYQAAnswerDetailViewController () <UITableViewDelegate, UITableViewDataSource, IMYREasyInputViewDelegate>
@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, strong) IMYTableViewAdapter *tableViewAdapter;
@property (nonatomic, strong) IMYQACommentHeaderAdapter *commentHeaderAdapter;
@property (nonatomic, strong) IMYQAAnswerCommentAdapter *commentAdapter;
/// 主楼信息
@property (nonatomic, strong) IMYQAAnswerHeaderView *mainContentView;
@property (nonatomic, strong) IMYCaptionViewV2 *captionView;
/// 自定义导航栏
@property (nonatomic, strong) UIView *customNavigationBar;
@property (nonatomic, strong) UIView *bottomLineView;
//导航栏右边按钮
@property (nonatomic, strong) IMYTouchEXButton *topRightButton;
@property (nonatomic, strong) IMYCKBottomReplyBar *bottomBar;
@property (nonatomic, strong) TTQTopicDetailPublisherTitleView *publisherInfoView; //titleview
@property (nonatomic, strong) IMYQATopicReferenceViewModel *viewModel;

@property(nonatomic, strong) MBProgressHUD *progressHUD; //图片上传进度展示

/**
 用于在弱网环境下，防止关注操作反复请求
 */
@property (nonatomic, assign) BOOL allowFollowAction;
@property (nonatomic, strong) IMYCKInputWithStickerView *inputContentsView;
@property (nonatomic, strong) NSIndexPath *locationIndexPath;

@property (nonatomic, assign) CGPoint preContentOffset;// 当前内容偏移量
@property (nonatomic, assign) CGPoint preCommentOffset;// 当前评论偏移量

@property (nonatomic, strong) UIView *biCommentView;

@end

@implementation IMYQAAnswerDetailViewController

- (instancetype)initWithViewModel:(IMYQATopicReferenceViewModel *)viewModel {
    self = [super init];
    if (self) {
        self.viewModel = viewModel;
    }
    return self;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    self.allowFollowAction = YES;
    [self initViews];
    [self request:YES];
    [self addBICommentView];

    // Do any additional setup after loading the view.
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
    if (self.viewModel.referenced) {
        [[NSNotificationCenter defaultCenter] postNotificationName:@"kNewsDataRefreshNotification" object:nil userInfo:@{@"type":@(14),@"itemId":@(self.viewModel.referenced_id),@"comments":@(self.viewModel.referenced.referenced_num),@"isPraise":@(self.viewModel.referenced.has_praise),@"userid":@(self.viewModel.referenced.publisher.userID),@"praise_num":@(self.viewModel.referenced.praise_num),@"is_qa":@YES}];

    }

}

#pragma mark -

- (void)request:(BOOL)isRefresh {
    NSInteger type = isRefresh?0:1;
    [[[self.viewModel requestRemoteDataForType:type params:@{@"requestRemoteDataForType":@(type)}] deliverOnMainThread] subscribeNext:^(id  _Nullable x)  {
        [self finishedRequest:nil isRefresh:isRefresh];
    } error:^(NSError * _Nullable error) {
        [self finishedRequest:error isRefresh:isRefresh];
    }];
}

- (void)finishedRequest:(NSError *)error isRefresh:(BOOL)isRefresh {
    [self.tableView imy_headerEndRefreshing];
    [self.tableView imy_footerEndRefreshing];
    if (error) {
        if (self.viewModel.referenced) {
            self.captionView.state = IMYCaptionViewStateHidden;
            //ios7，ios8的bug。第一次还是会返回有网络。所以用特定的状态-1005来判断
            if (isRefresh) {
                [self updateTableHeader];
                [self.commentAdapter updateViewModel:self.viewModel];
                [self setupTopRightShareButton];
            }
            [self.bottomBar.commentButton updateComment:self.viewModel.referenced.referenced_num];
            [self.bottomBar.praiseButton updatePraise:self.viewModel.referenced.has_praise count:self.viewModel.referenced.praise_num];
        } else {
            if ([IMYNetState networkEnable]) {
                [self.captionView setTitle:MT_Request_Retry andState:IMYCaptionViewStateRetry];
            } else {
                [self.captionView setTitle:MT_Request_NoNet andState:IMYCaptionViewStateRetry];
            }
        }
        [self.tableView imy_endRefreshAndLoadMore];
    } else {
        if (self.viewModel.referenced) {
            self.captionView.state = IMYCaptionViewStateHidden;
            if (self.viewModel.referenced.is_deleted) {
                [IMYQAMessageDetailViewModel deleteMessageByReviewId:self.viewModel.referenced_id topic_id:self.viewModel.topic_id];
            }
            if (isRefresh) {
                [self updateTableHeader];
                [self.commentAdapter updateViewModel:self.viewModel];
                [self setupTopRightShareButton];
                
                [self.bottomBar.commentButton updateComment:self.viewModel.referenced.referenced_num];
                [self.bottomBar.praiseButton updatePraise:self.viewModel.referenced.has_praise count:self.viewModel.referenced.praise_num];
            }
        }
        
        [self.commentAdapter loadItems:self.viewModel.dataSource];
        [self.tableView reloadData];
        if (self.viewModel.locate_to_comment) {
            [self locateToCommentAreaWithAnimation:NO];
            self.viewModel.locate_to_comment = NO;
        }
        if (!self.viewModel.hasMore) {
            if (isRefresh) {
                [self.tableView imy_headerEndRefreshing];
            }
            [self.tableView imy_endFooterRefreshAndNoMore];
        }
        else {
            [self.tableView imy_endRefreshAndLoadMore];
        }
        [self.commentHeaderAdapter updateCount:self.viewModel.referenced.referenced_num];
    }
    if (error) {
        if (![IMYNetState networkEnable]) {
            [UIWindow imy_showTextHUD:MT_Request_NoNetToast];
        } else if (error.code %1000 == 404) {
            /// 帖子被删了
            [UIWindow imy_showHUDwithNetworkError:error andResponseObject:error.af_responseData];
            self.captionView.state = IMYCaptionViewStateNoResult;
            [self imy_pop:YES];
        } else if (error.code % 1000 == 422) {
            self.captionView.retryBlock = nil;
            [self.captionView setTitle:IMYString(@"该楼层已被删除") forState:IMYCaptionViewStateNoResult];
            self.captionView.state = IMYCaptionViewStateNoResult;
            [UIWindow imy_showTextHUD:IMYString(@"该楼层已被删除")];
            self.viewModel.becomeFirstResponder = NO;
            [self imy_pop:YES];
            [IMYQAMessageDetailViewModel deleteMessageByReviewId:self.viewModel.referenced_id topic_id:self.viewModel.topic_id];
        } else if (error.code % 1000 == 400) {
            [UIWindow imy_showHUDwithNetworkError:error andResponseObject:error.af_responseData];
            self.captionView.state = IMYCaptionViewStateHidden;
        } else {
            [UIWindow imy_showTextHUD:IMYString(@"网络缓慢，请稍后再试")];
        }

    }
    self.tableView.mj_footer.hidden = self.viewModel.dataSource.count == 0;
}

#pragma mark -

- (void)updateTableHeader {
    if (!self.mainContentView) {
        self.mainContentView = [[IMYQAAnswerHeaderView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, 200)];
        @weakify(self);
        [self.mainContentView setTapFollowAction:^(UIButton * _Nonnull btn) {
            @strongify(self);
            [self followAction:btn];
        }];
        [self.mainContentView setArticleTapAction:^{
            @strongify(self);
            IMYURI *uri = [IMYURI uriWithURIString:self.viewModel.referenced.topic.redirect_url];
            [uri appendingParams:@{@"entrance":@([self biEntrance]),@"position":@([self biPosition])}];
            [[IMYURIManager sharedInstance] runActionWithURI:uri];
        }];
    }
    [self.mainContentView updateWithData:self.viewModel.referenced];
    self.tableView.tableHeaderView = self.mainContentView;
    
    [self updatePublisherInfoView];
}

#pragma mark - 上下拉

- (void)addRefreshCompont {
    @weakify(self);
//    [self]
    IMYCRefreshHeader *refreshHeader = [IMYCRefreshHeader headerWithRefreshingBlock:^{
        @strongify(self);
        [self.viewModel.requestRemoteDataCommand cancel];
        if (![IMYNetState networkEnable]) {
            [self.tableView imy_headerEndRefreshing];
            [UIWindow imy_showTextHUD:IMYString(@"网络不见了，请检查网络")];
            return;
        }
        // 发起请求
        [self request:YES];
    }];
    self.tableView.mj_header = refreshHeader;

    [self ttq_addFooter];
}

- (void)ttq_addFooter {
    if (!self.tableView.mj_footer) {
        @weakify(self);
        [self.tableView imy_addFooterWithMoreBlock:^{
            @strongify(self);
            if (![IMYNetState networkEnable]) {
                [self.tableView imy_footerEndRefreshing];
            } else {
                [self request:NO];
            }
        }];
        if ([self.tableView.mj_footer isKindOfClass:MJRefreshBackStateFooter.class]) {
            [(MJRefreshBackStateFooter *)_tableView.mj_footer setTitle:IMYString(@"上拉加载更多") forState:MJRefreshStatePulling];
            [(MJRefreshBackStateFooter *)_tableView.mj_footer setTitle:IMYString(@"正在加载更多...") forState:MJRefreshStateRefreshing];
            [(MJRefreshBackStateFooter *)_tableView.mj_footer setTitle:IMYString(@"已显示全部") forState:MJRefreshStateNoMoreData];
            [((MJRefreshBackNormalFooter *)_tableView.mj_footer).arrowView setImage:nil];
        }
    }

}

#pragma mark - 作者/关注

- (void)userInfoAction:(TTQPublisherModel *)data {
    if (data.error == 1) {
        [UIView imy_showTextHUD:kStatusText_UserAnonymous];
    } else if (data.error > 1) {
        [UIView imy_showTextHUD:kStatusText_homePageNotOpen];
    } else {
        NSInteger userId = data.userID;
        // 女人通广告位的柚+文章携带page参数
        IMYURI *uri = [IMYURI uriWithPath:@"user/dynamic"
                                   params:@{ @"userID": @(userId)}
                                                 info:nil];
        [[IMYURIManager shareURIManager] runActionWithURI:uri];

    }
    [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"dsq_nrxqy_tx",@"action":@2,@"comment_id":@(self.viewModel.referenced_id),@"info_type":@12,@"info_id":@(self.viewModel.topic_id),@"fuid":@(self.viewModel.referenced.publisher.userID),@"public_type":@1} headers:nil completed:nil];

}

- (void)updateFollowView {
    BOOL isSHow = self.viewModel.referenced.publisher.error == 0 && self.viewModel.referenced.publisher.userID != [[IMYPublicAppHelper shareAppHelper].userid integerValue];
    if ([self.tableView.tableHeaderView isKindOfClass:IMYQAAnswerHeaderView.class]) {
        [self.mainContentView updatefollowButtonShow:isSHow status:self.viewModel.referenced.publisher.is_followed];
    }

}

#pragma mark - UIScrollView delegate

- (void)scrollViewDidScroll:(UIScrollView *)scrollView {
    if (scrollView.contentOffset.y >= 52) {
        [self.publisherInfoView.containView pop_removeAnimationForKey:@"hiden_publish"];
        if (![self.publisherInfoView.containView pop_animationForKey:@"show_publish"] && self.publisherInfoView.containView.alpha < 1.0) {
            POPBasicAnimation *alphaAnim = [POPBasicAnimation animationWithPropertyNamed:kPOPViewAlpha];
            alphaAnim.toValue = @(1.0);
            alphaAnim.duration = 0.25 * (1.0 - self.publisherInfoView.containView.alpha);
            [self.publisherInfoView.containView pop_addAnimation:alphaAnim forKey:@"show_publish"];
        }
    } else {
        [self.publisherInfoView.containView pop_removeAnimationForKey:@"show_publish"];
        if (![self.publisherInfoView.containView pop_animationForKey:@"hiden_publish"] && self.publisherInfoView.containView.alpha > 0.0) {
            POPBasicAnimation *alphaAnim = [POPBasicAnimation animationWithPropertyNamed:kPOPViewAlpha];
            alphaAnim.toValue = @(0.0);
            alphaAnim.duration = 0.25 * self.publisherInfoView.containView.alpha;
            [self.publisherInfoView.containView pop_addAnimation:alphaAnim forKey:@"hiden_publish"];
        }
    }
    [self updateBICommentView];
    [self changeNavigaitonBarUIWithTableViewOffset:scrollView.contentOffset.y];
}

- (void)changeNavigaitonBarUIWithTableViewOffset:(CGFloat)offsetY {
    if (offsetY > 0) {
        if (offsetY < 8) {
            CGFloat alpha = offsetY / 8.0;
            self.bottomLineView.alpha = alpha;
        } else {
            self.bottomLineView.alpha = 1;
        }
    } else {
        self.bottomLineView.alpha = 0;
    }
}

// MARK: - IMYREasyInputViewDelegate
- (void)inputViewWillResignFirstResponder:(IMYREasyInputView *)inputView
                           keyboardHeight:(CGFloat)height
                        animationDuration:(CGFloat)duration
                                   option:(UIViewAnimationOptions)option {
    CGRect rect = [inputView convertRect:inputView.frame toView:[UIApplication sharedApplication].keyWindow];
    if (rect.origin.x != 0) {
        return;
    }
    [self postHandleInputViewResign];
}

- (void)postHandleInputViewResign {
    // 键盘隐藏，处理保存评论数据的逻辑
    if (self.viewModel.selectedReplyIndex) {
        TTQCommentModel *comment = [self.viewModel.dataSource imy_objectAtIndex:self.viewModel.selectedReplyIndex.section];
        if ([comment isKindOfClass:TTQCommentModel.class]) {
            [[TTQCommentContentCacheManager sharedManager] setContent:[self inputContentFromTextView] withCommentID:comment.commentID];
        }
    } else {
        NSInteger itemId = self.viewModel.topic_id;
        [[TTQCommentContentCacheManager sharedManager] setContent:[self inputContentFromTextView] withTopicID:itemId];
    }
}

#pragma mark - 回复
- (void)replyActionAtIndexPath:(NSIndexPath *)indexPath {
    [self replyActionAtIndexPath:indexPath publicKey:1];
}
- (void)replyActionAtIndexPath:(NSIndexPath *)indexPath publicKey:(NSInteger)publicKey{
    self.viewModel.bi_replyPublicKey = publicKey;
    if ([self judgeBlocked]) {
        return;
    }
    if ([TTQABTestConfig disableInteractByType:TTQ_Interact_type_comment]) {
        return;
    }

    self.viewModel.selectedReplyIndex = indexPath;
    NSString *cachedContent = nil;
    BOOL shouldReply = YES;
    if (indexPath == nil) {
        self.viewModel.inputDefaultText = nil;
        cachedContent = [[TTQCommentContentCacheManager sharedManager] contentWithTopicID:self.viewModel.topic_id];
    } else {
        TTQCommentModel *comment = [self.viewModel.dataSource imy_objectAtIndex:indexPath.section];
        if (comment) {
            self.viewModel.inputDefaultText = [NSString stringWithFormat:@"@%@：", comment.publisher.screen_name];
            cachedContent = [[TTQCommentContentCacheManager sharedManager] contentWithCommentID:comment.commentID];
        } else {
            shouldReply = NO;
        }
    }
    if (shouldReply) {
        [self.inputContentsView restPose];
        if (indexPath) {
            self.inputContentsView.associatedTableView = self.tableView;
            self.inputContentsView.scrollIndexPath = [self.commentAdapter.module feedsIndexPathByModulePath:indexPath];
        }
        if (cachedContent.length) {
            NSAttributedString *attributeStrToShow = [IMYREmoticonManager decodeEmojiText:cachedContent attributes:self.inputContentsView.textView.internalTextView.typingAttributes];
            self.inputContentsView.textView.selectedRange = NSMakeRange(attributeStrToShow.length,0);
            self.inputContentsView.textView.internalTextView.attributedText = attributeStrToShow;
        } else {
            self.inputContentsView.textView.internalTextView.attributedText  = nil;
        }
        if (![self.inputContentsView.textView isFirstResponder]) {
            [self.inputContentsView.textView becomeFirstResponder];
        }
        [self.inputContentsView.textView refreshHeight];
    }
    [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"dsq_nrxqypl_plqpl",@"action":@2,@"info_type":@12,@"info_id":@(self.viewModel.topic_id),@"fuid":@(self.viewModel.referenced.publisher.userID),@"comment_id":@(self.viewModel.referenced_id)} headers:nil completed:nil];

}


- (void)postToRemoteWithImageUrls:(nullable NSArray<NSString *> *)imageUrls {
    @weakify(self);
    ProgressCallback progressBlock = nil;
    if (imageUrls.count == 0) {
        [self hidenProgressHUD];
        [UIWindow imy_showLoadingHUDWithText:IMYString(@"发送中")];
    } else {
        [self setProcessHUD:0.9];
        progressBlock = ^(int64_t completedUints, int64_t totalUnits) {
            imy_asyncMainBlock(^{
                @strongify(self);
                [self setProcessHUD:0.1 * completedUints / (double)totalUnits + 0.9f];
            });
        };
    }
    __block NSMutableDictionary *params = [[NSMutableDictionary alloc] init];
    params[@"content"] = [[self inputContentFromTextView] imy_trimString];
    if (imageUrls.count > 0) {
        params[@"images"] = imageUrls;
        /// 有图片的才处理
        NSMutableDictionary *imageDic = [NSMutableDictionary dictionaryWithCapacity:1];
        IMYUGCImageObject *image = [self.inputContentsView imageModel];
        IMYVKStickerItemModel *obj = image.userInfo;
        if ([image.userInfo isKindOfClass:NSClassFromString(@"IMYVKStickerItemModel")]) {
            /// 贴纸
            imageDic[@"url"] = obj.url;
            imageDic[@"referer"] = @1;
            imageDic[@"referer_id"] = @(obj.stickerId);
        } else {
            imageDic[@"url"] = [params[@"images"] firstObject];
            imageDic[@"referer"] = @0;
        }
        params[@"images_v2"] = @[imageDic];
    }
    [[[self.viewModel.replyCommand execute: @[params]] deliverOnMainThread] subscribeNext:^(id<IMYHTTPResponse> response) {
        @strongify(self);
        IMYUGCImageObject *imageObj = [self.inputContentsView imageModel];
        NSString *content = self.inputContentsView.textView.text;
        self.viewModel.inputDefaultText = nil;
        self.inputContentsView.textView.text = nil;
        imy_asyncMainExecuteBlock(^{
            [self.inputContentsView clearPhoto];
            [self.inputContentsView restPose];
        });

        
        // 清除缓存记录
        // referenced_id 需要使用 NSInteger 接收，详情页帖子评论该值为-1
        NSInteger commentID = [params[@"referenced_id"] integerValue];
        NSUInteger topicID = [params[@"topic_id"] integerValue];

        self.viewModel.selectedReplyIndex = nil;
        [self hidenProgressHUD];
        [UIWindow imy_showTextHUD:IMYString(@"回复成功")];
        [self finishedRequest:nil isRefresh:NO];
        
        if (imageObj && [imageObj.userInfo isKindOfClass:IMYVKStickerItemModel.class]) {
            IMYVKStickerItemModel *sticker = imageObj.userInfo;
            NSString *typeString = [NSString stringWithFormat:@"%@_%ld",sticker.groupName, sticker.stickerId];
            [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"dsq_nrxqypl_fsemoji",@"action":@2,@"public_type":typeString} headers:nil completed:nil];
        }
#if __has_include(<IMYEBPublic/IMYEBYoubiTaskManager.h>)
        [[IMYEBYoubiTaskManager shareManager] oprationTaskFinishedWithKey:@"community_review" uploadParams:nil];
#endif

    }
        error:^(NSError *error) {
            @strongify(self);
            NSString *postFaildMsg = [[NSString alloc] initWithData:error.af_responseData encoding:NSUTF8StringEncoding];
            NSString *stringError = [NSString stringWithFormat:@"接口报错:%@",error.localizedDescription];
            stringError = imy_isNotEmptyString(postFaildMsg)?postFaildMsg:stringError;
            NSMutableDictionary *errorData = [NSMutableDictionary dictionary];
            if (error.userInfo) {
                [errorData addEntriesFromDictionary:error.userInfo];
            }
            if (params) {
                errorData[@"requestParams"] = params;
            }
            if (error.af_responseData) {
                errorData[@"responseData"] = [error.af_responseData responseString];
            }

            [IMYErrorTraces postWithType:IMYErrorTraceTypeOthers pageName:NSStringFromClass([UIViewController imy_currentViewControlloer].class) category:IMYErrorTraceCategoryCommunity message:@"问答评论失败" detail:errorData];

            [self hidenProgressHUD];
            [UIWindow imy_hideHUD];
            if (error.code % 1000 == 400) {
                [UIWindow imy_showHUDwithNetworkError:error andResponseObject:error.af_responseData];
                [self hideKeyboard];//清除输入框内容
            } else if (error.code == kPhoneDubiousErrorCode || error.code == kPhoneStolenErrorCode) {
                [self hideKeyboard];
                imy_asyncMainBlock(0.15, ^{
                    NSInteger type = (error.code == kPhoneStolenErrorCode) ? 1 : 2;
                    [[IMYURIManager shareURIManager] runActionWithPath:@"account/phone/verify" params:@{ @"type": @(type) } info:nil];
                });
            } else if (error.code % 1000 == 422) {
                NSString *message = nil;
                IMYWebMessageModel *failModel = [UIWindow imy_showHUDwithNetworkError:error andResponseObject:error.af_responseData];
                if (imy_isNotEmptyString(failModel.message)) {
                    message = failModel.message;
                }

                [UIWindow imy_showTextHUD:IMYString(message)];

                NSUInteger commentID = [params[@"referenced_id"] integerValue];
                if (commentID) {
                    [TTQMessageDetailViewModel deleteMessageByReviewId:commentID topic_id:self.viewModel.topic_id postNotification:NO];
                }

            } else {
                if (error.code != ********) {
                    [self hideKeyboard];
                }
                
                IMYWebMessageModel *failModel = [UIWindow imy_showHUDwithNetworkError:error andResponseObject:error.af_responseData];
                // code = 16/11的情况在底层未做处理，业务端处理
                if (failModel.code == 16 || failModel.code == 11) {
                    if (failModel.message.length > 0) {
                        [UIView imy_showTextHUD:failModel.message];
                    } else {
                        [UIView imy_showHUDwithNetworkError:error];
                    }
                }
            }
        }];
}


- (BOOL)loginActicon {
    if (![IMYPublicAppHelper shareAppHelper].hasLogin) {
        [UIWindow imy_showTextHUD:kStatusText_unLogin];
        [[IMYURIManager shareURIManager] runActionWithString:@"login"];
        return NO;
    } else {
        return YES;
    }
}
/**
 是否加圈判断
 */
- (BOOL)canReply {
    return self.viewModel.canReply;
}

/**
 是否被封号
 */
- (BOOL)canAccess {
    return self.viewModel.canAccess;
}

- (BOOL)judgeBlocked {
  //禁言处理
    if (self.viewModel.currentUserInfo.error == 2 || self.viewModel.currentUserInfo.error == 3) {
        [self judgeBlockedWithUserInfo:self.viewModel.currentUserInfo];
        return YES;
    }
    return NO;
}

- (void)judgeBlockedWithUserInfo:(TTQTopicCurrentUserInfo *)userInfo {
  //禁言处理
  NSString *message =
      userInfo.error == 3
          ? @"违反圈规被禁言"
          : (userInfo.error == 2 ? @"违反圈规被封号" : @"违反圈规被禁言");
  [UIAlertController
      imy_showAlertViewWithTitle:message
                         message:@"可以到\"帮助与反馈\"里申诉反馈"
               cancelButtonTitle:IMYString(@"取消")
               otherButtonTitles:@[ IMYString(@"去反馈") ]
                         handler:^(UIAlertController *alertController,
                                   NSInteger buttonIndex) {
                           if (buttonIndex == 1) {
                               [[IMYURIManager shareURIManager] runActionWithString:@"meiyou:///qiyu/chat?params=eyJncm91cElkIjogNDgwODE1ODc4fQ=="];
                           } else {
                             [self resignFirstResponder];
                           }
                         }];
}

- (NSString *)inputContentFromTextView {
    NSAttributedString *attributedText = self.inputContentsView.textView.internalTextView.attributedText;
    if (attributedText) {
        return [IMYREmoticonManager encodeEmojiText:attributedText];
    }
    return nil;
}

#pragma mark - 滚动定位

- (void)locateToCommentAreaWithAnimation:(BOOL)animation {
    imy_asyncMainBlock(^{
        CGFloat headY = [self firstSectionHeaderY];
        CGFloat maxOffset = MIN(MAX(0, self.tableView.contentSize.height - self.tableView.imy_height - self.tableView.contentInset.top),headY);
        [self.tableView setContentOffset:CGPointMake(0, maxOffset) animated:animation];
    });
}

- (CGFloat)firstSectionHeaderY {
    CGFloat sectionHeaderY = 0;
    UIView *headView = (UIView *)self.tableView.tableHeaderView;
    if ([self.tableView numberOfSections] > 0) {
        sectionHeaderY = [self.tableView rectForHeaderInSection:0].origin.y + 8;
    } else {
        sectionHeaderY = headView.imy_height;
    }
    return sectionHeaderY;
}


#pragma mark - 键盘

- (void)initInputContentView {
    self.inputContentsView = [[IMYCKInputWithStickerView alloc] initPhotoMultitudeInnerWithFrame:CGRectMake(0, 0, self.view.imy_width, 87)];
    //    self.inputContentsView.enablePostArticle = YES;
    self.inputContentsView.delegate = self;
    [self.inputContentsView resetKeyboardWithShowSticker:YES];
    [self.inputContentsView showCameraButton];
    self.inputContentsView.attachedView = self.view;
    [self.inputContentsView.sendButton addTarget:self action:@selector(inputViewSend) forControlEvents:UIControlEventTouchUpInside];
    self.inputContentsView.inputBottomForNone = self.view.imy_height + self.inputContentsView.imy_height + 100;
    self.inputContentsView.imy_bottom = self.inputContentsView.inputBottomForNone;
    @weakify(self);
    [[RACObserve(self.viewModel, inputDefaultText) deliverOnMainThread] subscribeNext:^(NSString *text) {
        @strongify(self);
        if (text) {
//            self.inputContentsView.textView.text = nil;
            self.inputContentsView.textView.placeholder = text;
        } else {
            if (self.viewModel.referenced.publisher.screen_name) {
                self.viewModel.inputDefaultText = [NSString stringWithFormat:@"@%@：", self.viewModel.referenced.publisher.screen_name];
            }
        }
    }];
    self.inputContentsView.biCameraButtonClickBlock = ^{
        @strongify(self);
        NSDictionary *dict = @{@"event":@"dsq_nrxqy_xztp",
                               @"action":@2,
                               @"public_key":@1,
                               @"comment_id":@(self.viewModel.referenced_id),
                               @"public_type":@2};
        [IMYGAEventHelper postWithPath:@"event" params:dict headers:nil completed:nil];
    };
}

- (void)inputViewSend {
    [self inputViewSendWithKeyboard:NO];
}

- (void)inputViewSendWithKeyboard:(BOOL)keyboard {
    NSMutableDictionary *plParams = [@{@"event":@"dsq_nrxqy_djfs",@"action":@2,@"info_type":@12,@"info_id":@(self.viewModel.topic_id),@"fuid":@(self.viewModel.referenced.publisher.userID),@"comment_id":@(self.viewModel.referenced.commentID)} mutableCopy];
    if (keyboard) {
        plParams[@"public_info"] = @"输入法发送";
    }

    [IMYGAEventHelper postWithPath:@"event" params:plParams headers:nil completed:nil];

    // 点击发送，进行判断是否登录、设置昵称、绑定手机号、加入圈子、封号
    if (![self loginActicon]) {
        return;
    }
    
    if ([NSString imy_isEmptyString:[IMYPublicAppHelper shareAppHelper].nickName]) {
        [UIWindow imy_showTextHUD:IMYString(@"请先设置你的昵称哦~")];
        [[IMYURIManager shareURIManager] runActionWithString:@"user/nickname"];
        return;
    }
    
    if ([IMYAccountCheckService checkPhoneShouldBeBindedWithCompletion:nil]) {
        return;
    }
        
//    if (![self canAccess]) {
//        [UIWindow imy_showTextHUD:IMYString(@"您因违反她她圈圈规，已被禁止该操作！")];
//        return;
//    }
    BOOL blocked = [self judgeBlocked];
    if (blocked) {
        return;
    }
    if ([IMYPublicAppHelper shareAppHelper].cannotAccess) {
        @weakify(self);
        [UIAlertController imy_showAlertViewWithTitle:IMYString(@"来自 美柚助手 通知")
                                        message:IMYString(@"你因违反她她圈圈规，已被封号！")
                              cancelButtonTitle:IMYString(@"我知道了")
                              otherButtonTitles:nil
                                        handler:^(UIAlertController *alertController, NSInteger buttonIndex) {
                                            @strongify(self);
                                            if (buttonIndex == 0) {
                                                [[IMYURIManager shareURIManager] runActionWithPath:@"msg/system" params:nil info:nil];
                                            }
                                        }];
        [self.inputContentsView restPose];
        return;
    }
    
    if (![IMYNetState networkEnable]) {
        [UIWindow imy_showTextHUD:MT_Request_NoNetToast];
        return;
    }

    IMYCKInputWithStickerView *inputView = self.inputContentsView;
    NSString *text = [self.inputContentsView.textView.text imy_trimString];
    if ([NSString imy_isEmptyString:text]) {
        BOOL shouldStop = YES;
        /// 没内容也没图片的
        if (inputView.imageModel) {
            shouldStop = NO;
        }
        if (shouldStop) {
            /// 详情页允许空回复，
            inputView.textView.text = text;
            [UIWindow imy_showTextHUD:IMYString(@"您的回复为空，多写一点吧")];
            return;
        }
    }

    if (!inputView.imageModel) {
        [self postToRemoteWithImageUrls:nil];
    }else{
        IMYCKInputWithStickerView *inputView = self.inputContentsView;
        [self postImageModelWithInputView:inputView];
    }
}

- (void)postImageModelWithInputView:(IMYCKInputWithStickerView *)inputView{
    if ([inputView isPostSticker]) {
        /// 表情就直接传图
        [UIWindow imy_showLoadingHUDWithText:IMYString(@"正在发送...")];
        [self postToRemoteWithImageUrls:@[inputView.imageModel.url]];
    } else {
        IMYUGCImageObject *uploadImageObject = inputView.imageModel;
        if (uploadImageObject.url) {
            [self postToRemoteWithImageUrls:@[uploadImageObject.url]];
            return;
        }
        [self.progressHUD showAnimated:YES];
        [self setProcessHUD:0.2];
        id<IMYOSSFileObject> fileObject = [uploadImageObject getImageOSSFileObject];
        fileObject.querys = @{@"scene":@14};
        @weakify(self,uploadImageObject);
        __block NSError *_error = nil;
        __block NSDate *uploadStartTime = [NSDate date];
        [[IMYOSS defaultUploader] uploadAllObjects:@[fileObject] progressBlock:^(id<IMYOSSFileObject> _Nonnull object, double progress) {
            @strongify(self);
            self.progressHUD.progress = 0.2 + progress * 0.7;
        } complatedBlock:^(id<IMYOSSFileObject> _Nonnull object, NSError *_Nullable error) {
            @strongify(uploadImageObject)
            NSInteger success = 1;
            if (error) {
                _error = error;
                success = 0;
            }
            [TTQCommonHelp GAuploadImageSuccessRate:success code:error.code errorMessage:error.description time:[[NSDate date] timeIntervalSinceDate:uploadStartTime]  imageURL:object.url.absoluteString type:1];
            uploadStartTime = [NSDate date];
            if (error == nil && object.state == IMYOSSFileStateCompleted &&
                object.name) {
                uploadImageObject.url = object.url.absoluteString;
            }
        } allComplatedBlock:^( NSArray<id<IMYOSSFileObject>> *_Nonnull allObjects) {
             @strongify(self);
             if (_error) {
                 @strongify(self);
                 [self hidenProgressHUD];
                 //            [self hideKeyboard];
                 [UIWindow imy_hideHUD];
                 
                 if (_error.code == - 121) {
                     [UIWindow imy_showTextHUD:@"图片大小须不超过20M"];
                 } else {
                     if ([IMYNetState networkEnable] &&
                         _error.code != NSURLErrorNetworkConnectionLost &&
                         _error.code != NSURLErrorNotConnectedToInternet) {
                         [UIWindow imy_showHUDwithNetworkError:_error];
                     } else {
                         [UIWindow imy_showTextHUD:MT_Request_NoNetToast];
                     }
                 }
                 
                 //发送到bugly
                 NSDictionary *errorMap =
                 [NSObject imy_validJSONObject:_error.userInfo];
                 NSMutableDictionary *extraInfo = [NSMutableDictionary dictionary];
                 extraInfo[@"error"] = [errorMap imy_jsonString];
                 [[IMYAppReport shareInstance] reportErrorWithCategory:3 name:@"OSS Upload Fail In TTQ!" reason:_error.domain ?: @"com.imyttq.uploadImage" callStack:nil extraInfo:extraInfo];
                 [self postImagesFailed];
             } else {
                 @strongify(self,uploadImageObject);
                 [self postToRemoteWithImageUrls:@[uploadImageObject.url]];
             }
         }];

    }
}

- (void)postImagesFailed {
    [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"dsq_nrxqy_fssb",@"action":@2,@"info_type":@12,@"info_id":@(self.viewModel.topic_id),@"fuid":@(self.viewModel.topicUserID),@"publi_info":@"图片上传失败"} headers:nil completed:nil];
    [IMYErrorTraces postWithType:IMYErrorTraceTypeOthers pageName:NSStringFromClass([UIViewController imy_currentViewControlloer].class) category:IMYErrorTraceCategoryCommunity message:@"答案详情评论图片上传失败" detail:@{@"message":@"上传失败"}];
    
}



// MARK:- 收键盘
- (void)hideKeyboard {
    //在主线程收键盘。不然可能会奔溃
    [self hideKeyboardForceDispatchToMain:YES];
}

- (void)hideKeyboardForceDispatchToMain:(BOOL)forceDispatchToMain {
    [self.inputContentsView.textView resignFirstResponder];
    [self.inputContentsView restPose];
}

- (void)inputViewWillSend:(IMYREasyInputView *)inputView {
    [self inputViewSendWithKeyboard:YES];
}

#pragma mark - 分享

- (void)shareForDetail {
    @weakify(self);    
    IMYCoolShareConfig *config = [IMYCoolShareConfig baseShareConfig];
    IMYCoolShareItem *shareItem =
    [IMYCoolShareItem itemWithName:@"举报"
                              icon:@"all_share_btn_report"
                               tag:100000];
    shareItem.shareBlock = ^{
        @strongify(self);
        if (![self loginActicon]) {
            return;
        }
        [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"dsq_nrxqy_gdan",@"public_type":@"举报",@"info_type":@12,@"info_id":@(self.viewModel.topic_id),@"fuid":@(self.viewModel.referenced.publisher.userID),@"action":@2} headers:nil completed:nil];

        [TTQDetailHelper reportTopicAction:self.viewModel.topic_id postType:1 topicId:self.viewModel.topic_id callback:nil];
    };
    
    IMYCoolShareItem *shareItem2 =
    [IMYCoolShareItem itemWithName:@"复制链接"
                              icon:@"all_share_btn_copylink"
                               tag:100001];
    shareItem2.shareBlock = ^{
        @strongify(self);
        UIPasteboard *pasteboard = [UIPasteboard generalPasteboard];
        pasteboard.string = self.viewModel.shareBody.url;
        [UIWindow imy_showTextHUD:@"复制成功"];
        [self postBIShareWithType:IMYCoolShareSheetTypeCopyLink];
    };
    
    IMYCoolShareItem *shareItemDelete =
    [IMYCoolShareItem itemWithName:@"删除"
                              icon:@"all_share_btn_delete"
                               tag:100002];
    shareItemDelete.shareBlock = ^{
        // 删除问答帖
        @strongify(self);
        [self deleteAnswer];
    };
    
    NSString *userId = [NSString stringWithFormat:@"%@",@(self.viewModel.referenced.publisher.userID)];
    
    if (![userId isEqualToString:[IMYPublicAppHelper shareAppHelper].userid]) {
        [config addShareItemsAtSecondShareItemsGroup:@[shareItem2, shareItem]];
    } else {
        [config addShareItemsAtSecondShareItemsGroup:@[shareItem2, shareItemDelete ]];
    }
    
    NSMutableArray *configList = [NSMutableArray array];
    NSArray *firstItemGroup = [config createItems];
    if (firstItemGroup.count > 0) {
        [configList addObject:firstItemGroup];
    }
    if (config.secondShareItemsGroup.count > 0) {
        [configList addObject:config.secondShareItemsGroup];
    }
    
    [IMYCoolShareSheet
     customShareInViewController:self.navigationController
     configList:[configList copy]
     indexBlock:^(IMYCoolShareSheetType itemType,
                  NSInteger shareType) {
        
        @strongify(self);
        if (shareType == -1) {
            return;
        }
        
        if (shareType < 9999) {
            /// locationID需要从外部传入
            /// 区分是顶部的分享还是底部bar上的分享
            
            [IMYPublicShareManager new]
                .title(self.viewModel.shareBody.title)
                .content(
                         imy_isNotEmptyString(
                                              self.viewModel.shareBody.content)
                         ? self.viewModel.shareBody.content
                         : [NSString stringWithFormat:
                            @"发现一个你可能感兴趣的"
                            @"问题\"%@\"",
                            self.viewModel.shareBody
                            .title])
                .shareType(shareType)
                .imageURL(self.viewModel.shareBody.image)
                .fromURL(self.viewModel.shareBody.url)
                .callback(^(BOOL isSuccess) {
                    if (isSuccess) {
                    }
                })
                .share();
            [self postBIShareWithType:itemType];
        } else {
        }
        
    }];

    
}

- (void)deleteAnswer {
    @weakify(self);
    [UIAlertController
        imy_showAlertViewWithTitle:nil
                           message:@"要删除该内容吗？"
                 cancelButtonTitle:IMYString(@"取消")
                 otherButtonTitles:@[IMYString(@"删除")]
                           handler:^(UIAlertController *alertController, NSInteger buttonIndex) {
                               @strongify(self);
                               if (buttonIndex == 1) {
                                   if (![IMYNetState networkEnable]) {
                                       [UIWindow imy_showTextHUD:IMYString(@"咦？网络不见了，请检查网络连接")];
                                       return;
                                   }
                                   [[[self.viewModel deleteCommentWithID:self.viewModel.referenced_id] deliverOnMainThread] subscribeNext:^(id x) {
                                       [[NSNotificationCenter defaultCenter] postNotificationName:@"TTQTopicReviewDidDeleteNotifition" object:@{@"review_id": @(self.viewModel.referenced_id)}];
                                       [self imy_pop:YES];
                                   }];
                               }
                           }];

}
#pragma mark - Follow

- (void)followAction:(IMYCKFollowButton *)followBtn {
    TTQPublisherModel *publisherModel = self.viewModel.referenced.publisher;
    @weakify(self);
    [followBtn followAction:publisherModel.is_followed userId:publisherModel.userID targetUserError:publisherModel.error myError:0 completeBlock:^(BOOL success, BOOL hasRequest, IMYRelationType finalRelation, id  _Nonnull responseObj) {
        @strongify(self);
        if(success){
            // 1. 关注或者取消关注接口请求成功埋点上报处理
            if(self.viewModel.referenced.publisher.is_followed == 1 || self.viewModel.referenced.publisher.is_followed == 4){
                // 取消关注成功
                NSMutableDictionary *biParams = [self commonBIDataWithEvent:@"dsq_nrxqy_gz" action:2];
                biParams[@"public_type"] = @"2";
                biParams[@"fuid"] = @(self.viewModel.referenced.publisher.userID);
                [IMYGAEventHelper postWithPath:@"event" params:biParams headers:nil completed:nil];
            } else {
                NSMutableDictionary *biParams = [self commonBIDataWithEvent:@"dsq_nrxqy_gz" action:2];
                biParams[@"public_type"] = @"1";
                biParams[@"fuid"] = @(self.viewModel.referenced.publisher.userID);
                [IMYGAEventHelper postWithPath:@"event" params:biParams headers:nil completed:nil];
            }
            // 2.改变model状态, 按钮title跟数据联动
            self.viewModel.referenced.publisher.is_followed = finalRelation;
            [self updatePublisherInfoView];
            [self updateFollowView];
        }
    }];
}
#pragma mark - UI

- (BOOL)isNavigationBarHidden {
    return YES;
}

- (void)initViews {
    [self initTableView];
    [self addRefreshCompont];
    [self setupNavigationBar];
    [self setupBottomBar];
    self.captionView = [IMYCaptionViewV2 addToView:self.view show:YES];
    IMYCKLoadingView *loading = [[IMYCKLoadingView alloc] initWithtype:IMYCKLoadingDetail];
    loading.frame = self.captionView.bounds;
    [self.captionView setStateView:loading forState:IMYCaptionViewStateLoading];
    @weakify(self);
    [self.captionView setRetryBlock:^{
        @strongify(self);
        [self request:YES];
    }];
    [self.captionView setNoNetworkRetryBlock:^{
        [UIView imy_showTextHUD:kStatusText_networkDisconnectNoCache];
    }];
    [self initInputContentView];
}

- (void)setupNavigationBar {
    // 创建自定义导航栏
    CGFloat barHeight = SCREEN_STATUSBAR_NAVIGATIONBAR_HEIGHT;
    UIView *newNavBar = [[UIView alloc] initWithFrame:CGRectMake(0, -SCREEN_STATUSBAR_HEIGHT, SCREEN_WIDTH, barHeight)];
    [newNavBar imy_setBackgroundColorForKey:kIMY_BG];
    
    CGRect boxFrame = CGRectMake(0, SCREEN_STATUSBAR_HEIGHT, 38, barHeight - SCREEN_STATUSBAR_HEIGHT);
    IMYTouchEXButton *leftButton = [[IMYTouchEXButton alloc] initWithFrame:boxFrame];
    [leftButton imy_setImage:@"nav_btn_back_black"];
    leftButton.contentHorizontalAlignment = UIControlContentHorizontalAlignmentRight;
    [leftButton addTarget:self action:@selector(imy_topLeftButtonTouchupInside) forControlEvents:UIControlEventTouchUpInside];
    [newNavBar addSubview:leftButton];

    [self setupTopRightButton];
    [newNavBar addSubview:self.topRightButton];
    self.topRightButton.imy_right = newNavBar.imy_width - 6;
    self.topRightButton.imy_top = SCREEN_STATUSBAR_HEIGHT;
        
    [self.view addSubview:newNavBar];
    [newNavBar addSubview:self.bottomLineView];
    self.bottomLineView.imy_bottom = newNavBar.imy_height;
    self.customNavigationBar = newNavBar;
}

- (UIView *)bottomLineView {
    if (!_bottomLineView) {
        _bottomLineView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, 1 / [UIScreen mainScreen].scale)];
        [_bottomLineView imy_setBackgroundColorForKey:kCK_Black_J];
        _bottomLineView.alpha = 0;
    }
    return _bottomLineView;
}

- (void)setupTopRightButton {
    @weakify(self);
    //设置button的RAC点击信号只能设置一次,不然会收到多次回调信号, 重复创建shareView, 造成遮挡
    [[self.topRightButton rac_signalForControlEvents:UIControlEventTouchUpInside] subscribeNext:^(id x) {
        @strongify(self);
        [self.inputContentsView.textView resignFirstResponder];
        [self shareForDetail];
    }];
}

- (void)setupTopRightShareButton{
    [self.topRightButton imy_setTitle:@"\U0000e6f6"];
    self.topRightButton.titleLabel.font = [UIFont imy_IconFontWith:22];
    [self.topRightButton imy_setTitleColor:kCK_Black_A];
    self.topRightButton.accessibilityIdentifier = @"ttq_detail_topRight_more";
}


- (void)initTableView {
    self.tableView = [[TTQTopicTableView alloc] initWithFrame:CGRectMake(0, SCREEN_NAVIGATIONBAR_HEIGHT, SCREEN_WIDTH, SCREEN_HEIGHT - SCREEN_STATUSBAR_NAVIGATIONBAR_HEIGHT - 52 - SCREEN_TABBAR_SAFEBOTTOM_MARGIN) style:UITableViewStylePlain];
    [self.tableView imy_setBackgroundColorForKey:kCK_White_AN];
    self.tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    self.tableView.estimatedRowHeight = 0;
    self.tableView.estimatedSectionHeaderHeight = 0;
    self.tableView.estimatedSectionFooterHeight = 0;
    [self.view addSubview:self.tableView];
    
    self.tableViewAdapter = [IMYTableViewAdapter adpaterWithTableView:self.tableView];
    self.tableViewAdapter.UIDelegate = self;
    self.commentHeaderAdapter = [IMYQACommentHeaderAdapter new];
    [self.tableViewAdapter registerModuleDelegate:self.commentHeaderAdapter];
    self.commentAdapter = [IMYQAAnswerCommentAdapter new];
    self.commentAdapter.tableView = self.tableView;
    self.commentAdapter.biEntrance = [self biEntrance];
    [self.tableViewAdapter registerModuleDelegate:self.commentAdapter];
    @weakify(self);
    [self.commentAdapter setReplyActionBlock:^(NSIndexPath * _Nonnull indexPath) {
        @strongify(self);
        self.locationIndexPath = indexPath;
        [self replyActionAtIndexPath:self.locationIndexPath];
    }];
    [self.commentAdapter setReloadDataSourceBlock:^{
        @strongify(self);
        [self finishedRequest:nil isRefresh:NO];
    }];
    [self.commentAdapter setAutoLoadMoreBlock:^(NSIndexPath * _Nonnull indexPath) {
        @strongify(self);
        if (self.viewModel.hasMore && ![self.tableView.mj_footer isRefreshing] && (indexPath.section > self.viewModel.dataSource.count - 4)) {
            [self.tableView.mj_footer beginRefreshing];
        }
    }];
}

- (TTQTopicDetailPublisherTitleView *)publisherInfoView {
    if (_publisherInfoView == nil && self.viewModel.referenced.publisher) {
        _publisherInfoView = [[TTQTopicDetailPublisherTitleView alloc] initWithType:TTQTopicDetailPublisherTitleStyleDefault];
        _publisherInfoView.frame = CGRectMake(48, SCREEN_STATUSBAR_HEIGHT, SCREEN_WIDTH - 60 - 48 + 10, 44);
        _publisherInfoView.containView.alpha = 0;
        @weakify(self);
        [self updatePublisherInfoView];
        [_publisherInfoView bk_whenTapped:^{
            @strongify(self);
            if (self.publisherInfoView.containView.alpha == 0) {
                return;
            }
            [self userInfoAction:self.viewModel.referenced.publisher];
        }];
        [_publisherInfoView setTapFollowAction:^(UIButton *btn) {
            @strongify(self);
            [self followAction:btn];
        }];
        [self.customNavigationBar addSubview:_publisherInfoView];
        [self.publisherInfoView updateTitleViewWidth:46];
    }
    return _publisherInfoView;
}

- (void)updatePublisherInfoView {
    NSString *babyInfoStr = self.viewModel.referenced.publisher.baby_info;
    if (imy_isNotEmptyString(self.viewModel.referenced.publisher.mp_user_icon)){
        babyInfoStr = self.viewModel.referenced.publisher.mp_expert_user;
    } else if(imy_isNotEmptyString(self.viewModel.referenced.publisher.mp_expert_user_icon)){
        babyInfoStr = self.viewModel.referenced.publisher.mp_expert_user_icon;
    } else if (imy_isNotEmptyString(self.viewModel.referenced.publisher.qa_user_icon)){
        babyInfoStr = self.viewModel.referenced.publisher.qa_user_icon;
    } else if (imy_isNotEmptyString(self.viewModel.referenced.publisher.expertIconNew)){
        babyInfoStr = self.viewModel.referenced.publisher.expertIconNew;
    }
    [self.publisherInfoView setUserAvastar:self.viewModel.referenced.publisher.user_avatar.large isVip:self.viewModel.referenced.publisher.isvip name:self.viewModel.referenced.publisher.screen_name title:babyInfoStr];
    //  增加是否匿名 &自己发帖子的判断，是的话不显示关注按钮
    BOOL isSHow = (self.viewModel.referenced.publisher.error == 0 || self.viewModel.referenced.publisher.error == 2|| self.viewModel.referenced.publisher.error == 3) && self.viewModel.referenced.publisher.userID != [[IMYPublicAppHelper shareAppHelper].userid integerValue];
    [self.publisherInfoView updatefollowButtonShow:isSHow status:self.viewModel.referenced.publisher.is_followed rightShowNotice:NO];
}

- (void)setupBottomBar {
    self.bottomBar = [IMYCKBottomReplyBar qaAnwserBarView];
    [self.bottomBar updateToAnswerStyle];
    self.bottomBar.imy_bottom = SCREEN_HEIGHT - SCREEN_STATUSBAR_HEIGHT;
    [self.view addSubview:self.bottomBar];
    @weakify(self);
    [self.bottomBar setPlaceholderActionBlock:^{
        @strongify(self);
 
        [self replyActionAtIndexPath:nil];
    }];
    [self.bottomBar setCommentActionBlock:^{
        @strongify(self);
        if ([TTQABTestConfig disableInteractByType:TTQ_Interact_type_comment]) {
            return;//互动禁止793
        }
        NSInteger publicType = 2;
        //如果评论数为0，直接唤起键盘
        if (self.viewModel.referenced.referenced_num == 0) {
            [self replyActionAtIndexPath:nil publicKey:2];
        } else {
            //评论区的位置小于屏幕高度/2，直接唤起键盘，否则吸顶显示
            CGFloat statusNavBarHeight = SCREEN_STATUSBAR_NAVIGATIONBAR_HEIGHT;
            CGFloat contentOffsetY = self.tableView.contentOffset.y;
            CGFloat commentSectionY = [self firstSectionHeaderY] + SCREEN_STATUSBAR_NAVIGATIONBAR_HEIGHT - contentOffsetY;
            CGFloat halfVCHeight = SCREEN_HEIGHT / 2.0;
            
            CGFloat currentMaxY = floor(CGRectGetHeight(self.tableView.bounds) +  contentOffsetY);
            CGFloat contentHeight = floor(self.tableView.contentSize.height);
            //当前偏移的高度距离最大高度的距离
            CGFloat y = self.tableView.contentSize.height - self.tableView.contentOffset.y - CGRectGetHeight(self.tableView.bounds);
            if (currentMaxY >= contentHeight || commentSectionY < halfVCHeight || (y < 40)) {
                [self replyActionAtIndexPath:nil publicKey:2];
            }else{
                [self locateToCommentAreaWithAnimation:YES];
                publicType = 1;
            }
        }
        [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"dsq_nrxqypl_dbpl",@"action":@2,@"comment_id":@(self.viewModel.referenced_id),@"info_type":@12,@"info_id":@(self.viewModel.topic_id), @"public_type":@(publicType)} headers:nil completed:nil];
    }];
    [self.bottomBar setPraiseActionBlock:^{
        @strongify(self);
        [[IMYURIManager shareURIManager] runActionWithPath:@"push/checkPushSettings" params:@{@"position":@(IMYNABoxShowPositionCommunity_dianzan)} info:nil];
        BOOL shouldPraise = [TTQCheckService checkShouldPraiseWithUserStatus:self.viewModel.currentUserInfo.error hasPraised:self.viewModel.referenced.has_praise];
        if (shouldPraise) {
            BOOL isPraise = !self.viewModel.referenced.has_praise;
            if (isPraise) {
                [self.bottomBar.praiseButton startAnimating];
            }
            self.viewModel.referenced.has_praise = !self.viewModel.referenced.has_praise;
            NSInteger beforePraise = self.viewModel.referenced.praise_num;
            if (isPraise) {
                self.viewModel.referenced.praise_num ++;
            } else {
                self.viewModel.referenced.praise_num --;
            }
            [self.bottomBar.praiseButton updatePraise:isPraise count:self.viewModel.referenced.praise_num];
            NSMutableDictionary *params = [NSMutableDictionary dictionary];
            params[@"topic_id"] = @(self.viewModel.topic_id);
            params[@"forum_id"] = @(self.viewModel.forum_id);
            params[@"review_id"] = @(self.viewModel.referenced_id);
            params[@"is_praise"] = isPraise?@1:@0;
            [[[IMYPublicServerRequest postPath:@"v2/user_praise_review" host:circle_seeyouyima_com params:params headers:nil] deliverOnMainThread] subscribeNext:^(id x) {
            } error:^(NSError *error) {
            }];
            [IMYUGCRequest postReviewPraiseEvent:isPraise num:self.viewModel.referenced.praise_num reviewId:self.viewModel.referenced_id];
            if ([[IMYPublicAppHelper shareAppHelper].userid integerValue] == self.viewModel.topicUserID) {
                /// 自己是提问者本人
                self.viewModel.referenced.praised_by_author = isPraise;
            }
            [self updateTableHeader];
            
            NSMutableDictionary *gaDic = [@{
                @"action":@2,
                @"info_type":@12,
                @"info_id":@(self.viewModel.topic_id),
                @"fuid":@(self.viewModel.referenced.publisher.userID),
                @"public_type":(isPraise?@21:@22),
                @"public_key":@1,
                @"comment_uid":@(self.viewModel.referenced.publisher.userID),
                @"comment_id":@(self.viewModel.referenced_id),
                @"interact_num":@(self.viewModel.referenced.praise_num),
                @"event":@"dsq_nrxqy_dz",@"public_info":@"回答帖",
                @"is_on_period":@([IMYUGCEventHelper userIsInPhysiological]),@"redirect_url":[IMYUGCEventHelper currentPageRedirectUrl]
            } mutableCopy];
            [IMYGAEventHelper postWithPath:@"event" params:gaDic headers:nil completed:nil];

        }

    }];

}

- (UIButton *)topRightButton {
    if (!_topRightButton && self.navigationItem.rightBarButtonItems.count == 0) {
        IMYTouchEXView *btBox;
        CGRect boxFrame = CGRectMake(0, 0, 40, 44);
        btBox = [[IMYTouchEXView alloc] initWithFrame:boxFrame];

        _topRightButton = [[IMYTouchEXButton alloc] initWithFrame:boxFrame];
        [(id)_topRightButton setExtendTouchAllValue:20];

        BOOL isNight = [IMYPublicAppHelper shareAppHelper].isNight;
        BOOL isWhite = [self imy_isWhiteTopBar];
        if (!isNight && isWhite) {
            [_topRightButton imy_setTitleColor:kCK_Black_A];
        } else {
            [_topRightButton imy_setTitleColor:kIMY_TopbarButtonTitleColor highl:kIMY_TopbarButtonTitleHighlightedColor];
        }

        _topRightButton.titleEdgeInsets = UIEdgeInsetsMake(0, 0, 0, 2.5);
        _topRightButton.imageEdgeInsets = UIEdgeInsetsMake(0, 0, 0, 2.5);
        _topRightButton.contentHorizontalAlignment = UIControlContentHorizontalAlignmentCenter;
        _topRightButton.titleLabel.font = [UIFont systemFontOfSize:16];

        [btBox addSubview:_topRightButton];
        _topRightButton.hidden = NO;
    }
    return _topRightButton;
}
//进度展示
- (MBProgressHUD *)progressHUD {
    if (_progressHUD == nil) {
        UIWindow *topWindow = [UIWindow imy_getShowTopWindow];
        //        UIView *topWindow = self.view;
        _progressHUD = [MBProgressHUD progressHUDForRound:topWindow];
    }
    return _progressHUD;
}

- (void)setProcessHUD:(CGFloat)progress {
    self.progressHUD.progress = progress;
}

- (void)hidenProgressHUD {
    [UIWindow imy_hideHUD];
    if (_progressHUD) {
        [self.progressHUD removeFromSuperview];
        [self.progressHUD hideAnimated:YES];
        self.progressHUD = nil;
    }
}

#pragma mark - UITableViewDelegate

- (void)scrollViewDidEndDecelerating:(UIScrollView *)scrollView {
    if (self.tableView.contentOffset.y < [self firstSectionHeaderY]) { // 处于正文区域
        self.preContentOffset = self.tableView.contentOffset;
    } else {
        self.preCommentOffset = self.tableView.contentOffset;
    }
}

- (void)scrollViewDidEndDragging:(UIScrollView *)scrollView willDecelerate:(BOOL)decelerate {
    if (!decelerate) {
        if (self.tableView.contentOffset.y < [self firstSectionHeaderY]) { // 处于正文区域
            self.preContentOffset = self.tableView.contentOffset;
        } else {
            self.preCommentOffset = self.tableView.contentOffset;
        }
    }
}
#pragma mark - ga
- (NSDictionary *)ga_appendParams
{
    NSMutableDictionary *appendParams = [NSMutableDictionary dictionary];
    [appendParams addEntriesFromDictionary:super.ga_appendParams];
    appendParams[@"redirect_url"] = self.fromURI.uri;
    appendParams[@"entrance"] = @([self biEntrance]);
    appendParams[@"position"] = @([self biPosition]);
    if ([self biEntrance] == 14) {
        appendParams[@"page_type"] = @1;
    } 
    appendParams[@"info_type"] = @12;
    appendParams[@"info_id"] = @(self.viewModel.topic_id);
    appendParams[@"is_on_period"] = @([IMYUGCEventHelper userIsInPhysiological]);
    appendParams[@"redirect_url"] = self.fromURI.uri;
    appendParams[@"comment_id"] = @(self.viewModel.referenced_id);
    if (!appendParams[@"forum_id"]) {
        appendParams[@"forum_id"] = @(self.viewModel.forum_id);
    }
    if (appendParams[@"viewModel"]) {
        appendParams[@"viewModel"] = nil;
    }
    return appendParams;
}

- (void)addBICommentView {
    self.biCommentView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, 10, 10)];
    self.biCommentView.hidden = YES;
    [self.tableView addSubview:self.biCommentView];
    self.biCommentView.imyut_eventInfo.eventName = [NSString stringWithFormat:@"comment_subReviews_%p",self];
    self.biCommentView.imyut_eventInfo.type = IMYUTExposureTypeReal;
    @weakify(self);
    [self.biCommentView.imyut_eventInfo setExposuredBlock:^(__kindof UIView *view, NSDictionary *params) {
        @strongify(self);
        NSInteger entrance = [self.fromURI.params[@"entrance"] integerValue];
        NSInteger action = 4;
        if (view.imyut_eventInfo.isVisible) {
            action = 3;
        }
        NSMutableDictionary *biParams = [self commonBIDataWithEvent:@"dsq_nrxqy_plqll" action:action];
        biParams[@"index"] = @1;
        biParams[@"position"] = @141;
        biParams[@"entrance"] = @([self biEntrance]);
        [IMYGAEventHelper postWithPath:@"event" params:biParams headers:nil completed:nil];
    }];
    
    [RACObserve(self.tableView, contentSize) subscribeNext:^(id  _Nullable x) {
        @strongify(self);
        imy_asyncMainBlock(0.1, ^{
            [self updateBICommentView];
        });
    }];

}

- (void)updateBICommentView {
    /// 已经可以曝光过self.headView
    
    if (self.viewModel.dataSource && self.captionView.state == IMYCaptionViewStateHidden) {
        self.biCommentView.hidden = NO;
            /// 去除评论的section
        CGRect rect = [self.tableView rectForSection:0];
        self.biCommentView.imy_top = MAX(self.tableView.contentOffset.y, rect.origin.y + rect.size.height + 36 + 12);
    } else {
        self.biCommentView.hidden = YES;
    }
}

- (NSMutableDictionary *)commonBIDataWithEvent:(NSString *)event action:(NSInteger)action {
    NSMutableDictionary *params = [NSMutableDictionary dictionaryWithDictionary:@{@"event":event,@"action":@(action),@"info_type":@12,@"info_id":@(self.viewModel.topic_id)}];
    params[@"comment_id"] = @(self.viewModel.referenced_id);
    return params;
}

- (NSMutableDictionary *)detailBiDataWithEvent:(NSString *)event action:(NSInteger)action {
    NSMutableDictionary *params = [self commonBIDataWithEvent:event action:action];
    params[@"is_on_period"] = @([IMYUGCEventHelper userIsInPhysiological]);
    params[@"fuid"] = @(self.viewModel.referenced.publisher.userID);
    params[@"redirect_url"] = [IMYUGCEventHelper currentPageRedirectUrl];
    return params;
}

- (void)postBIShareWithType:(IMYCoolShareSheetType)type {
    NSString *public_type = @"";
    if (type == IMYCoolShareSheetTypeWeixiTimeline) {
        public_type = @"微信朋友圈";
    } else if (type == IMYCoolShareSheetTypeWeixiSession) {
        public_type = @"微信好友";
    } else if (type == IMYCoolShareSheetTypeQQSpace) {
        public_type = @"QQ空间";
    } else if (type == IMYCoolShareSheetTypeQQSession) {
        public_type = @"QQ好友";
    } else if (type == IMYCoolShareSheetTypeCopyLink) {
        public_type = @"复制链接";
    } else if (type == IMYCoolShareSheetTypeSinaWeibo) {
        public_type = @"微博";
    }
    if (public_type.length == 0) return;
    NSMutableDictionary *params = [self detailBiDataWithEvent:@"dsq_nrxqy_fxtz" action:2];
    params[@"public_type"] = public_type;
    [IMYGAEventHelper postWithPath:@"event" params:params headers:nil completed:nil];
}

- (NSInteger)biEntrance {
    return [self.fromURI.params[@"entrance"] integerValue];
}

- (NSInteger)biPosition {
    return [self.fromURI.params[@"position"] integerValue];
}

@end
