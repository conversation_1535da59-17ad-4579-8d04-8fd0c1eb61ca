//
//  IMYQATopicReferenceViewModel.m
//  IMYTTQ
//
//  Created by king on 15/7/16.
//  Copyright © 2015年 MeiYou. All rights reserved.
//

#import "IMYQATopicReferenceViewModel.h"
#import "NSString+TTQ.h"
#import "TTQABTestConfig.h"
#import "TTQForumModel.h"
#import "TTQHttpHelper.h"
#import "IMYQAAudioPlayer.h"
#import "IMYQAQuestionAnswerModel.h"
#import <IMYBaseKit/IMYViewKit.h>
#import "TTQTopicModel.h"
#import "IMYUGCEventHelper.h"
#import "TTQCheckService.h"
#import "IMYUGCRequest.h"

@interface IMYQATopicReferenceViewModel ()
@property (nonatomic, assign) BOOL isFirstRequest;//第一次进入页面的请求
@end

@implementation IMYQATopicReferenceViewModel
@synthesize forum_id = _forum_id;
@synthesize replyCommand = _replyCommand;

- (instancetype)init {
    self = [super init];
    if (self) {
        self.isFirstRequest = YES;
        self.topicType = TTQCommentBindTopicTypeUnknown;
        self.cardShareUrlString = @"http://embryo-video.listenvod.com/my_bbbh_mmbh_wd_fx/moren.png";
    }
    return self;
}

- (instancetype)initWithTopicID:(NSInteger)topic_id referenced_id:(NSInteger)referenced_id {
    if (self = [self init]) {
        self.isFirstRequest = YES;
        self.topic_id = topic_id;
        self.referenced_id = referenced_id;
    }
    return self;
}

///type: 0 下拉刷新，1 加载下一页，2 加载上一页,3 跳楼
- (RACSignal *)requestRemoteDataForType:(NSInteger)type params:(NSDictionary *)parameters {
    @weakify(self);
    self.requestType = type;
    if (self.isFirstRequest) {
        self.isFirstRequest = NO;
        self.requestType = 100;
    }
    
    if (type == 100) {
        // 暂停语音
        for (TTQCommentModel *qaModel in self.dataSource) {
            IMYQAAudioPlayer *player = [IMYQAAudioPlayer sharedInstance];
            if ([player.currentAudio isEqual:qaModel.audio]) {
                [[NSNotificationCenter defaultCenter] postNotificationName:@"IMYQAAudioPlayerChange" object:nil];
                [player resetPlayer];
                break;
            }
        }
    }
    
    NSMutableDictionary *params = parameters.mutableCopy;
    params[@"topic_id"] = @(self.topic_id);
    params[@"referenced_id"] = @(self.referenced_id);
    if (type == 1) {
        TTQCommentModel *comment = self.dataSource.lastObject;
        params[@"last"] = @(comment.commentID);
        params[@"load_direction"] = @"next";
    } else if (type == 2) {
        TTQCommentModel *comment = self.dataSource.firstObject;
        params[@"last"] = @(comment.commentID);
        params[@"load_direction"] = @"prev";
    } else if (type == 3) {
        params[@"goto"] = @(self.gotoID);
    } else if (type == 0) {
        if (self.animationCommentIdWhenAppear) {
            params[@"top_review_id"] = @(self.animationCommentIdWhenAppear);
        }
    }
    
    //reveal_num 透出的评论数量；
    //应测试要求，增加这个参数，修补评论总回复数和外层问答详情页的回复数不一致的问题；
    params[@"reveal_num"] = @(3);
    params[@"size"] = @20;
    IMYHTTPBuildable *paramBuild = [IMYServerRequest get:@"v2/stacke_review_detail" host:circle_seeyouyima_com params:params headers:nil];
    RACSignal *reviewDetailSignal = paramBuild.signal;
    
    return [reviewDetailSignal doNext:^(id<IMYHTTPResponse> response) {
        @strongify(self);
        NSArray<TTQCommentModel> *referenceds = [response.responseObject[@"references"] toModels:[TTQCommentModel class]];
        if (response.responseObject[@"user_info"]) {
            TTQTopicCurrentUserInfo *currentUserInfo = [response.responseObject[@"user_info"] toModel:[TTQTopicCurrentUserInfo class]];
            self.currentUserInfo = currentUserInfo;
        }
        if (type == 0 && self.animationCommentIdWhenAppear > 0) {
            [referenceds match:^BOOL(TTQCommentModel *element) {
                if (element.commentID == self.animationCommentIdWhenAppear) {
                    element.needHighlightWhenAppear = YES;
                    element.isSetToTop = YES;
                    return YES;
                }
                return NO;
            }];
            self.animationCommentIdWhenAppear = 0;
        } else if (type == 2) {
            TTQCommentModel *fistComment = self.dataSource.firstObject;
            if ([fistComment isKindOfClass:TTQCommentModel.class] && fistComment.isSetToTop) {
                referenceds = [referenceds filter:^BOOL(TTQCommentModel * element) {
                    if (element.commentID == fistComment.commentID) {
                        return NO;
                    }
                    return YES;
                }];
            }
        }

        self.hasMore = YES;
        switch (type) {
            case 0:
                self.dataSource = referenceds;
                self.referenced = [response.responseObject[@"review"] toModel:[TTQReferenceCommentModel class]];
                self.referenced.topic = [response.responseObject[@"topic"] toModel:[TTQTopicModel class]];
                self.referenced.type = self.topicType;
                self.shareBody = [response.responseObject[@"share_body"] toModel:[TTQShareBodyModel class]];
                self.showTableHeader = true;
                self.automaticallyRefresh = referenceds.count >= 20;
                self.hasMore = self.automaticallyRefresh;
                break;
            case 1:
                if (referenceds.count) {
                    NSMutableArray *array = [[NSMutableArray alloc] initWithArray:self.dataSource];
                    [array addObjectsFromArray:referenceds];
                    self.dataSource = array;
                }
                else {
                    self.hasMore = NO;
                }
                self.automaticallyRefresh = referenceds.count >= 20;
                break;
            case 2:
                if (referenceds.count) {
                    NSMutableArray *array = referenceds.mutableCopy;
                    [array addObjectsFromArray:self.dataSource];
                    self.dataSource = array;
                }
                else {
                    self.hasMore = NO;
                }
                self.showTableHeader = referenceds.count < 20;
                if (self.showTableHeader) {
                    self.gotoID = -1;
                }
                break;
            case 3:
                self.dataSource = referenceds;
                self.referenced = [response.responseObject[@"review"] toModel:[TTQReferenceCommentModel class]];
                self.referenced.type = self.topicType;
                self.showTableHeader = referenceds.count < 20;
                break;
            default:
                break;
        }
        if (self.inputDefaultText == nil) {
            self.inputDefaultText = nil;
        }
    }];
}
- (RACCommand *)replyCommand {
    if (_replyCommand == nil) {
        @weakify(self);
        _replyCommand = [RACCommand commandWithSignalBlock:^RACSignal *(NSArray *array) {
            @strongify(self);
            NSMutableDictionary *params = array.firstObject;
            ProgressCallback progressBlock = nil;
            if (array.count > 1) {
                progressBlock = array[1];
            }
            
            TTQCommentModel *model = [self tableCellModelAtIndexPath:self.selectedReplyIndex];
            if (model) {
                params[@"referenced_id"] = @(model.commentID);
                params[@"parent_referenced_id"] = @(self.referenced_id);
            } else {
                params[@"referenced_id"] = @(self.referenced_id);
                params[@"parent_referenced_id"] = @(self.referenced_id);
            }
            TTQCommentModel *comment = self.dataSource.lastObject;
            if (comment) {
                params[@"last"] = @(comment.commentID);
            } else {
                params[@"last"] = @(0);
            }
            params[@"diff_data"] = @(YES);
            params[@"topic_id"] = @(self.topic_id);
            NSString *content = params[@"content"];
            if ([content isKindOfClass:NSString.class] && content.length) {
                params[@"content_size"] = @(content.ttq_textLength);
            }
            NSString *path = @"v2/topic_review";
            TTQCommentModel *biComment = model?:self.referenced;
            NSInteger bi_comment_id = biComment.commentID;
            NSInteger bi_publisherId = biComment.publisher.userID;
            NSInteger public_type = model?2:1;
            return [[TTQHttpHelper postPath:path params:params progressBlock:progressBlock] doNext:^(id<IMYHTTPResponse> response) {
                @strongify(self);
                [IMYEventHelper event:@"plxq-hf"];
                NSDictionary *dic = response.responseObject[@"reviews"];

                if (!dic) {
                    dic = response.responseObject[@"data"][@"reviews"];
                }
                if (self.from_community_home) {
                    [TTQCommonHelp GAEventForEventWithName:@"ttq_tzplxqy_hfpl" action:2];
                }
                NSArray *comments = [dic toModels:[TTQCommentModel class]];
                if (comments) {
                    if (self.dataSource == nil) {
                        self.dataSource = comments;
                    } else {
                        NSMutableArray *array = self.dataSource.mutableCopy;
                        [array addObjectsFromArray:comments];
                        self.dataSource = array;
                    }
                    self.referenced.referenced_num += 1;
                    if (self.referenced.referenced_num < self.dataSource.count) {
                        self.referenced.referenced_num = self.dataSource.count;
                    }
                }
                [[IMYURIManager shareURIManager] runActionWithPath:@"push/checkPushSettings" params:@{@"position":@(IMYNABoxShowPosition_ttqPublic)} info:nil];

                NSInteger addNum = self.referenced.referenced_num - self.commentModel.referenced_num;
                NSString *pageInfo = self.bi_pageSource?self.bi_pageSource:@"内容详情页";
                TTQCommentModel *biComment = model?:self.referenced;
                NSMutableDictionary *gaDic = [@{@"event":@"dsq_nrxqy_pl",
                                                @"action":@2,
                                                @"public_type":@(public_type),
                                                @"info_type":@12,
                                                @"info_id":@(self.topic_id),
                                                @"fuid":@(self.referenced.publisher.userID),
                                                @"comment_uid":@(bi_publisherId),
                                                @"comment_id":@(bi_comment_id),
                                                @"interact_num":@(self.referenced.referenced_num),
                                                @"public_info":pageInfo,
                                                @"is_on_period":@([IMYUGCEventHelper userIsInPhysiological]),
                                                @"redirect_url":[IMYUGCEventHelper currentPageRedirectUrl]} mutableCopy];
                
                gaDic[@"public_key"] = @(self.bi_replyPublicKey);
                [IMYGAEventHelper postWithPath:@"event" params:gaDic headers:nil completed:nil];
            }];
        }];
    }
    return _replyCommand;
}

- (BOOL)canReply {
    return YES;
}

- (BOOL)canAccess {
    return self.currentUserInfo.error != 2;
}

- (NSInteger)forum_id {
    if (!_forum_id) {
        _forum_id = self.referenced.topic_forum_id;
    }
    return _forum_id;
}

- (NSUInteger)topicUserID {
    return self.referenced.topic_user_id;
}

- (BOOL)is_ask {
    return self.referenced.is_ask;
}

- (NSInteger)numberOfSections {
    return 1;
}
- (NSInteger)numberOfRowsInSection:(NSInteger)section {
    return self.dataSource.count;
}
- (id)tableCellModelAtIndexPath:(NSIndexPath *)indexPath {
    if (indexPath == nil) {
        return nil;
    }
    NSInteger index = indexPath.section;
//    if (self.v887Style) {
    
    return [self.dataSource imy_objectAtIndex:index];
}

- (NSString *)identifierRowAtIndexPath:(NSIndexPath *)indexPath {
    return @"IMYQATopicReferenceCell";
}

/// 评论点赞/// 处理评论点赞操作
/// - Parameters:
///   - model: TTQCommentModel
///   - isPraise: 是否点赞, YES为点赞，NO为取消点赞
///   - completionBlk: 操作完成回调，返回结果和错误信息
- (void)handleCommentPraiseWithModel:(TTQCommentModel *)model
                            isPraise:(BOOL)isPraise
                       completionBlk:(void(^)(id <IMYHTTPResponse>resData, NSError * error))completionBlk {
    
    if (self.currentUserInfo && self.currentUserInfo.error > 0) {
        //禁言处理
        NSString *message = self.currentUserInfo.error == 3 ? @"违反圈规被禁言"  : (self.currentUserInfo.error == 2 ? @"违反圈规被封号" : @"违反圈规被禁言");
        [UIAlertController imy_showAlertViewWithTitle:message message:@"可以到\"帮助与反馈\"里申诉反馈" cancelButtonTitle:IMYString(@"取消") otherButtonTitles:@[ IMYString(@"去反馈") ] handler:^(UIAlertController *alertController,  NSInteger buttonIndex) {
            if (buttonIndex == 1) {
                [[IMYURIManager shareURIManager] runActionWithString:@"meiyou:///qiyu/chat?params=eyJncm91cElkIjogNDgwODE1ODc4fQ=="];
            }
        }];
        return;
    }

    [super handleCommentPraiseWithModel:model isPraise:isPraise completionBlk:^(id <IMYHTTPResponse>resData, NSError * error) {
        if (self.from_community_home > 0 && isPraise) {
            [TTQCommonHelp GAEventForEventWithName:@"ttq_tzplxqy_dzpl" action:2];//点赞埋点
        }

        NSMutableDictionary *params = [NSMutableDictionary dictionary];
        params[@"event"] = @"dsq_nrxqy_dz";
        params[@"action"] = @2;
        params[@"info_type"] = @12;
        params[@"info_id"] = @(model.topic_id);
        params[@"fuid"] = @(self.referenced.publisher.userID);
        params[@"public_type"] = isPraise ? @21 : @22;
        params[@"public_key"] = @1,
        params[@"comment_uid"] = @(model.publisher.userID);
        params[@"comment_id"] = @(model.commentID);
        params[@"public_info"] = @"回答帖的评论";
        params[@"interact_num"] = @(model.praise_num);
        params[@"is_on_period"] = @([IMYUGCEventHelper userIsInPhysiological]);
        params[@"redirect_url"] = [IMYUGCEventHelper currentPageRedirectUrl];
        
        [IMYGAEventHelper postWithPath:@"event" params:params.copy headers:nil completed:nil];
        !completionBlk ?: completionBlk(resData, error);
    }];
}

@end
