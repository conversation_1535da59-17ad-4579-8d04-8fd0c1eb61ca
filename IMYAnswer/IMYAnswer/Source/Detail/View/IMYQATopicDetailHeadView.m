//
//  SYCommentTitleView.m
//  Seeyou
//
//  Created by <PERSON> on 13-6-28.
//  Copyright (c) 2013年 linggan. All rights reserved.
//

#import "IMYQATopicDetailHeadView.h"

#import "IMYQATopicViewModel.h"
#import <IMYTTQ/TTQVideoModel.h>
#import <IMYTTQ/TTQPublisherModel+TTQCommentCellTag.h>

#import <IMYTTQ/TTQLinkView.h>
#import <IMYTTQ/TTQTopicActivityView.h>
#import <IMYTTQ/TTQVideoImageView.h>
#import <IMYTTQ/TTQVoteView.h>

#import <IMYTTQ/IMYRM80AttributedLabel+TTQ.h>
#import <IMYTTQ/NSString+TTQ.h>
#import <IMYTTQ/TTQJumpType.h>
#import <IMYTTQ/TTQMacro.h>
#import <IMYTTQ/UIFont+TTQ.h>
#import <IMYBaseKit/UIFont+IMYViewKit.h>
#import <IMYTTQ/UIImageView+TTQ.h>
#import <IMYTTQ/UILabel+TTQ.h>
#import <IMYTTQ/UIView+TTQ.h>
#import <MediaPlayer/MediaPlayer.h>
#import <IMYTTQ/TTQABTestConfig.h>

#import <IMYTTQ/TTQCostomPKView.h>
#import <IMYTTQ/TTQFeedsVoteView.h>
#import <IMYTTQ/TTQTwoImageVoteView.h>
#import <IMYTTQ/TTQMultipleVoteView.h>
#import <UIView+IMYFoundation.h>
#import "TTQPublisherModel+IMYQACommentCellTag.h"
#import <IMYBaseKit/IMYURIManager.h>
#import <IMYUGC/IMYCKFeedsAuthenticationView.h>

#define FixImageTag 12549

NSString *const IMYQATopicContentLinkTypeHotTopic = @"IMYQATopicContentLinkTypeHotTopic";
NSString *const IMYQATopicContentLinkTypeTheme = @"IMYQATopicContentLinkTypeTheme";

@interface IMYQATopicDetailHeadView ()

@property (weak, nonatomic) IBOutlet NSLayoutConstraint *praiseViewWidthConstraint;
@property (nonatomic, strong) TTQTopicActivityView *activityView;
@property (nonatomic, strong) IMYCapsuleButton *followButton;
@property (nonatomic, strong) IMYAvatarImageView *avatarBtn;
@property (nonatomic, strong) UIImageView *avatarExtentsionArea;//扩大点击范围
@property (nonatomic, strong) UILabel *attentionAndTotalCountLabel; // 姐妹关注浏览数
@property (nonatomic, strong) UILabel *timeLabel;
@property (nonatomic, strong) UILabel *ipLabel;
@property (nonatomic, strong) UILabel *babyInfo;

@property (nonatomic, strong) UIView *authView;   /// 创作者标识
@property (nonatomic, strong) UILabel *nameLabel;
@property (nonatomic, strong) IMYQATopicViewModel *viewModel;
@property (nonatomic, strong) TTQTopicModel *bindingModel;
@property (nonatomic, copy) NSArray *btns;
@property (nonatomic, strong) NSMutableArray *imgViews;
@property (nonatomic, strong) NSMutableArray *lowQualityViews;

@end


@implementation IMYQATopicDetailHeadView

- (void)awakeFromNib {
    [super awakeFromNib];
    [self commonInit];
}

- (void)commonInit {

    self.imy_width = [[UIScreen mainScreen] bounds].size.width;
    self.backgroundColor = [UIColor clearColor];
    
    self.babyInfo = [UILabel new];
    [self.mainView addSubview:self.babyInfo];
    self.babyInfo.font = [UIFont systemFontOfSize:11];
    [self.babyInfo imy_setTextColorForKey:kCK_Black_BT];

    self.timeLabel = [UILabel new];
    [self.mainView addSubview:self.timeLabel];
    self.timeLabel.font = [UIFont imy_FontWith:11];
    [self.timeLabel imy_setTextColorForKey:kCK_Black_BT];
    
    self.ipLabel = [UILabel new];
    [self.mainView addSubview:self.ipLabel];
    self.ipLabel.font = [UIFont imy_FontWith:11];
    [self.ipLabel imy_setTextColorForKey:kCK_Black_BT];

    self.richParserView.imy_width = self.imy_width - 30;
    self.richParserView.backgroundColor = [UIColor clearColor];
    self.richParserView.userInteractionEnabled = YES;

    [self.mainView imy_setBackgroundColorForKey:kISY_White];
    [self.mainView addSubview:self.authView];

    [self.titleLabel imy_setTextColorForKey:kCK_Black_A];
    self.titleLabel.backgroundColor = [UIColor clearColor];
    self.titleLabel.font = [UIFont ttqMediumFontWith:21];
    self.titleLabel.lineSpacing = 2;
    self.titleLabel.paragraphSpacing = 1;
    self.titleLabel.imyr_analysisDynamicEmoticon = NO;
    self.titleLabel.textAlignment = kCTTextAlignmentJustified;
    self.titleLabel.lineBreakMode = kCTLineBreakByWordWrapping;
    
    self.attentionAndTotalCountLabel = [[UILabel alloc] init];
    self.attentionAndTotalCountLabel.font = [UIFont imy_FontWith:13];
    [self.attentionAndTotalCountLabel imy_setTextColorForKey:kCK_Black_B];

    self.followButton = [[IMYCapsuleButton alloc] init];
    [self.followButton.titleLabel setFont:[UIFont systemFontOfSize:11]];
    self.followButton.lineWidth = 1/SCREEN_SCALE;
    
    [self.mainView addSubview:self.nameLabel];
    [self.mainView addSubview:self.followButton];
    [self.mainView addSubview:self.avatarExtentsionArea];
    [self.mainView addSubview:self.avatarBtn];
    [self.mainView addSubview:self.attentionAndTotalCountLabel];
    
    [self.avatarExtentsionArea mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.titleLabel.mas_bottom).mas_offset(16);
        make.size.mas_equalTo(CGSizeMake(36, 36));
        make.left.equalTo(@(12));
    }];
    
    [self.avatarBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.titleLabel.mas_bottom).mas_offset(16);
        make.width.height.equalTo(@36);
        make.left.equalTo(@(12));
    }];
    
    CGFloat nameLabelInterval = 8;
    
    [self.nameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.avatarBtn.mas_right).offset(nameLabelInterval);
        make.height.equalTo(@(20));
        make.top.equalTo(self.avatarBtn.mas_top).offset(1.0);
    }];
    
    [self.authView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.nameLabel.mas_right).offset(4);
        make.top.equalTo(self.avatarBtn);
        make.centerY.equalTo(self.nameLabel);
        make.right.lessThanOrEqualTo(self.mainView).mas_offset(-12);
        make.size.mas_equalTo(CGSizeMake(0, 16));
    }];
    
    [self.babyInfo mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.avatarBtn.mas_right).offset(nameLabelInterval);
        make.right.equalTo(@(-12));
        make.height.equalTo(@(20));
        make.top.equalTo(self.nameLabel.mas_bottom);
    }];
    
    [self.timeLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.avatarBtn.mas_right).offset(nameLabelInterval);
        make.height.mas_equalTo(16);
        make.top.equalTo(self.nameLabel.mas_bottom);
    }];
    
    [self.ipLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.timeLabel.mas_right).offset(nameLabelInterval);
        make.centerY.equalTo(self.timeLabel.mas_centerY);
        make.height.mas_equalTo(16);
    }];
    
    [self.attentionAndTotalCountLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(@(12));
        make.centerY.equalTo(self.followButton);
    }];
    
    @weakify(self);
    [[[[self.followButton rac_signalForControlEvents:UIControlEventTouchUpInside] deliverOnMainThread] takeUntil:self.rac_willDeallocSignal] subscribeNext:^(id x) {
        @strongify(self);
        if (self.tapFollowAction) {
            self.tapFollowAction(x);
        }
    }];
    
    self.followButton.hidden = [IMYPublicAppHelper isYunqi];//柚宝宝问答暂时隐藏关注问题按钮793jer
}

- (UIView *)answerChannelView {
    UIView *view = [UIView new];
    [view bk_whenTapped:^{
        [[IMYURIManager shareURIManager] runActionWithString:@"answer"];
        
        [IMYGAEventHelper postWithPath:@"event" params:@{@"event": @"xqy_wdrk", @"action": @(2)} headers:nil completed:nil];
    }];
    
    [view imy_lineViewWithDirection:IMYDirectionDown show:YES margin:0];
    
    UILabel *title = [UILabel new];
    title.font = [UIFont imy_MediumFontWith:15];
    [title imy_setTextColorForKey:kCK_Black_A];
    [view addSubview:title];
    [title mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(view);
        make.left.equalTo(@16);
    }];
    IMYSwitchModel *channelSwitch = [[IMYDoorManager sharedManager] switchForType:@"qas_home_lead_text"];
    NSString *serviceTitle = channelSwitch.dataDictionary[@"title"];
    title.text = [NSString imy_isBlankString:serviceTitle] ? @"问答频道" : serviceTitle;
    
    UILabel *content = [UILabel new];
    content.font = [UIFont imy_MediumFontWith:14];
    [content imy_setTextColorForKey:kCK_Red_B];
    [view addSubview:content];
    [content mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(view);
        make.right.equalTo(@(-28));
        make.left.greaterThanOrEqualTo(title.mas_right).offset(10);
    }];
    [title setContentCompressionResistancePriority:1000 forAxis:UILayoutConstraintAxisHorizontal];
    NSString *serviceContent = channelSwitch.dataDictionary[@"text"];
    content.text = [NSString imy_isBlankString:serviceContent] ? @"去看看" : serviceContent;
    
    UIImageView *image = [[UIImageView alloc] initWithImage:[UIImage imageNamed:@"wenda_icon_jiantou"]];
    [view addSubview:image];
    [image mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(content);
        make.height.equalTo(@(8));
        make.width.equalTo(@(4.4));
        make.right.equalTo(@(-15.6));
    }];
    
    return view;
}

- (void)dealloc {
    TTQVoteView *voteView = [self.contentView imy_findSubviewWithClass:[TTQVoteView class]];
    [voteView clearTimerHelper];
}

- (instancetype)bindModel:(TTQTopicModel *)topicModel cellForRowAtIndexPath:(NSIndexPath *)indexPath viewModel:(IMYQATopicViewModel *)viewModel {
    self.viewModel = viewModel;
    self.bindingModel = topicModel;
    BOOL isShow = topicModel.publisher.error == 0 && topicModel.publisher.userID != [[IMYPublicAppHelper shareAppHelper].userid integerValue];
    [self updatefollowButtonShow:isShow status:topicModel.is_followup];
    
    [self setupAvatarWithModel:topicModel];

    self.nameLabel.text = topicModel.publisher.screen_name;
    [self updateBadgesWithModel:topicModel];

    CGFloat imageViewsHeight = 0;
    if (self.viewModel.isPregnancyAnswer) {
        NSString *content = [topicModel.content stringByReplacingOccurrencesOfString:@"<img[^>]*>" withString:@"" options:NSCaseInsensitiveSearch | NSRegularExpressionSearch range:NSMakeRange(0, [topicModel.content length])];
        content = [self filterVideoHtmlString:content];
        
        NSString *titlecontent = [topicModel.title stringByReplacingOccurrencesOfString:@"<img[^>]*>" withString:@"" options:NSCaseInsensitiveSearch | NSRegularExpressionSearch range:NSMakeRange(0, [topicModel.title length])];
        titlecontent = [self filterVideoHtmlString:titlecontent];
        
        topicModel.title = titlecontent;
        topicModel.content = content;
        
        CGFloat horizontalSpacing = 12.0f;
        CGFloat horizontalItemSpacing = 4.0f;
        CGFloat verticalItemSpacing = 4.0f;
        NSInteger maxItemSingleLine = 4;
        CGFloat itemHeight = (SCREEN_WIDTH - horizontalSpacing * 2 - (maxItemSingleLine - 1) * horizontalItemSpacing) / maxItemSingleLine;
        
        NSMutableArray *btns = [NSMutableArray new];
        NSMutableArray *imgViews = [NSMutableArray new];
        [self.imgViews makeObjectsPerformSelector:@selector(removeFromSuperview)];
        [self.imgViews removeAllObjects];
        self.imgViews = nil;
        
        if (!self.lowQualityViews) {
            self.lowQualityViews = [[NSMutableArray alloc] init];
        }
        [self.lowQualityViews makeObjectsPerformSelector:@selector(removeFromSuperview)];
        [self.lowQualityViews removeAllObjects];
        
        for (NSUInteger i = 0; i < self.viewModel.topic.images.count; i++) {
            NSString *url = self.viewModel.topic.images[i];
            @weakify(self);
            UIImageView *imgView = [UIImageView new];
            imgView.clipsToBounds = YES;
            imgView.contentMode = UIViewContentModeScaleAspectFill;
            [imgViews addObject:imgView];
            [self.contentView addSubview:imgView];
            [imgView mas_makeConstraints:^(MASConstraintMaker *make) {
                make.top.equalTo(self.richParserView.mas_bottom).offset(12 + (i / 4 * (itemHeight + verticalItemSpacing)));
                make.height.width.equalTo(@(itemHeight));
                make.left.equalTo(@(horizontalSpacing + (i % 4) * (itemHeight + horizontalItemSpacing)));
            }];
            imgView.layer.cornerRadius = 8;
            [imgView imy_setBackgroundColorForKey:kCK_Black_F];
            imgView.imy_showViewSize = CGSizeMake(itemHeight, itemHeight);
            [imgView imy_setImageURL:url];
            UIButton *btn = [UIButton buttonWithType:UIButtonTypeCustom];
            [self.contentView addSubview:btn];
            [btn mas_makeConstraints:^(MASConstraintMaker *make) {
                make.edges.equalTo(imgView);
            }];
            [btn addTarget:self action:@selector(imgViewClick:) forControlEvents:UIControlEventTouchUpInside];
            [btns addObject:btn];
            
            BOOL isLowQuality = [self.viewModel.topic.imageQualityArray[i] boolValue];
            if (isLowQuality) {
                UIView *lowQualityView = [[UIView alloc] init];
                [self.lowQualityViews addObject:lowQualityView];
                [self.contentView addSubview:lowQualityView];
                [lowQualityView mas_makeConstraints:^(MASConstraintMaker *make) {
                    make.edges.equalTo(imgView);
                }];
                
                UIVisualEffectView *blurView = [[UIVisualEffectView alloc] initWithEffect:[UIBlurEffect effectWithStyle:UIBlurEffectStyleLight]];
                blurView.frame = CGRectMake(0, 0, itemHeight, itemHeight);
                blurView.alpha = 0.9;
                blurView.contentView.backgroundColor = [[UIColor imy_colorWithHexString:@"#999999"] colorWithAlphaComponent:0.3];
                [lowQualityView addSubview:blurView];
                
                UILabel *label1 = [[UILabel alloc] init];
                label1.numberOfLines = 0;
                label1.textAlignment = NSTextAlignmentCenter;
                [label1 imy_setTextColorForKey:kCK_White_A];
                label1.font = [UIFont systemFontOfSize:11];
                [label1 imy_setText:@"图片\n可能引起不适"];
                [lowQualityView addSubview:label1];
                
                UILabel *label2 = [[UILabel alloc] init];
                [label2 imy_setTextColorForKey:kCK_White_A];
                label2.font = [UIFont systemFontOfSize:11];
                [label2 imy_setText:@"点击查看"];
                [lowQualityView addSubview:label2];
                
                [label1 mas_makeConstraints:^(MASConstraintMaker *make) {
                    make.centerX.equalTo(lowQualityView);
                    make.top.equalTo(lowQualityView).offset((itemHeight-56)/2);
                }];
                
                [label2 mas_makeConstraints:^(MASConstraintMaker *make) {
                    make.centerX.equalTo(lowQualityView);
                    make.bottom.equalTo(lowQualityView).offset(-(itemHeight-56)/2);
                }];
                
                @weakify(lowQualityView);
                [lowQualityView bk_whenTapped:^{
                    @strongify(lowQualityView);
                    [UIView animateWithDuration:0.5 animations:^{
                        lowQualityView.alpha = 0;
                    }];
                    
                }];
            }
        }
        self.btns = btns;
        self.imgViews = imgViews;
        
        if (imgViews.count) {
            NSInteger rows = (imgViews.count + (maxItemSingleLine - 1)) / maxItemSingleLine;
            imageViewsHeight = rows * itemHeight + (rows - 1) * verticalItemSpacing + 12;
        }
    }
    
    
    CGFloat spaceHeight = 0.0;
    spaceHeight += imageViewsHeight;
    
    CGFloat titleTopInterval = 0;
    
    if (imy_isEmptyString(topicModel.title)) {
        titleTopInterval += 0.0;
        self.titleHeightConstra.constant = 0;
        self.titleLabel.hidden = YES;
    } else {
        [self.titleLabel setFont:[UIFont ttqMediumFontWith:21]];
        self.titleLabel.imy_width = self.imy_width - 24;
        [self.titleLabel setText:nil];
        [self.titleLabel setTopicDetailTitleWith:topicModel tagArray:nil];
        self.titleLabel.imy_width = self.imy_width - 24;
        
        titleTopInterval += 20.0;
        self.titleHeightConstra.constant = self.titleLabel.imy_height;
        self.titleLabel.hidden = NO;
        
        // 15=>标题到顶部距离
        spaceHeight += (20 + self.titleLabel.imy_height);
    }
    
    self.titleTopConstra.constant = titleTopInterval;
    [self.titleLabel setNeedsDisplay];
    
    // 头像间距
    spaceHeight += (16 + 36 + 8);
    
    NSString *timeString = [[self.viewModel.topic.published_date imy_getDateTime] imy_getDisplayTimeStringYear];
    self.timeLabel.text = timeString;
    if (self.viewModel.topic.ip_region.length > 0) {
        self.ipLabel.text = [NSString stringWithFormat:@"发布于 %@",self.viewModel.topic.ip_region];
    }
    
    NSString *publishBabyInfo = self.viewModel.publishBabyInfo;
    self.babyInfo.text = publishBabyInfo;
    self.richParserTopLayout.constant = 60;
    if (publishBabyInfo.length > 0) { // 时间label需要往下移动
        [self.timeLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.avatarBtn.mas_right).offset(8);
            make.height.mas_equalTo(16);
            make.top.equalTo(self.babyInfo.mas_bottom);
        }];
        if (imy_isNotEmptyString(self.viewModel.topic.ip_region) || imy_isNotEmptyString(self.timeLabel.text)) {
            spaceHeight += 16;
            self.richParserTopLayout.constant = 76;
        }
    } else {
        [self.timeLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.avatarBtn.mas_right).offset(8);
            make.height.mas_equalTo(16);
            make.top.equalTo(self.nameLabel.mas_bottom);
        }];
    }
    
    // 同时使用了Frame设置和自动布局会有问题，这里关注按钮重新设置下约束
    [self.followButton mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.mainView).offset(-12);
        make.size.mas_equalTo(self.followButton.imy_size);
        make.bottom.equalTo(self.mainView.mas_bottom).offset(-16);
    }];
    
    // 添加视频内容到content中，方便统一处理
    NSMutableString *htmlContent = [NSMutableString new];
    // 兼容处理
    if (topicModel.content) {
        [htmlContent appendString:[self filterVideoHtmlString:topicModel.content]];
    }
    if (topicModel.videos.count > 0) {
        TTQVideoModel *videoData = topicModel.videos.firstObject;
        CGFloat ratio = 0;
        if (videoData.width < 0.01 || videoData.height < 0.01) {
            ratio = 345.0 / 194;
        } else {
            ratio = MAX(345.0 / 474, videoData.width / videoData.height);
        }
        NSString *videoHtmlContent = [NSString stringWithFormat:@"<video src=\"%@\" ratio=\"%.3f\" poster=\"%@\" duration=\"%.2f\" size=\"%@\"></video>", videoData.video_url, ratio, videoData.thum_pic, videoData.videoDuration, videoData.size];
        [htmlContent appendString:videoHtmlContent];
    }
    
    self.richParserView.hidden = self.viewModel.isPregnancyAnswer;
    if (imy_isNotEmptyString(topicModel.content) && self.viewModel.isPregnancyAnswer) {
        self.richParserView.hidden = NO;
    }
    
    // 热议话题内容
    NSString *prefixText = nil;
    NSString *prefixLinkData = nil;
    [self.richParserView ttq_setText:htmlContent parseType:topicModel.parseType prefixText:prefixText prefixLinkData:prefixLinkData];
    [self.richParserView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(self.richParserView.imy_height);
    }];
    if (!self.richParserView.hidden && self.richParserView.imy_height > 1) {
        // 15 => 距离顶部关注按钮的距离
        spaceHeight += self.richParserView.imy_height;
    }
    __block UIView *view = self.richParserView;
    
    CGFloat voteViewHeight = [self voteViewHeighWithModel:topicModel];
    if (topicModel.vote.item_type_new == TTQVoteItemTypeTwoItems && topicModel.vote.items.count >= 2) {
        TTQCostomPKView *voteView = [self findOrAddPKView];
        [self voteViewUpdateConstrain:voteView];
        voteView.hidden = NO;
        voteView.imy_height = voteViewHeight;
        voteView.voteModel = topicModel.vote;
        @weakify(self);
        voteView.itemSelectedBlock = ^(NSArray * _Nonnull selectedItems) {
            @strongify(self);
            [self sendVoteRequestWithSelectedItems:selectedItems];
        };
        CGSize fittingSize = voteView.imy_size;
        [voteView mas_updateConstraints:^(MASConstraintMaker *make) {
            make.height.mas_equalTo(fittingSize.height);
        }];
        if ([view isKindOfClass:[UIImageView class]]) {
            spaceHeight += (12 + fittingSize.height);
        } else {
            spaceHeight += (12 + fittingSize.height);
        }
        view = voteView;
    } else if (topicModel.vote.item_type_new == TTQVoteItemTypeTwoImageItems && topicModel.vote.items.count >= 2) {
        TTQTwoImageVoteView *voteView = [self findOrAddTwoImageView];
        [self voteViewUpdateConstrain:voteView];
        voteView.hidden = NO;
        voteView.imy_height = voteViewHeight;
        [voteView setModel:topicModel.vote];
        @weakify(self);
        voteView.voteActionBlock = ^(NSArray * _Nonnull voteData, id<TTQFeedsVoteViewProtocol>  _Nonnull voteView) {
            @strongify(self);
            [self sendVoteRequestWithSelectedItems:voteData];
        };
        CGSize fittingSize = voteView.imy_size;
        [voteView mas_updateConstraints:^(MASConstraintMaker *make) {
            make.height.mas_equalTo(fittingSize.height);
        }];
        if ([view isKindOfClass:[UIImageView class]]) {
            spaceHeight += (12 + fittingSize.height);
        } else {
            spaceHeight += (12 + fittingSize.height);
        }
        view = voteView;
    } else if (topicModel.vote.item_type_new == TTQVoteItemTypeMutipleItems && topicModel.vote.items.count > 0) {
        TTQMultipleVoteView *voteView = [self findOrAddMutipleVoteView];
        [self voteViewUpdateConstrain:voteView];
        voteView.hidden = NO;
        voteView.imy_height = voteViewHeight;
        [voteView setModel:topicModel.vote];
        @weakify(self);
        voteView.voteActionBlock = ^(NSArray * _Nonnull voteData, id<TTQFeedsVoteViewProtocol>  _Nonnull voteView) {
            @strongify(self);
            [self sendVoteRequestWithSelectedItems:voteData];
        };
        voteView.voteFoldBlock = ^(BOOL isFold) {
            @strongify(self);
            UITableView *tableView = [self imy_findParentViewWithClass:UITableView.class];
            [tableView reloadData];
        };
        CGSize fittingSize = voteView.imy_size;
        [voteView mas_updateConstraints:^(MASConstraintMaker *make) {
            make.height.mas_equalTo(fittingSize.height);
        }];
        if ([view isKindOfClass:[UIImageView class]]) {
            spaceHeight += (12 + fittingSize.height);
        } else {
            spaceHeight += (12 + fittingSize.height);
        }
        view = voteView;
    }
    
    
    if (topicModel.link_body) {
        if (topicModel.link_body.type == 1) {
            [IMYEventHelper event:@"tz-gjbg"];
        }
        TTQLinkView *linkView = [self.contentView viewWithTag:-1001];
        if (linkView == nil) {
            linkView = [[TTQLinkView alloc] initWithFrame:(CGRect){12, 0, SCREEN_WIDTH - 24, 64}];
            linkView.lineSpacing = 2;
            linkView.paragraphSpacing = 1;
            linkView.tag = -1001;
            [self.mainView addSubview:linkView];
        }
        [linkView setText:topicModel.link_body.text subTitle:topicModel.link_body.tips];
        [linkView setIconUrl:topicModel.link_body.icon];
        linkView.clickedBlock = ^() {
            if (topicModel.link_body.type == 1) {
                [IMYEventHelper event:@"tz-gjdj"];
            }
            if (topicModel.link_body.error.length > 0 && topicModel.link_body.type == 1 && [IMYPublicAppHelper isYunqi]) {
                [UIView imy_showTextHUD:topicModel.link_body.error];
            } else if (topicModel.link_body.uri) {
                [[IMYURIManager shareURIManager] runActionWithString:topicModel.link_body.uri];
            }
        };
        [linkView mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(view.mas_bottom).offset(12);
            make.height.mas_equalTo(64);
            make.left.mas_equalTo(12);
            make.right.mas_equalTo(-12);
        }];
        view = linkView;
        spaceHeight += (12 + 64);
    } else {
        TTQLinkView *linkView = [self.contentView viewWithTag:-1001];
        [linkView removeFromSuperview];
    }
    
    @weakify(self);
    [[RACObserve(topicModel, total_review) deliverOnMainThread] subscribeNext:^(id x) {
        @strongify(self);
        [self setupCommentButton:topicModel.total_review];
    }];

    if (viewModel.topic_activity) {
        spaceHeight += 10 + 80;
        self.activityView.hidden = NO;
        self.activityView.userInteractionEnabled = YES;
        [self.activityView setWith:viewModel.topic_activity];
        @weakify(viewModel);
        [self.activityView bk_whenTapped:^{
            @strongify(viewModel);
            [IMYEventHelper event:@"htxq-hd"];
            if (!imy_isEmptyString(viewModel.topic_activity.redirect_uri)) {
                [[IMYURIManager shareURIManager] runActionWithString:viewModel.topic_activity.redirect_uri];
            }
        }];
    } else {
        self.activityView.hidden = YES;
    }

    // 把图文图层放在最上面，防止在当前图层上复制文本操作放大镜会被更上层的图层遮挡
    [self.mainView bringSubviewToFront:self.richParserView];
    
//    // 底部间距
//    spaceHeight += (10);
    
    // 底部关注按钮
    // 21:按钮到内容距离
    // 24:按钮到底部距离
    // 30:按钮高度
    spaceHeight += (12 + 28 + 16);

    // 设置当前View的高度
    self.imy_height = spaceHeight;
    ((UITableView *)[self imy_findParentViewWithClass:[UITableView class]]).tableHeaderView = self;
    
    return self;
}

- (NSString *)filterVideoHtmlString:(NSString *)originString {
    originString = [originString stringByReplacingOccurrencesOfString:@"<video (.*?)/video>" withString:@"" options:NSCaseInsensitiveSearch | NSRegularExpressionSearch range:NSMakeRange(0, [originString length])];
    
    return originString;
}

- (void)imgViewClick:(UIButton *)btn {
    NSUInteger index = [self.btns indexOfObject:btn];
    if (index == NSNotFound) {
        return;
    }
    
    NSMutableArray *array = [NSMutableArray new];
    for (NSString *url in self.viewModel.topic.images) {
        IMYPhoto *photo = [[IMYPhoto alloc] init];
        photo.url = [NSURL URLWithString:url];
        [array addObject:photo];
    }
        
    CGRect frame = [self convertRect:btn.frame toView:nil];
    // 若不指定 fromRect, 会造成控件 dismiss 时闪烁的问题
    IMYPhotoBrowser *browser = [IMYPhotoBrowser showWithPhotos:array atIndex:index fromRect:frame];
    browser.pageControlStyle = IMYBrowserPageControlStyleText;
    
    NSMutableDictionary *tmpDic = [[NSMutableDictionary alloc] init];
    tmpDic[@"action"] = @(2);
    tmpDic[@"info_type"] = @(12);
    tmpDic[@"info_id"] = @(self.bindingModel.topic_id);
    tmpDic[@"fuid"] = @(self.bindingModel.publisher.userID);
    tmpDic[@"event"] = @"dsq_nrxqy_ztnrtp";
    [IMYGAEventHelper postWithPath:@"event" params:[tmpDic copy] headers:nil completed:nil];
}


-(void)updateBadgesWithModel:(TTQCommentModel *)model{
    self.authView.hidden = YES;
    if (model.publisher.badges.count > 0) {
        self.authView.hidden = NO;
        [self.authView imy_removeAllSubviews];
        // 排序
        NSArray *badges = [model.publisher.badges sortedArrayUsingComparator:^NSComparisonResult(IMYMixHomeUserBadge * obj1, IMYMixHomeUserBadge * obj2) {
            if ([obj1.type isEqualToString:@"blue-v"]) {
                return NSOrderedAscending;
            }
            if ([obj2.type isEqualToString:@"blue-v"]) {
                return NSOrderedDescending;
            }
            return [obj1.type compare:obj2.type];
        }];

        UIView *lastItemView = nil;
        for (IMYMixHomeUserBadge *badge in badges) {
            IMYCKFeedsAuthenticationView *itemView = [[IMYCKFeedsAuthenticationView alloc] initWithFrame:CGRectMake(0, 0, 0, 16)];
            [self.authView addSubview:itemView];
            [itemView updateWithBadge:badge];
            CGFloat width = itemView.imy_width;
            
            [itemView mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.top.bottom.equalTo(self.authView);
                make.height.mas_equalTo(16);
                make.width.mas_equalTo(width);
                if (lastItemView) {
                    make.left.equalTo(lastItemView.mas_right).mas_offset(2);
                }else{
                    make.left.equalTo(self.authView);
                }
            }];
            lastItemView = itemView;
        }
        if (lastItemView) {
            [lastItemView mas_makeConstraints:^(MASConstraintMaker *make) {
                make.right.equalTo(self.authView);
            }];
        }
    
        [self.authView mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.centerY.equalTo(self.nameLabel);
            make.right.lessThanOrEqualTo(self.mainView).mas_offset(-12);
            make.left.equalTo(self.nameLabel.mas_right).mas_offset(4);
        }];
    }

    [self layoutIfNeeded];
}

// MARK: - Helper

+ (void)gotoUserIntroduce:(TTQTopicModel *)topicModel {
    if (topicModel.publisher.userID > 0 && topicModel.publisher.error == 0) {
        IMYURI *uri = [IMYURI uriWithPath:@"user/dynamic"
                                               params:@{ @"userID": @(topicModel.publisher.userID)
                                                         }
                                                 info:nil];
        [[IMYURIManager shareURIManager] runActionWithURI:uri];
        
    } else if (topicModel.publisher.error == 1) {
        [UIView imy_showTextHUD:kStatusText_UserAnonymous];
    } else if (topicModel.publisher.error == 2) {
        [UIView imy_showTextHUD:@"该用户主页暂未开放"];
    } else if (topicModel.publisher.error == 3){
        [UIView imy_showTextHUD:IMYString(kStatusText_homePageNotOpen)];
    }
    [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"dsq_nrxqy_tx",@"action":@2,@"info_type":@12,@"info_id":@(topicModel.topic_id),@"fuid":@(topicModel.publisher.userID),@"public_type":@1} headers:nil completed:nil];

}

- (NSString *)countString:(NSInteger)count {
    if (count < 1) {
        return IMYString(@"回复");
    }
    if (count < 10000) {
        return [NSString stringWithFormat:@"%ld", (long)count];
    } else {
        return [[NSString alloc] initWithFormat:@" %@%@", @(count / 10000), IMYString(@"万")];
    }
}

- (void)voteViewUpdateConstrain:(UIView*)voteView {
    __block UIView *view = self.richParserView;
    [voteView mas_remakeConstraints:^(MASConstraintMaker *make) {
        if ([view isKindOfClass:[UIImageView class]]) {
            make.top.equalTo(view.mas_bottom).offset(12);
        } else {
            make.top.equalTo(view.mas_bottom).offset(12);
        }
        make.left.mas_offset(15);
        make.right.mas_offset(-15);
        make.height.mas_equalTo(0);
    }];
}


- (void)setupCommentButton:(NSInteger)count {

}

- (NSString*)getColorKey{
    return kCK_Black_AT;
}

- (int)getWordFont{
    return 10;
}

- (void)sendVoteRequestWithSelectedItems:(NSArray *)selectedItems {
    if ([self.viewModel isKindOfClass:IMYQATopicViewModel.class]) {
        IMYQATopicViewModel *topicsViewModel = (IMYQATopicViewModel *)self.viewModel;
        [topicsViewModel requestVoteWithTopicModel:self.bindingModel selectedItems:selectedItems biParams:nil];
    } 
}

- (void)updatefollowButtonShow:(BOOL)show status:(NSInteger)followStatus {
    self.followButton.imy_size = CGSizeMake(68, 28);
    self.followButton.imy_right = self.mainView.imy_right - 12;
    if (followStatus) {
        [self.followButton imy_setTitle:IMYString(@"已关注")];
        self.followButton.borderColor = [IMYColor colorWithNormal:kCK_Black_K];
        self.followButton.contentColor = [IMYColor colorWithNormal:kCK_Black_K];
        self.followButton.titleColor = [IMYColor colorWithNormal:kCK_White_A];
    } else {
        [self.followButton imy_setTitle:IMYString(@"关注问题")];
        self.followButton.borderColor = [IMYColor colorWithNormal:kISY_Red];
        self.followButton.contentColor = [IMYColor colorWithNormal:kCK_Clear_A];
        self.followButton.titleColor = [IMYColor colorWithNormal:kISY_Red];
    }
    
    NSString *followupNum = @"0";
    if (self.viewModel.topic.followup_count > 0) { // 关注数
        followupNum = [NSString stringShowWithCount:self.viewModel.topic.followup_count];
    }
    NSString *totalviewNum = @"0";
    if (self.viewModel.topic.total_view > 0) { // 浏览数
        totalviewNum = [NSString stringShowWithCount:self.viewModel.topic.total_view];
    }
    self.attentionAndTotalCountLabel.text = [NSString stringWithFormat:@"%@ 姐妹关注 · %@ 姐妹浏览",followupNum,totalviewNum];
}

// MARK: - Getter
- (UIView *)authView {
    if (!_authView) {
        _authView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, 0, 16)];
    }
    return _authView;
}
- (TTQTopicActivityView *)activityView {
    if (_activityView == nil) {
        _activityView = [[TTQTopicActivityView alloc] init];
        [self addSubview:_activityView];
        [_activityView imy_setBackgroundColorForKey:kCK_White_A];
        [_activityView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.bottom.and.right.equalTo(self);
            make.height.mas_equalTo(80);
        }];
    }
    return _activityView;
}

- (TTQCostomPKView *)findOrAddPKView {
    TTQCostomPKView *pkView = nil;
    for (UIView *subView in self.mainView.subviews) {
        if ([subView isKindOfClass:TTQCostomPKView.class]) {
            pkView = (TTQCostomPKView *)subView;
            break;
        }
    }
    if (!pkView) {
        pkView = [[TTQCostomPKView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH - 30, 10)];
        [self.mainView addSubview:pkView];
    }
    return pkView;
}

- (TTQTwoImageVoteView *)findOrAddTwoImageView {
    TTQTwoImageVoteView *voteView = nil;
    for (UIView *subView in self.mainView.subviews) {
        if ([subView isKindOfClass:TTQTwoImageVoteView.class]) {
            voteView = (TTQTwoImageVoteView *)subView;
            break;
        }
    }
    if (!voteView) {
        voteView = [TTQTwoImageVoteView viewWithModel:[self voteModel]];
        [self.mainView addSubview:voteView];
    }
    return voteView;
}

- (TTQMultipleVoteView *)findOrAddMutipleVoteView {
    TTQMultipleVoteView *voteView = nil;
    for (UIView *subView in self.mainView.subviews) {
        if ([subView isKindOfClass:TTQMultipleVoteView.class]) {
            voteView = (TTQMultipleVoteView *)subView;
            break;
        }
    }
    if (!voteView) {
        voteView = [TTQMultipleVoteView viewWithModel:[self voteModel]];
        [self.mainView addSubview:voteView];
    }
    return voteView;
}

- (TTQVoteModel *)voteModel {
    TTQTopicModel *topicModel = nil;
    if ([self.bindingModel isKindOfClass:TTQTopicModel.class]) {
        topicModel = (TTQTopicModel *)self.bindingModel;
    }
    topicModel.vote.isInFeeds = NO;//标识为流里面的投票jer075
    return topicModel.vote;
}

- (IMYAvatarImageView *)avatarBtn {
    if (!_avatarBtn) {
        _avatarBtn = [[IMYAvatarImageView alloc] init];
        _avatarBtn.userInteractionEnabled = YES;
        @weakify(self);
        [_avatarBtn bk_whenTapped:^{
            @strongify(self);
            [self jumpToUserInfoPage:nil];
        }];
    }
    return _avatarBtn;
}

- (UIImageView *)avatarExtentsionArea {
    if (!_avatarExtentsionArea) {
        _avatarExtentsionArea = [[UIImageView alloc]init];
        _avatarExtentsionArea.userInteractionEnabled = YES;
        @weakify(self);
        [_avatarExtentsionArea bk_whenTapped:^{
            @strongify(self);
            [self jumpToUserInfoPage:nil];
        }];
    }
    return _avatarExtentsionArea;
}

- (UILabel *)nameLabel {
    if (!_nameLabel) {
        _nameLabel = [UILabel new];
        _nameLabel.font = [UIFont imy_FontWith:14];
        [_nameLabel imy_setTextColor:kCK_Black_AT];
        _nameLabel.textAlignment = NSTextAlignmentLeft;
        _nameLabel.lineBreakMode = NSLineBreakByTruncatingMiddle;
        UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(jumpToUserInfoPage:)];
        _nameLabel.userInteractionEnabled = YES;
        [_nameLabel addGestureRecognizer:tap];
    }
    
    return _nameLabel;
}

#pragma mark - Avatar

- (void)jumpToUserInfoPage:(UIButton *)sender {
    if ([TTQABTestConfig disableInteractByType:TTQ_Interact_type_userPage]) {
        return;
    }
    if (self.viewModel.topic.publisher.error == 2) {
        [UIView imy_showTextHUD:IMYString(@"该用户主页暂未开放")];
    } else if (self.viewModel.topic.publisher.error==1){
        [UIView imy_showTextHUD:kStatusText_UserAnonymous];
    } else if (self.viewModel.topic.publisher.error == 3){
        [UIView imy_showTextHUD:IMYString(kStatusText_homePageNotOpen)];
    }
    else if (self.viewModel.topic.publisher.error == 0) {
        if (self.viewModel.topic.publisher.userID <= 0) {
            return;
        }
        IMYURI *uri = [IMYURI uriWithPath:@"user/dynamic"
                                   params:@{ @"userID": @(self.viewModel.topic.publisher.userID),
                                             @"locationType": @(12),
                                             @"error": @(self.viewModel.topic.publisher.error) }
                                     info:nil];
        [[IMYURIManager shareURIManager] runActionWithURI:uri];
    }
    [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"dsq_nrxqy_tx",@"action":@2,@"info_type":@12,@"info_id":@(self.viewModel.topic.topic_id),@"fuid":@(self.viewModel.topic.publisher.userID),@"public_type":@1} headers:nil completed:nil];
}

- (void)setupAvatarWithModel:(TTQTopicModel*)model {
    [self.avatarBtn setAvatarWithURLString:model.publisher.user_avatar.large.length ? model.publisher.user_avatar.large : model.publisher.avatar placeholder:[UIImage imy_imageForKey:@"all_usericon"]];
}

#pragma mark -

- (CGFloat)voteViewHeighWithModel:(TTQTopicModel *)model {
    // FIXME: ZYT item_type_new enum define
    return [self voteViewHeightWithModel:model.vote];
    
}

- (CGFloat)voteViewHeightWithModel:(TTQVoteModel *)model {
    if (model.item_type_new == 1) {
        return [TTQCostomPKView viewHeightWithModel:model];
    } else if (model.item_type_new == 2) {
        //双图
        return [TTQTwoImageVoteView viewHeightWithModel:model];
    } else if (model.item_type_new == 3) {
        return [TTQMultipleVoteView viewHeightWithModel:model];
    }
    return 0;
}



@end

