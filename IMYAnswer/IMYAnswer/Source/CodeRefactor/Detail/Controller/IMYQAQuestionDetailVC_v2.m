//
//  IMYQAQuestionDetailVC_v2.m
//  IMY_ViewKit
//
//  Created by lzm on 2020/5/27.
//

#import "IMYQAQuestionDetailVC_v2.h"

#import "TCPSendMessage.h"
#import <IMYTTQ/TTQEmptyCommentModel.h>
#import <IMYTTQ/TTQJumpType.h>
#import <IMYTTQ/TTQMessageDetailViewModel.h>
#import <IMYTTQ/TTQSegmentModel.h>
#import <IMYTTQ/TTQTopicCache.h>

#import "IMYQACommentEmptyCell.h"
#import "IMYQADetailListCommonHeaderCell.h"
#import "IMYQARecommendCommentCell.h"
#import "IMYQATopicDetailCell.h"
#import "IMYQATopicDetailHeadView.h"
#import "IMYQATopicDetailInputTipToolView.h"
#import <IMYTTQ/IMYSelectableAttributedLabel.h>
#import <IMYTTQ/TTQNewFirstPageCell.h>
#import <IMYTTQ/TTQNewFirstPageVideoCell.h>
#import <IMYTTQ/TTQRefreshAutoNormalFooter.h>
#import <IMYTTQ/TTQTopicDetailForumView.h>
#import <IMYTTQ/TTQTopicJumpView.h>
#import <IMYTTQ/TTQTopicScreenPlaceholderCell.h>
#import <IMYTTQ/TTQUnfoldForumCell.h>

#import <IMYTTQ/TTQTopicReferenceViewModel.h>
#import <IMYTTQ/TTQTopics848ViewController.h>

#import "GCDObjC.h"
#import "IMYAdvertisementSDK.h"
#import "IMYQACommentShortEmptyCell.h"
#import "IMYQAGAEventHelper.h"
#import "IMYQAInputView.h"
#import "IMYQAQuestionAnswerModel.h"
#import "IMYQASearchCell.h"
#import "IMYQATopicDetailScreenTipToolView.h"
#import "IMYRM80AttributedLabelURL.h"
#import "IMYVideoPlayer.h"
#import "NSString+IMYR.h"
#import "POP.h"
#import "TCPClientManager.h"
#import "TTQTopicModel+IMYQA.h"
#import "TTQDetailHelper.h"
#import <IMYAccount/IMYAccountCheckService.h>
#import <IMYTTQ/IMYVideoView+TTQVideoPlayUtil.h>
#import <IMYTTQ/TTQABTestConfig.h>
#import <IMYTTQ/TTQCheckService.h>
#import <IMYTTQ/TTQDoorConfig.h>
#import <IMYTTQ/TTQHttpHelper.h>
#import <IMYTTQ/TTQPushManager.h>
#import <IMYTTQ/TTQRefreshBackNormalFooter.h>
#import <IMYTTQ/TTQShareView+TTQDetail.h>
#import <IMYTTQ/TTQTopicQuoteCell.h>
#import <IMYTTQ/TTQUriRegister.h>
#import <IMYTTQ/TTQVideoPlayerManager.h>
#import <IMYTTQ/UIFont+TTQ.h>
#import <IMYTTQ/UIScrollView+TTQ.h>
#import <IMYTTQ/UIView+TTQ.h>
#import <IMYTTQ/UIViewController+TTQ.h>
#import <IMYVideoPlayer/IMYVPlayerView+HL.h>
#import <IMYBaseKit/IMYCoolShareSheet.h>
#import <IMYTTQ/TTQDetailListHeaderModel.h>

#import "IMYQAAudioPlayer.h"
#import "IMYQAHomeCell.h"
#import "IMYQAHomeExpertCell.h"
#import "IMYQASearchCell.h"
#import "IMYQATopicScreenPlaceholderCell.h"
#import <IMYAccountServerURL.h>
#import <IMYAdvertisement/IMYSideAdManager.h>
#import <IMYAdvertisementSDK.h>
#import <MBProgressHUD+TTQ.h>
#import <TTQNewbeeTaskManager.h>
#import "IMYQADefine.h"
#import <IMYCommonKit/IMYCRefreshHeader.h>
#import <IMYUGC/IMYUGCTopicDetailStatusBar.h>
#if __has_include(<IMYEBPublic/IMYEBYoubiTaskManager.h>)
#import <IMYEBPublic/IMYEBYoubiTaskManager.h>
#endif
#import <IMYUGC/IMYUGCTaskManager.h>
#import <IMYUGC/IMYRewardAlertView.h>
#import <IMYUGC/IMYUGCEventHelper.h>

static const NSInteger kQuestionDetailVCInputViewMaxTextLength = 500;

@interface IMYQAQuestionDetailVC_v2 () <
IMYVideoViewDelegate, TTQTopicRichWithParserViewDelegate,
UIGestureRecognizerDelegate, IMYNewPageVideoViewDelegate,
ShowLargePhotosDelegate, IMYREasyInputViewDelegate>

@property(nonatomic, strong) TTQTopicDetailForumView *forumView;
///是否发送tcp消息了
@property(nonatomic, assign) Boolean sendTCP;
@property(nonatomic, strong) IMYQATopicDetailInputTipToolView *inputTipToolView;
@property(nonatomic, assign) BOOL isMute; //!< 是否静音播放
@property(nonatomic, strong) IMYPhotoBrowser *photoBrowser;

@property(nonatomic, strong)
IMYQATopicDetailScreenTipToolView *screenTipToolView; //筛选tool
@property(nonatomic, strong) UIView *scrollIndicator;     //滚动条
@property(nonatomic, strong) NSArray<TTQSegmentModel *> *segmentModels;
@property(nonatomic, strong) UIView *headView;
@property(nonatomic, strong) IMYCRefreshHeader *refreshHeader;

@property(nonatomic, strong) UIView *tableBgView;
@property(nonatomic, assign) BOOL isChangeFilterFailed;
@property(nonatomic, assign) TTQOrderByFilter originalFilter;
//视频播放相关
@property(nonatomic, assign) BOOL isMonitorADUrl;
@property(nonatomic, assign)
NSInteger lastTimeNetState; //上一次的移动网络状态 0.wifi 1.移动网络
@property(nonatomic, strong) IMYVideoView *currentPlayVideoView;
@property(nonatomic, strong) NSPointerArray *weakVideoViews;
@property(nonatomic, strong) IMYQAInputView *inputContentsView;

//表情雨
@property(nonatomic, strong) CAEmitterLayer *emitterLayer;
@property(nonatomic) CGAffineTransform transform;

//导航栏右边按钮
@property(nonatomic, strong) IMYTouchEXButton *topRightButton;
@property(nonatomic, assign) BOOL isNavigationBarBottomLineRealHide;
/**
 忽略更新悬浮View的标记位，切换Tab的方法changeCommentFilter中会把该值设置为YES，此时TableView的ContentOffset值还是上一个Tab的值，使用新的recommendHeaderStartY值进行处理会导致异常的情况（悬浮View先隐藏再显示），切换Tab的第一次更新悬浮View不处理
 */
@property(nonatomic, assign) BOOL ignoreUpdateTipView;

// 用于处理跳楼的逻辑
@property(nonatomic, assign) CGFloat maxOffsetValue;

// MARK: - bi埋点参数
@property(nonatomic, assign)
NSTimeInterval bi_startDuration; ///< 开始时间，记录每次播放开始时所处的时间
@property(nonatomic, assign)
NSTimeInterval bi_endDuration; ///< 终止时间，记录每次播放终止时所处的时间
/**
 开始类型：1 手动点击播放  2：手动重新播放  3
 恢复播放（包含：返回视频位置自动恢复播放，4g切换到wifi自动恢复播放）4
 播放完成后自动循环播放 5 流里自动播放  6 进入详情页自动播放
 */
@property(nonatomic, assign) NSInteger bi_startType;
/**
 终止类型（1是播放完成，2是暂停（含退出/切换））
 */
@property(nonatomic, assign) NSInteger bi_endType;
@property(nonatomic, copy) NSString *algorithm;
@property(nonatomic, copy) NSString *al_source;
@property(nonatomic, assign) BOOL firstEnterDetail; //首次进去详情页

@property(nonatomic, assign) BOOL gotoAlreadyRefresh;
@property(nonatomic, assign) NSInteger qaGotoID;

@property(nonatomic, strong) UILabel *answerCountlabel;
@property(nonatomic, strong) NSArray *templateCell;
@property(nonatomic, copy) NSString *reuseIdentifier;

@property(nonatomic, strong) MBProgressHUD *progressHUD; //图片上传进度展示

// 问答语音
@property(nonatomic, strong) IMYQAAudioPlayer *player;

@property(nonatomic, strong) id<IMYITableViewAdManager> adManager;
@property(nonatomic, strong) IMYSideAdManager *sideAdManager;
@property(nonatomic, assign) BOOL hasRequestSideAd;

//长按手势
@property(nonatomic, strong) NSIndexPath *longPressIndexPath; //当前长按的index
@property(nonatomic, strong)
TTQCommentModel *longPressCommentModel; //当前长按的commentmodel
@property(nonatomic, strong)
UIView *handleUIMenuItemView; //用于处理UIMenuitem的消失事件

@property (nonatomic, assign) BOOL canShare;//能不能分享
@property (nonatomic, assign) BOOL hasCheckShare;//在当前页面第一次检查能不能分享

@property(nonatomic, assign) BOOL hasRequest;

/// 帖子状态异常status bar
@property (nonatomic, strong) IMYUGCTopicDetailStatusBar *topicStatusTopView;
/// 最热评论曝光
@property (nonatomic, strong) UIView *commentBiViewHot;
/// 最新评论曝光
@property (nonatomic, strong) UIView *commentBiViewNew;

@property (nonatomic, assign) BOOL canReportInputViewChange;

/// 自定义导航栏
@property (nonatomic, strong) UIView *customNavigationBar;
@property (nonatomic, strong) UIView *bottomLineView;
@property (nonatomic, strong) UILabel *navTitleLabel;
@end

@implementation IMYQAQuestionDetailVC_v2

- (void)bindViewModel {
    [self registerCells];
    @weakify(self);
    
    [[[[RACObserve(self.viewModel, feedsSourceChange) skip:1]
       takeUntil:self.rac_willDeallocSignal] deliverOnMainThread]
     subscribeNext:^(id x) {
        @strongify(self);
        [self refreshTableView];
    }];
    
    [[[[[RACObserve(self.viewModel, recommendQuestions) skip:1] takeUntil:self.rac_willDeallocSignal] deliverOnMainThread] distinctUntilChanged] subscribeNext:^(id x) {
        @strongify(self);
        [self footerReload];
    }];
    
    IMYCRefreshHeader *refreshHeader = [IMYCRefreshHeader headerWithRefreshingBlock:^{
        @strongify(self);
        [self updateFooterWithMoreAction:NO];
        [self.tableView imy_footerEndRefreshing];
        [self.viewModel.requestRemoteDataCommand cancel];
        if (self.viewModel.datasourceOrderFilter != self.viewModel.orderByFilter) {
            self.viewModel.orderByFilter = self.viewModel.datasourceOrderFilter;
        }
        if (![IMYNetState networkEnable]) {
            [self.tableView imy_headerEndRefreshing];
            if(!self.hasRequest){
                [self.captionView setTitle:MT_Request_NoNet
                                  andState:IMYCaptionViewStateRetry];
            } else {
                [UIWindow imy_showTextHUD:IMYString(@"网络不见了，请检查网络")];
            }
            return;
        }
        // 下拉刷新需要重新请求贴边广告
        self.hasRequestSideAd = NO;
        [self requestRefresh:nil
                successBlock:^{
            self.qaGotoID = self.viewModel.qaGotoID;
            
            // 下拉刷新后不要 Goto
            self.viewModel.qaGotoID = -1;
        }];
    }];
    //  refreshHeader.showUpdateText = YES;
    self.tableView.mj_header = refreshHeader;
    self.refreshHeader = refreshHeader;
    
    RACSignal *viewtopicSignal = RACObserve(self.viewModel, topic);
    [[viewtopicSignal deliverOnMainThread] subscribeNext:^(id x) {
        @strongify(self);
        [self setupScreenTipToolView];
        [self updatePublisherInfoView];
        
        //广告监测
        [self monitorADUrl];
    }];
    [[[viewtopicSignal distinctUntilChanged]
      deliverOnMainThread] subscribeNext:^(TTQTopicModel *topic) {
        @strongify(self);
        if (topic) {
            if (self.forumView == nil) {
                // 贴士精华的话，直接隐藏
                BOOL isNotFromTopicsDetail =
                [self.navigationController.viewControllers bk_none:^BOOL(id vc) {
                    @strongify(self);
                    if ([vc isKindOfClass:[TTQTopics848ViewController class]]) {
                        if (((TTQTopics848ViewController *)vc).viewModel.forum_id ==
                            self.viewModel.forum_id) {
                            return true;
                        }
                    }
                    return false;
                }];
            }
            self.topRightButton.enabled = YES;
            self.inputTipToolView.userInteractionEnabled = YES;
            if (IOS9) { // spotlight
                IMYSpotlightItem *tipItem = [IMYSpotlightItem new];
                tipItem.title = topic.title;
                tipItem.content = topic.content;
                tipItem.domainIdentifier = @"kIMYTTQSpotlightDomainTopic";
                tipItem.uri = @"circles/group/topic";
                tipItem.icon = topic.publisher.user_avatar.large;
                tipItem.uriParams =
                @{ @"topicID" : @(topic.topic_id),
                   @"source" : @"Spotlight搜索" };
                if (topic.forum_name) {
                    tipItem.keywords = @[ topic.forum_name ];
                }
                tipItem.itemIdentifier =
                [NSString stringWithFormat:@"%lu_%@", topic.topic_id, topic.title];
                [[IMYSpotlightManager sharedManager] addSpotlightItems:@[ tipItem ]
                                                     completionHandler:nil];
            }
        } else {
            self.topRightButton.enabled = NO;
            self.inputTipToolView.userInteractionEnabled = NO;
        }
    }];
    // 处理评论删除通知
    [[[[NSNotificationCenter defaultCenter]
       rac_addObserverForName:TTQTopicReviewDidDeleteNotifition
       object:nil]
      deliverOnMainThread] subscribeNext:^(NSNotification *note) {
        @strongify(self);
        NSInteger commentID = [[note.object valueForKey:@"review_id"] integerValue];
        for (TTQSegmentModel *segmentModel in self.segmentModels) {
            segmentModel.dataSource =
            [segmentModel.dataSource bk_select:^BOOL(TTQCommentModel *model) {
                if ([model isKindOfClass:TTQCommentModel.class]) {
                    return model.commentID != commentID;
                } else {
                    return model;
                }
            }];
        }
        self.viewModel.dataSource =
        [self.viewModel.dataSource bk_select:^BOOL(TTQCommentModel *model) {
            if ([model isKindOfClass:TTQCommentModel.class]) {
                return model.commentID != commentID;
            } else {
                return YES;
            }
        }];
        [self.viewModel forceHandleAttachDatas];
        [self bringScreenTipToolToFront];
        [self refreshTableView];
    }];
    
    //    6.6.0 监听其他页面关注状态是否改变，是的话就更新model
    [[[[[NSNotificationCenter defaultCenter]
        rac_addObserverForName:@"mpnFollowNotification"
        object:nil] deliverOnMainThread]
      takeUntil:self.rac_willDeallocSignal] subscribeNext:^(NSNotification *x) {
        @strongify(self);
        NSUInteger userId = [[x.object valueForKey:@"userId"] integerValue];
        if (self.viewModel.topic.publisher.userID != userId) {
            return;
        }
        
        NSUInteger followStatus = [[x.object valueForKey:@"isFollow"] integerValue];
        if (followStatus > 1) {
            return;
        }
        self.viewModel.topic.publisher.is_followed = followStatus;
        [self updatePublisherInfoView];
        [self updateTopicDetailHeadView];
    }];
    
    [self ttq_addFooterWithMoreBlock:^{
        @strongify(self);
        [self.tableView imy_headerEndRefreshing];
        [self.viewModel.requestRemoteDataCommand cancel];
        if (self.viewModel.datasourceOrderFilter != self.viewModel.orderByFilter) {
            self.viewModel.orderByFilter = self.viewModel.datasourceOrderFilter;
        }
        if (![IMYNetState networkEnable]) {
            [UIWindow imy_showTextHUD:IMYString(@"网络不见了，请检查网络")];
            [self.tableView imy_footerEndRefreshing];
            return;
        }
        [self requestRemoteDataForType:1 params:nil];
    }];
    
    [[[NSNotificationCenter defaultCenter]
      rac_addObserverForName:kTTQForumChangeNotification
      object:nil] subscribeNext:^(NSNotification *x) {
        @strongify(self);
        if ([x.object isKindOfClass:[TTQForumModel class]] &&
            [(TTQForumModel *)x.object forum_id] == self.viewModel.forum_id) {
            self.viewModel.forum = x.object;
        }
    }];
    [[[[NSNotificationCenter defaultCenter]
       rac_addObserverForName:TTQTopicDetailChangeNotifition
       object:nil] deliverOnMainThread]
     subscribeNext:^(NSNotification *x) {
        @strongify(self);
        NSUInteger topicId =
        [[x.object valueForKey:@"topic_id"] unsignedIntegerValue];
        NSUInteger topic_status =
        [[x.object valueForKey:@"topic_status"] unsignedIntegerValue];
        if (topicId == self.viewModel.topic_id && topic_status == 1) {
            self.viewModel.showTableHeader = NO;
            [self refresh:NO];
        }
    }];
    
    // 监听关闭弹窗通知
    [[[[NSNotificationCenter defaultCenter]
       rac_addObserverForName:kQASafeLimitNotification
       object:nil] deliverOnMainThread]
     subscribeNext:^(NSNotification *x) {
        @strongify(self);
        [self closeAnswerInput];
    }];
    
    // 处理负反馈数据删除更新UI
    [[[RACObserve(self.viewModel, feedsSourceChange) skip:1] deliverOnMainThread]
     subscribeNext:^(id x) {
        @strongify(self);
        IMYQATopicDetailHeadView *headView =
        (IMYQATopicDetailHeadView *)self.tableView.tableHeaderView;
        [headView bindModel:self.viewModel.topic
      cellForRowAtIndexPath:nil
                  viewModel:self.viewModel];
        [self refreshTableView];
    }];
    
    [[[[[NSNotificationCenter defaultCenter]
        rac_addObserverForName:UIMenuControllerDidHideMenuNotification
        object:nil] deliverOnMainThread]
      takeUntil:self.rac_willDeallocSignal] subscribeNext:^(NSNotification *x) {
        @strongify(self);
        [self changeCellLongPressBackground];
    }];
}

- (void)viewDidLoad {
    self.player = [IMYQAAudioPlayer sharedInstance];
    [[UIApplication sharedApplication] beginReceivingRemoteControlEvents];
    self.viewModel.source = @"2";
    self.viewModel.isHideReferenceds = YES;
    self.firstEnterDetail = YES;
    self.transform = self.navigationController.navigationBar.transform;
    [super viewDidLoad];
    self.isMute = YES;
    self.view.imy_height = SCREEN_HEIGHT;
    [self.view imy_setBackgroundColorForKey:kCK_White_AN];
    self.tableView.showsHorizontalScrollIndicator = NO;
    // tableview 增加白色背景
    CGRect frame = self.view.bounds;
    frame.size.height -= (50 + SCREEN_TABBAR_SAFEBOTTOM_MARGIN - SCREEN_NAVIGATIONBAR_HEIGHT);
    UIView *bgView = [[UIView alloc] initWithFrame:frame];
    [bgView imy_setBackgroundColorForKey:kCK_Black_F];
    [self.tableView insertSubview:bgView atIndex:0];
    bgView.layer.zPosition = -1;
    bgView.userInteractionEnabled = NO;
    self.tableBgView = bgView;
    
    [self.tableView imy_setBackgroundColorForKey:kCK_Black_F];
    [self setupStatusBarStyle];
    [self setupTableViewForObserver];
    self.topRightButton.hidden = YES;

    [self requestOffsetCache];
    @weakify(self);
    [[[[[NSNotificationCenter defaultCenter]
        rac_addObserverForName:UIApplicationDidEnterBackgroundNotification
        object:nil] takeUntil:self.rac_willDeallocSignal]
      deliverOnMainThread] subscribeNext:^(id x) {
        @strongify(self);
        NSDictionary *dic = [self infoForTable];
        [NSObject imy_asyncBlock:^{
            @strongify(self);
            [self.viewModel saveOffsetCache:dic];
        }
                           level:IMYQueueLevelBackground];
    }];
    
    [[[IMYNetState networkChangedSignal] skipUntilBlock:^BOOL(id x) {
        @strongify(self);
        return self.isViewDidAppeared;
    }] subscribeNext:^(id x){
    }];
    
    [self setupTableFooter];
    [self bindViewModel];
    [self setupInputView];
    [self initADManager];
    
    IMYCKLoadingView *loading = [[IMYCKLoadingView alloc] initWithtype:IMYCKLoadingDetail];
    loading.frame = self.captionView.bounds;
    [self.captionView setStateView:loading forState:IMYCaptionViewStateLoading];
    
    [self.view bringSubviewToFront:self.captionView];
    
    [self addCommentBiView];
    
    [RACObserve(self.tableView, contentSize) subscribeNext:^(id  _Nullable x) {
        @strongify(self);
        imy_asyncMainBlock(0.1, ^{
            [self updateCommentBIView];
        });
    }];
    
    [self setupNavigationBar];
}


- (BOOL)isNavigationBarHidden {
    return YES;
}
- (void)setupNavigationBar {
    // 创建自定义导航栏
    CGFloat barHeight = SCREEN_STATUSBAR_NAVIGATIONBAR_HEIGHT;
    UIView *newNavBar = [[UIView alloc] initWithFrame:CGRectMake(0, -SCREEN_STATUSBAR_HEIGHT, SCREEN_WIDTH, barHeight)];
    [newNavBar imy_setBackgroundColorForKey:kIMY_BG];
    
    CGRect boxFrame = CGRectMake(0, SCREEN_STATUSBAR_HEIGHT, 38, barHeight - SCREEN_STATUSBAR_HEIGHT);
    IMYTouchEXButton *leftButton = [[IMYTouchEXButton alloc] initWithFrame:boxFrame];
    [leftButton imy_setImage:@"nav_btn_back_black"];
    leftButton.contentHorizontalAlignment = UIControlContentHorizontalAlignmentRight;
    [leftButton addTarget:self action:@selector(imy_topLeftButtonTouchupInside) forControlEvents:UIControlEventTouchUpInside];
    [newNavBar addSubview:leftButton];

    [self setupTopRightButton];
    [newNavBar addSubview:self.topRightButton];
    self.topRightButton.imy_right = newNavBar.imy_width - 6;
    self.topRightButton.imy_top = SCREEN_STATUSBAR_HEIGHT;
    
    UILabel *titleLabel = [[UILabel alloc] initWithFrame:CGRectMake(0, SCREEN_STATUSBAR_HEIGHT, SCREEN_WIDTH - 120, barHeight - SCREEN_STATUSBAR_HEIGHT)];
    titleLabel.font = [UIFont imy_mediumWith:17];
    titleLabel.textAlignment = NSTextAlignmentCenter;
    [titleLabel imy_setTextColorForKey:kCK_Black_A];
    [newNavBar addSubview:titleLabel];
    titleLabel.imy_centerX = newNavBar.imy_width / 2.0;
    self.navTitleLabel = titleLabel;
        
    [self.view addSubview:newNavBar];
    [newNavBar addSubview:self.bottomLineView];
    self.bottomLineView.imy_bottom = newNavBar.imy_height;
    self.customNavigationBar = newNavBar;
}

- (void)changeNavigaitonBarUIWithTableViewOffset:(CGFloat)offsetY {
    if (offsetY > 0) {
        if (offsetY < 8) {
            CGFloat alpha = offsetY / 8.0;
            self.bottomLineView.alpha = alpha;
        } else {
            self.bottomLineView.alpha = 1;
        }
    } else {
        self.bottomLineView.alpha = 0;
    }
}


// MARK: - AD Handler

- (void)initADManager {
    BOOL hasPopReply = [self isArray:0];
    NSDictionary *userInfo = @{
        @"topicId" : @(self.viewModel.topic_id),
        @"publisherId" : @(self.viewModel.topic.publisher.userID),
        @"custom_flag" : @"QADetail",
        @"no_setContentOffset":self.viewModel.gotoID > 0?@(NO):@(YES),
    };
    IMYAdvertiserInfo *adInfo =
    [IMYAdvertiserInfo adInfoWithSource:nil
                                   page:IMYADPageTopicDetail
                               position:0
                               userInfo:userInfo
                         viewController:self];
    self.adManager = [[IMYAdFactories getAdManagerFactory]
                      getTableViewAdManagerWithADInfo:adInfo];
    self.adManager.tableView = self.tableView;
    
    NSDictionary *sideUserInfo = @{
        @"shouldSide" : @(YES),
        @"topicId" : @(self.viewModel.topic_id),
        @"publisherId" : @(self.viewModel.topic.publisher.userID)
    };
    IMYAdvertiserInfo *sideAdInfo =
    [IMYAdvertiserInfo adInfoWithSource:nil
                                   page:IMYADPageSide
                               position:IMYADPositionTopicSidebar
                               userInfo:sideUserInfo
                         viewController:self];
    self.sideAdManager = [[IMYAdFactories getAdManagerFactory]
                          getTableViewAdManagerWithADInfo:sideAdInfo];
    [self.sideAdManager setTableView:self.tableView];
}

- (void)viewDidLayoutSubviews {
    [super viewDidLayoutSubviews];
    
    CGRect frame = self.view.bounds;
    
    frame.size.height -= (SCREEN_STATUSBAR_NAVIGATIONBAR_HEIGHT + self.inputTipToolView.imy_height);
    if(_topicStatusTopView != nil){
        frame.origin.y = 36 + SCREEN_NAVIGATIONBAR_HEIGHT;
        frame.size.height -= 36;
    } else {
        frame.origin.y = SCREEN_NAVIGATIONBAR_HEIGHT;
    }
    self.tableView.frame = frame;
    self.tableBgView.frame = frame;
    self.captionView.imy_top = self.tableView.imy_top;
    //    self.tableBgView.layer.zPosition = -1;
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    [self removeHandleUIMenuItemView];
    if ([TTQCommonHelp sharedCommonHelp].flowVideoMuteType == 1) {
        self.isMute = YES;
    } else if ([TTQCommonHelp sharedCommonHelp].flowVideoMuteType == 2) {
        self.isMute = NO;
    }
}

- (void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear:animated];
    NSNumber *lastTimeNetState =
    [[IMYUserDefaults standardUserDefaults] objectForKey:@"lastTimeNetState"];
    self.lastTimeNetState = lastTimeNetState.integerValue;
    [self refreshTableView];
    [self forcedToReply];
    
    // 视频：开始播放
    imy_asyncMainBlock(0.5, ^{
        NSLog(@"]======");
        if (self.view.window && self.view.imy_inWindowVisible) {
        }
    });
    if (self.openKeyboard && ![IMYRewardAlertView showInWindow]) {
        [self.inputContentsView.textView becomeFirstResponder];
        self.openKeyboard = NO;
    }
    [IMYUGCTaskManager getUGCTaskRequest];
}

- (void)viewWillDisappear:(BOOL)animated {
    [self removeHandleUIMenuItemView];
    [[UIApplication sharedApplication] setStatusBarHidden:NO];
    [[IMYUserDefaults standardUserDefaults]
     setObject:[NSNumber numberWithInteger:self.lastTimeNetState]
     forKey:@"lastTimeNetState"];
    [super viewWillDisappear:animated];
    if ([self imy_isPop]) {
        @weakify(self);
        NSDictionary *infoDic = [self infoForTable];
        [NSObject imy_asyncBlock:^{
            @strongify(self);
            [self GAEventForPOP:infoDic[@"lastIndexPath"]];
            [self.viewModel saveOffsetCache:infoDic];
        }
                           level:IMYQueueLevelBackground];
        if (self.viewModel.topic != nil && !self.viewModel.topic.is_favorite) {
            [[NSNotificationCenter defaultCenter]
             postNotificationName:TTQCollectTopicCancle
             object:@(self.viewModel.topic_id)];
        }
        if (self.viewModel.topic != nil && self.viewModel.topic.is_followup == 0) {
            [[NSNotificationCenter defaultCenter]
             postNotificationName:TTQFollowTopicCancle
             object:@(self.viewModel.topic_id)];
        }
        [[NSNotificationCenter defaultCenter]
         postNotificationName:@"kNewsDataRefreshNotification"
         object:nil
         userInfo:@{
            @"type" : @(1),
            @"itemId" : @(self.viewModel.topic.topic_id),
            @"comments" : @(self.viewModel.topic.total_review),
            @"isPraise" : @(self.viewModel.topic.has_praise),
            @"praise_num" : @(self.viewModel.topic.praise_num),
            @"userId" : @(self.viewModel.topic.publisher.userID),
            @"isFollow" :
                self.viewModel.topic.publisher.is_followed == 0 ? @NO
            : @YES
        }];
        BOOL jumpChainContainsAnswer = NO;
        for (UIViewController *vc in self.navigationController.viewControllers) {
            if ([vc isKindOfClass:NSClassFromString(@"IMYQAHomeContainerVC")] ||
                [vc isKindOfClass:NSClassFromString(@"TTQHome835ViewController")] ||
                [vc isKindOfClass:self.class]) {
                jumpChainContainsAnswer = YES;
                break;
            }
        }
        if (!jumpChainContainsAnswer && self.player) {// 如果回到问答首页流，就不重置播放器
            [self.player resetPlayer];
        }
    }
    TTQShareView *shareView = [[UIApplication sharedApplication].keyWindow
                               imy_findSubviewWithClass:[TTQShareView class]];
    [shareView dismiss];
    [self clearEmitterLayer];
    
    // 记录静音状态
    [TTQCommonHelp sharedCommonHelp].flowVideoMuteType =
    (self.isMute == YES) ? 1 : 2;
    
    // 视频：暂停播放
    if (self.currentPlayVideoView.videoType == TTQVideoViewTypeDetail) {
        if (!self.currentPlayVideoView.isFullScreen) {
            [self.currentPlayVideoView pause];
        }
    } else {
        if (!self.currentPlayVideoView.outterVideoView.isFullScreenPlay) {
            [self.currentPlayVideoView pauseByOutterVideoView];
        }
    }
}

- (UIStatusBarStyle)preferredStatusBarStyle {
    return UIStatusBarStyleDefault;
}

- (void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];
    NSLog(@"===didReceiveMemoryWarning==");
}

- (void)dealloc {
    NSLog(@"。。。。。。。释放了。。。。。。");
    [_inputContentsView removeFromSuperview];
    [_inputContentsView.myMaskView removeFromSuperview];
    _inputContentsView = nil;
    [self.tableView removeObserver:self forKeyPath:@"contentOffset"];
    [self.tableView.tableHeaderView removeObserver:self forKeyPath:@"frame"];
    [self.headView removeObserver:self forKeyPath:@"frame"];
}

// MARK: Business Method Override

- (BOOL)onlyDefaultEmoticon {
    return NO;
}

- (BOOL)isWhiteNavigationBar {
    return YES;
}

- (void)forceScrollToTop {
    self.tableView.contentOffset = CGPointMake(0, 0);
    if (!self.viewModel.showTableHeader) {
        [self refresh:NO];
    }
}

- (void)setupInputViewWhenViewDidAppear {
}

- (void)setupInputView {
    
    @weakify(self);
    [[RACObserve(self.viewModel, inputDefaultText) deliverOnMainThread]
     subscribeNext:^(NSString *text) {
        @strongify(self);
        if (text) {
            
        } else {
            self.viewModel.inputDefaultText = IMYString(@"添加回答：");
        }
    }];
    [self setupInputToolView];
}

- (void)hideKeyboard {
    if (!_inputContentsView) {
        return;
    }
    if (_inputContentsView.isFirstResponder) {
        [self hideKeyboardForceDispatchToMain:YES];
    }
}

- (void)hideKeyboardForceDispatchToMain:(BOOL)forceDispatchToMain {
    if (forceDispatchToMain) {
        @weakify(self);
        [[GCDQueue mainQueue] queueBlock:^{
            @strongify(self);
            [self.inputContentsView.textView resignFirstResponder];
        }];
    } else {
        // 父类发送图片时会调用
        [self.inputContentsView.textView resignFirstResponder];
    }
}

- (NSDictionary *)ga_appendParams {
    NSMutableDictionary *dict = [[super ga_appendParams] mutableCopy];
    if (self.search_key) {
        //如果有带搜索key，则追加到页面参数中
        dict[@"search_key"] = self.search_key;
    }
    
    dict[@"redirect_url"] = self.fromURI.uri;

    return dict;
}

- (void)closeAnswerInput {
    [_inputContentsView close];
}
// !!!: getter
- (IMYQAInputView *)inputContentsView {
    if (!_inputContentsView) {
        _inputContentsView = [[IMYQAInputView alloc]
                              initPhotoMultitudeInnerWithFrame:
                                  CGRectMake(0, SCREEN_STATUSBAR_HEIGHT, self.view.imy_width,
                                             SCREEN_HEIGHT - SCREEN_STATUSBAR_HEIGHT -
                                             SCREEN_TABBAR_SAFEBOTTOM_MARGIN)];
        _inputContentsView.title = self.viewModel.topic.title;
        _inputContentsView.textView.minNumberOfLines = 3;
        _inputContentsView.textView.maxNumberOfLines = 30;
        _inputContentsView.photoView.relay = true;
        _inputContentsView.photoView.controller = self;
        _inputContentsView.showEmojiImage = YES;
        _inputContentsView.delegate = self;
        _inputContentsView.maxTextLength = kQuestionDetailVCInputViewMaxTextLength;
        [self.navigationController.view addSubview:_inputContentsView];
        
        _inputContentsView.inputBottomOffset = 0;
        _inputContentsView.inputBottomForNone = SCREEN_HEIGHT;
        _inputContentsView.imy_bottom = _inputContentsView.inputBottomForNone;
        [_inputContentsView.cancelButton addTarget:self
                                            action:@selector(closeAnswerInput)
                                  forControlEvents:UIControlEventTouchUpInside];
        @weakify(self);
        [_inputContentsView.sendButton addTarget:self
                                          action:@selector(inputViewSend)
                                forControlEvents:UIControlEventTouchUpInside];
        [self.viewModel.replyCommand.executionSignals
         subscribeNext:^(RACSignal *innerSignal) { ///评论回复后回调
            @strongify(self)[[innerSignal deliverOnMainThread]
                             subscribeNext:^(id<IMYHTTPResponse> response) {
                if (self.tableView.contentSize.height <
                    self.tableView.imy_height) {
                    [self.tableView setContentOffset:CGPointZero animated:true];
                } else {
                    [self.tableView
                     setContentOffset:CGPointMake(
                                                  0, self.tableView.tableHeaderView
                                                  .imy_height +
                                                  10)
                     animated:true];
                }
                [self closeAnswerInput];
            }];
        }];
        [_inputContentsView.myMaskView bk_whenTapped:^{
            @strongify(self);
            [self hideKeyboard];
        }];
        [[RACObserve(self.viewModel, topic) deliverOnMainThread]
         subscribeNext:^(id x) {
            @strongify(self);
            ((IMYQAInputView *)self.inputContentsView).title =
            self.viewModel.topic.title;
        }];
        _inputContentsView.biCameraButtonBlk = ^{
            @strongify(self);
            NSDictionary *dict = @{@"event":@"dsq_nrxqy_xztp",
                                   @"action":@2,
                                   @"public_key":@1,
                                   @"public_type":@1};
            [IMYGAEventHelper postWithPath:@"event" params:dict headers:nil completed:nil];
        };
        [self setupSubViewInTurn];
        [self viewWillLayoutSubviews];
    }
    return _inputContentsView;
}

- (void)finishedReplyRequest:(NSString *)content
                   timestamp:(NSInteger)timestamp {
}

- (void)showReplySuccessHub {
    NSString *virsion = [[IMYUserDefaults standardUserDefaults] objectForKey:@"kShowedMyQATipWindow_virsion"];
    
    [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"dsq_nrxqy_pl",@"action":@2,@"info_type":@12,@"public_type":@(1),@"public_key":@(1),@"public_info":@"内容详情页",@"info_id":@(self.viewModel.topic_id),@"fuid":@(self.viewModel.topicUserID)} headers:nil completed:nil];
    
    if (imy_isEmptyString(virsion) || ![APPVersion isEqualToString:virsion]) {
        [[IMYUserDefaults standardUserDefaults] setValue:APPVersion forKey:@"kShowedMyQATipWindow_virsion"];
        [UIAlertController imy_showAlertViewWithTitle:@"回答成功"
                                              message:@"在\"我-个人主页-回答列表\"，可以查看发布过的所有回答"
                                    cancelButtonTitle:@"知道了"
                                    otherButtonTitles:nil
                                              handler:nil];
    } else {
        [UIWindow imy_showTextHUD:@"回答成功"];
    }
}

// MARK: - ShowLargePhotosDelegate 点击图片浏览
- (void)showWithPhotos:(NSMutableArray *)photos atIndex:(NSUInteger)index {
    if (_inputContentsView.isFirstResponder) {
        [self hideKeyboard];
        return;
    }
    self.photoBrowser = [IMYPhotoBrowser showWithPhotos:photos atIndex:index];
    self.photoBrowser.pageControlStyle = IMYBrowserPageControlStyleText;
}

// MARK: - Init

// MARK: Datas Getter

- (NSArray *)orderTitles {
    NSMutableArray *orderTitles = [[NSMutableArray alloc]
                                   initWithObjects:[TTQTopicOrderByModel new]
        .orderbyDescriptionWith(IMYString(@"最新"))
        .orderbyFilterWith(TTQOrderByFilterLastest),
                                   nil];
    if (self.viewModel.commentFilter == TTQTopicFilterAll) {
        [orderTitles insertObject:[TTQTopicOrderByModel new]
         .orderbyDescriptionWith(IMYString(@"最热"))
         .orderbyFilterWith(TTQOrderByFilterHot)
                          atIndex:0];
    }
    return orderTitles.copy;
}

- (NSArray<TTQSegmentModel *> *)segmentModels {
    if (_segmentModels == nil) {
        _segmentModels = @[
            [TTQSegmentModel new], [TTQSegmentModel new], [TTQSegmentModel new]
        ];
        for (TTQSegmentModel *model in _segmentModels) {
            model.gotoID = -1;
        }
    }
    return _segmentModels;
}

- (TTQCommentFilter)commentFilter:(TTQCommentFilter)commentFilter {
    if (!self.viewModel.topic.have_only_image_model &&
        commentFilter == TTQTopicFilterImage) {
        commentFilter = TTQTopicFilterNone;
    }
    return commentFilter;
}

- (TTQOrderByFilter)orderByFilter:(TTQOrderByFilter)orderByFilter
                    commentFilter:(TTQCommentFilter)commentFilter {
    if (commentFilter != TTQTopicFilterNone &&
        orderByFilter == TTQOrderByFilterHot) {
        orderByFilter = TTQOrderByFilterNone;
    }
    return orderByFilter;
}

/**
 获取第一个Section的高度，也就是顶部内容区域的高度加上10个像素分隔条的高度
 该位置用于处理
 - 1、【评论切换Tab View】吸顶效果
 - 2、定位到评论区
 */
- (CGFloat)firstSectionHeaderY {
    CGFloat sectionHeaderY = 0;
    IMYQATopicDetailHeadView *headView =
    (IMYQATopicDetailHeadView *)self.tableView.tableHeaderView;
    if ([self.tableView numberOfSections]) {
        CGRect headerFrame = [self.tableView rectForHeaderInSection:0];
        sectionHeaderY =
        CGRectGetMaxY(headerFrame) - self.screenTipToolView.imy_height - 10;
        // headView是Section=0的SectionHeaderView，出现一个场景：负反馈删除了相关问答数据之后刷新TableView,headView处于不可见状态，此时headView.superview为空，使用headView计算会出现偏差
        if (self.headView.superview && self.headView.imy_top > sectionHeaderY) {
            sectionHeaderY = self.headView.imy_top;
        }
    } else {
        sectionHeaderY = headView.imy_height;
    }
    sectionHeaderY += 10;
    return sectionHeaderY;
}

// MARK: Setup

- (void)setupStatusBarStyle {
    if ([IMYPublicAppHelper shareAppHelper].isNight) {
        self.statusBarStyle = UIStatusBarStyleLightContent;
    } else {
        self.statusBarStyle = UIStatusBarStyleDefault;
    }
    [[UIApplication sharedApplication] setStatusBarStyle:self.statusBarStyle];
}

- (void)setupTableViewForObserver {
    [self.tableView
     addObserver:self
     forKeyPath:@"contentOffset"
     options:(NSKeyValueObservingOptionNew | NSKeyValueObservingOptionOld)
     context:@"contentOffset"];
}

- (void)setupSubViewInTurn {
    [self.view bringSubviewToFront:self.inputTipToolView];
    [self.view bringSubviewToFront:self.captionView];
}

- (void)setupScreenTipToolView {
    if (self.screenTipToolView == nil) {
        @weakify(self);
        
        BOOL (^orderByAction)(NSInteger index, TTQTopicOrderByModel *orderbyModel,
                              IMYButton *button) =
        ^BOOL(NSInteger index, TTQTopicOrderByModel *orderbyModel,
              IMYButton *button) {
            @strongify(self);
            if (![IMYNetState networkEnable]) {
                [UIWindow
                 imy_showTextHUD:IMYString(@"网络不见了，请检查网络")];
                return NO;
            }
            if (orderbyModel.orderbyFilter != self.viewModel.orderByFilter ||
                (self.qaGotoID != -1 && self.gotoAlreadyRefresh == NO)) {
                self.viewModel.datasourceOrderFilter = self.viewModel.orderByFilter;
                [self changeOrderFilter:orderbyModel.orderbyFilter];
                
                if (self.qaGotoID != -1) {
                    self.gotoAlreadyRefresh = YES;
                }
            }
            switch (orderbyModel.orderbyFilter) {
                case TTQOrderByFilterHot: {
                    [IMYEventHelper event:@"htxq-rm"];
                    break;
                }
                case TTQOrderByFilterNone: {
                    [IMYEventHelper event:@"htxq-mr"];
                    break;
                }
                case TTQOrderByFilterLastest: {
                    [IMYEventHelper event:@"htxq-zx"];
                    break;
                }
                default:
                    break;
            }
            return YES;
        };
        self.screenTipToolView = [IMYQATopicDetailScreenTipToolView
                                  topicDetailScreenTipToolView:nil
                                  andMenuAction:nil
                                  orderbyTitles:[self orderTitles]
                                  andOrderbyAction:orderByAction];
        self.screenTipToolView.orderbyAppearActionBlock =
        ^(NSInteger index, TTQTopicOrderByModel *orderbyModel,
          IMYButton *orderbyButton) {
            if (orderbyButton.tag == 0 &&
                orderbyModel.orderbyFilter == TTQOrderByFilterNone) {
                [orderbyButton imy_setTitle:IMYString(@"默认")];
            }
        };
        self.screenTipToolView.actionTab.enableActionBlock = ^BOOL(
                                                                   NSInteger index) {
                                                                       @strongify(self);
                                                                       TTQSegmentModel *nextSegmentModel =
                                                                       self.segmentModels[[self commentFilter:index]];
                                                                       if (![IMYNetState networkEnable] &&
                                                                           nextSegmentModel.dataSource.count <= 1) {
                                                                           [UIWindow imy_showTextHUD:IMYString(@"网络不见了，请检查网络")];
                                                                           return NO;
                                                                       }
                                                                       return YES;
                                                                   };
        self.screenTipToolView.orderbyEnableBlock = ^BOOL(IMYButton *button) {
            @strongify(self);
            [self hideKeyboard];
            if (imy_isNotEmptyString(button.titleLabel.text)) {
                [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"dsq_nrxqy_hdlbqh",@"action":@2,@"info_type":@12,@"info_id":@(self.viewModel.topic_id),@"fuid":@(self.viewModel.topicUserID),@"public_type":button.titleLabel.text} headers:nil completed:nil];
            }
            
            //      if (![IMYNetState networkEnable]) {
            //        [UIWindow imy_showTextHUD:IMYString(@"咦？网络不见了，请检查网络连接")];
            //        return NO;
            //      }
            return YES;
        };
        
        [self.headView imy_setBackgroundColorForKey:kCK_Black_F];
        self.headView.userInteractionEnabled = YES;
        
        self.answerCountlabel = [UILabel new];
        [self.screenTipToolView addSubview:self.answerCountlabel];
        self.answerCountlabel.font = [UIFont boldSystemFontOfSize:17];
        [self.answerCountlabel imy_setTextColorForKey:kCK_Black_AT];
        [self.answerCountlabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.screenTipToolView).offset(15);
            make.bottom.equalTo(self.screenTipToolView).offset(-10);
        }];
        if (!self.screenTipToolView.superview) {
            [self.tableView addSubview:self.screenTipToolView];
        }
    } else {
        [self.screenTipToolView setOrerByButtonWith:[self orderTitles]];
    }
    
    [self changeCommentFilter:self.viewModel.commentFilter
                  orderFilter:self.viewModel.orderByFilter
                       action:NO];
}

- (void)setupTableHeader {
    @weakify(self);
    if (self.tableView.tableHeaderView || self.viewModel.topic == nil) {
        if (self.tableView.tableHeaderView && self.viewModel.topic) {
            IMYQATopicDetailHeadView *headView =
            (IMYQATopicDetailHeadView *)self.tableView.tableHeaderView;
            [headView setTapFollowAction:^(UIButton *btn) {
                @strongify(self);
                [self followQuestionAction:btn];
            }];
            [headView bindModel:self.viewModel.topic
          cellForRowAtIndexPath:nil
                      viewModel:self.viewModel];
        }
        return;
    }
    IMYQATopicDetailHeadView *headView =
    [[[NSBundle mainBundle] loadNibNamed:@"IMYQATopicDetailHeadView"
                                   owner:nil
                                 options:nil] lastObject];
    headView.isShowAnswerChannel =
    (!self.isFromAnswerHome &&
     ![IMYPublicAppHelper isYunqi]); //柚宝宝问答暂时不展示顶部分类条793jer
    headView.imyut_eventInfo.eventName = [NSString
                                          stringWithFormat:@"ttq_detail_content_%@", @(self.viewModel.topic_id)];
    headView.imyut_eventInfo.eventValue =
    @{ @"topicId" : @(self.viewModel.topic_id) };
    [headView setTapFollowAction:^(UIButton *btn) {
        @strongify(self);
        [self followQuestionAction:btn];
    }];
    [headView bk_whenTapped:^{
        @strongify(self);
        
        [self hideKeyboard];
    }];
    headView.richParserView.delegate = self;
    self.tableView.tableHeaderView = headView;
    [self.tableView.tableHeaderView
     addObserver:self
     forKeyPath:@"frame"
     options:(NSKeyValueObservingOptionNew | NSKeyValueObservingOptionOld)
     context:@"frame"];
    
    self.viewModel.topic.vote.isInFeeds = NO;
    [headView bindModel:self.viewModel.topic
  cellForRowAtIndexPath:nil
              viewModel:self.viewModel];
}

// MARK:- TableFooter 相关
// 统一使用她她圈上拉文案
- (void)ttq_addFooterWithMoreBlock:(void (^)())block {
    TTQRefreshBackNormalFooter *footer =
    [TTQRefreshBackNormalFooter footerWithRefreshingBlock:block];
    [RACObserve(self.viewModel, automaticallyRefresh) subscribeNext:^(id x) {
        footer.automaticallyRefresh = [x boolValue];
    }];
    self.tableView.mj_footer = footer;
}

- (void)setupTableFooter {
    // 有别于她她圈模块，边距置为0
    self.tableView.contentInset = UIEdgeInsetsZero;
}

// MARK:-
- (void)setupInputToolView {
    self.inputTipToolView = [IMYQATopicDetailInputTipToolView topicDetailInputTipToolView];
    [self.view addSubview:self.inputTipToolView];
    self.inputTipToolView.userInteractionEnabled = (self.viewModel.topic != nil);
    self.topRightButton.enabled = (self.viewModel.topic != nil);
    @weakify(self);
    [[GCDQueue mainQueue] queueBlock:^{
        @strongify(self);
        CGRect frame = self.view.frame;
        self.inputTipToolView.imy_bottom = frame.size.height - SCREEN_STATUSBAR_HEIGHT;
        self.tableView.imy_top = SCREEN_NAVIGATIONBAR_HEIGHT;
        self.tableView.imy_height = self.inputTipToolView.imy_top - self.tableView.imy_top;
        self.captionView.imy_top = self.tableView.imy_top;
    }];
    [[RACObserve(self.viewModel, topic.is_favorite) deliverOnMainThread]
     subscribeNext:^(id x) {
        @strongify(self);
        self.inputTipToolView.collectButton.selected =
        self.viewModel.topic.is_favorite;
    }];
    
    [[[self.inputTipToolView.collectButton
       rac_signalForControlEvents:UIControlEventTouchUpInside]
      deliverOnMainThread] subscribeNext:^(id x) {
        //收藏操作
        @strongify(self);
        if ([TTQABTestConfig disableInteractByType:TTQ_Interact_type_collect]) {
            return;
        }
        [IMYEventHelper event:@"qzxq-sc" attributes:@{@"来源" : @"右下角收藏"}];
        if ([IMYPublicAppHelper shareAppHelper].hasLogin) {
            [self.viewModel.favoriteCommand execute:nil];
        } else {
            [IMYEventHelper event:@"dl"
                          addType:0
                       attributes:@{@"来源" : @"话题详情"}];
            [UIWindow imy_showTextHUD:kStatusText_unLogin];
            [[IMYURIManager shareURIManager] runActionWithString:@"login"];
        }
    }];
    
    [[[self.inputTipToolView.shareButton
       rac_signalForControlEvents:UIControlEventTouchUpInside]
      deliverOnMainThread] subscribeNext:^(id x) {
        @strongify(self);
        if ([TTQABTestConfig disableInteractByType:TTQ_Interact_type_share]) {
            return;
        }
        if (self.viewModel.topic.category == TTQTopicCategoryUGCVideo &&
            self.viewModel.topic.topic_status != TTQTopicStatusNormal) {
            [UIView imy_showTextHUD:@"视频未审核通过，暂时不能分享~"];
            return;
        }
        [TTQShareView shareViewForOnlyShareWith:self.viewModel shareEventBlock:nil];
    }];
    
    [self.inputTipToolView.praiseView bindModel:self.viewModel.topic
                                      viewModel:self.viewModel];
    
    self.inputTipToolView.inputHandler = ^() {
        @strongify(self);
        [self replyToTopic];
        NSString *answerStatus = [self.viewModel commentCount] > 0 ? @"有回答" : @"无回答";
        [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"dsq_nrxqy_xhd",@"action":@2,@"info_type":@12,@"info_id":@(self.viewModel.topic_id),@"fuid":@(self.viewModel.topicUserID),@"public_type":answerStatus} headers:nil completed:nil];
    };
}

- (void)setupTopRightButton {
    @weakify(self);
    if ([self.viewModel.source isKindOfClass:NSString.class] &&
        [@"消息" isEqualToString:self.viewModel.source] &&
        [TTQPushManager bOpenPush] && [TTQPushManager bOpenMsg]) {
        [self.topRightButton
         imy_setTitle:[TTQPushManager containTopicId:self.viewModel.topic_id]
         ? @"重新通知"
         : @"取消通知"];
        self.topRightButton.titleLabel.font = [UIFont systemFontOfSize:14];
        void (^nextBlock)() = ^(RACSignal *signal) {
            [[signal deliverOnMainThread] subscribeNext:^(id x) {
                @strongify(self);
                self.topRightButton.userInteractionEnabled = YES;
                if ([TTQPushManager containTopicId:self.viewModel.topic_id]) {
                    [self.topRightButton imy_setTitle:@"重新通知"];
                    [UIWindow imy_showTextHUD:IMYString(@"取消通知成功")];
                } else {
                    [self.topRightButton imy_setTitle:@"取消通知"];
                    [UIWindow imy_showTextHUD:IMYString(@"重新通知成功")];
                }
            }
                                                  error:^(NSError *error) {
                @strongify(self);
                self.topRightButton.userInteractionEnabled = YES;
                [UIWindow imy_showTextHUD:IMYString(@"操作失败，请重试")];
            }];
        };
        
        [[self.topRightButton
          rac_signalForControlEvents:UIControlEventTouchUpInside]
         subscribeNext:^(id x) {
            @strongify(self);
            if ([TTQPushManager containTopicId:self.viewModel.topic_id]) {
                self.topRightButton.userInteractionEnabled = NO;
                nextBlock(
                          [[TTQPushManager shareInstance].topicBlacklistCommand execute:@{
                            @"topic_id" : @(self.viewModel.topic_id)
                          }]);
            } else {
                [IMYActionSheet
                 sheetWithCancelTitle:IMYString(@"取消")
                 otherTitles:@[ IMYString(@"确认取消通知") ]
                 summary:IMYString(@"取消通知之后，当您不在使用美柚"
                                   @"的时候\n将无法获得新的回复通"
                                   @"知")
                 showInView:self.navigationController.view
                 action:^(NSInteger index) {
                    @strongify(self);
                    if (index == 1) {
                        [IMYEventHelper event:@"xx-qxtz"];
                        self.topRightButton.userInteractionEnabled =
                        NO;
                        nextBlock(
                                  [[TTQPushManager shareInstance]
                                      .topicBlacklistCommand execute:@{
                                    @"topic_id" :
                                        @(self.viewModel.topic_id),
                                    @"forum_id" : @(self.viewModel.forum_id)
                                  }]);
                    }
                }];
            }
        }];
        return;
    }
    
    [self.topRightButton setImage:[UIImage imageNamed:@"tools_icon_black_gengduo.png"] forState:UIControlStateNormal];
    [self.topRightButton imy_setTitleColor:kCK_Black_A];
    [[self.topRightButton rac_signalForControlEvents:UIControlEventTouchUpInside]
     subscribeNext:^(id x) {
        @strongify(self);
        if (!self.hasCheckShare) {
            // 点击右上角 再当前页面检查一次是否可以分享即可
            [self checkUserCanShare];
        }
        
        [self shareForDetail];
    }];
    
    [self.view imy_addThemeChangedBlock:^(id weakObject) {
        @strongify(self);
        BOOL isNight = [IMYPublicAppHelper shareAppHelper].isNight;
        if (isNight) {
            [self.topRightButton setImage:[UIImage imageNamed:@"tools_icon_black_gengduo_naght.png"] forState:UIControlStateNormal];
        } else {
            [self.topRightButton setImage:[UIImage imageNamed:@"tools_icon_black_gengduo.png"] forState:UIControlStateNormal];
        }
        
    }];
}

- (void)setupInputViewStatusWithTextLength:(NSUInteger)length
                                  withText:(NSString *)text {
    self.inputContentsView.sendButton.enabled = YES;
    [self.inputTipToolView setInputTipText:@"写回答"];
}

// MARK: Update Mehtods

- (void)updateFooterWithMoreAction:(BOOL)onlyAppear {
    if (onlyAppear && self.tableView.mj_footer.refreshingBlock) {
        self.tableView.mj_footer.refreshingBlock = nil;
        [(TTQRefreshAutoNormalFooter *)self.tableView.mj_footer
         setTitle:IMYString(@"正在加载中...")
         forState:MJRefreshStateRefreshing];
        UIActivityIndicatorView *loadingView =
        [self.tableView.mj_footer valueForKey:@"loadingView"];
        if (loadingView) {
            loadingView.alpha = 0.0;
        }
    } else if (!onlyAppear && self.tableView.mj_footer.refreshingBlock == nil) {
        @weakify(self);
        self.tableView.mj_footer.refreshingBlock = ^{
            @strongify(self);
            [self.tableView imy_headerEndRefreshing];
            [self.viewModel.requestRemoteDataCommand cancel];
            if (self.viewModel.datasourceOrderFilter !=
                self.viewModel.orderByFilter) {
                self.viewModel.orderByFilter = self.viewModel.datasourceOrderFilter;
            }
            [self requestRemoteDataForType:1 params:nil];
        };
        [(TTQRefreshAutoNormalFooter *)self.tableView.mj_footer
         setTitle:IMYString(@"正在加载中...")
         forState:MJRefreshStateRefreshing];
        UIActivityIndicatorView *loadingView =
        [self.tableView.mj_footer valueForKey:@"loadingView"];
        if (loadingView) {
            loadingView.alpha = 1.0;
        }
    }
}

- (void)updateRecommendHeaderStartY {
    for (UITableViewCell *cell in self.tableView.visibleCells) {
        if ([cell isKindOfClass:TTQNewFirstPageCell.class] ||
            [cell isKindOfClass:TTQNewFirstPageVideoCell.class]) {
            // 如果是定位到相关推荐区域不处理，recommendHeaderStartY值不改变
            // 相关推荐点击进入详情页返回的情况会出现
            // self.viewModel.recommendHeaderStartY = MAXFLOAT;
            break;
        } else if ([cell isKindOfClass:IMYQADetailListCommonHeaderCell.class] ||
                   [cell isKindOfClass:IMYQASearchCell.class] ||
                   [cell isKindOfClass:IMYQARecommendCommentCell.class] ||
                   [cell isKindOfClass:IMYQAHomeExpertCell.class]) {
            NSIndexPath *indexPath = [self.tableView indexPathForCell:cell];
            CGRect rect = [self.tableView rectForRowAtIndexPath:indexPath];
            NSLog(@"rect = %@", NSStringFromCGRect(rect));
            if ([cell isKindOfClass:IMYQADetailListCommonHeaderCell.class]) {
                self.viewModel.recommendHeaderStartY = rect.origin.y;
            } else {
                self.viewModel.recommendHeaderStartY = rect.origin.y - 44;
            }
            break;
        } else {
            self.viewModel.recommendHeaderStartY = 0;
        }
    }
}

- (void)updateScreenTipToolView {
    
    if (self.ignoreUpdateTipView) {
        self.ignoreUpdateTipView = NO;
        return;
    }
    CGRect rect = self.screenTipToolView.frame;
    //    IMYQATopicDetailHeadView *headView = (IMYQATopicDetailHeadView
    //    *)self.tableView.tableHeaderView;
    CGFloat sectionHeaderY = [self firstSectionHeaderY];
    
    TTQTipToolViewAnimationType animationType = TTQTipToolViewAnimationTypeNone;
    CGRect destAnimationFrame = CGRectZero;
    UIView *topTipAnimateView = nil;
    
    CGFloat topTipViewHeight = 0;
    if (!self.screenTipToolView.hidden) {
        // 评论切换控件有显示，需要执行动画的是评论切换控件
        topTipViewHeight = self.screenTipToolView.imy_height;
        rect = self.screenTipToolView.frame;
        topTipAnimateView = self.screenTipToolView;
    } else if (!self.forumView.hidden) {
        // 评论切换控件没有显示，无评论的情况，存在圈子入口的View，需要执行动画的是圈子入口的View
        topTipViewHeight = self.forumView.imy_height;
        rect = self.forumView.frame;
        topTipAnimateView = self.forumView;
    }
    CGFloat headerOffset = 10;
    CGFloat realRecommendStartY =
    self.viewModel.recommendHeaderStartY + headerOffset - topTipViewHeight;
    // 相关推荐位置是在TabHeader下面的，所有会有：self.viewModel.recommendHeaderStartY
    // > sectionHeaderY
    if (self.viewModel.recommendHeaderStartY > 0.1 &&
        realRecommendStartY > sectionHeaderY &&
        self.tableView.contentOffset.y > realRecommendStartY) {
        // 1、列表位置大于相关推荐显示的位置，需要隐藏评论切换View和评论输入框
        rect.origin.y = realRecommendStartY;
        destAnimationFrame = CGRectMake(rect.origin.x, rect.origin.y - 100,
                                        rect.size.width, rect.size.height);
        animationType = TTQTipToolViewAnimationTypeHide;
    } else if (self.tableView.contentOffset.y > sectionHeaderY) {
        // 2、列表位置小于相关推荐显示的位置 &&
        // 大于详情内容区域高度，这种情况是需要吸顶显示评论切换View，需要展示评论切换View和评论输入框
        animationType = TTQTipToolViewAnimationTypeShow;
        rect.origin.y = self.tableView.contentOffset.y;
        if (nil == self.tableView.tableHeaderView && self.headView.superview) {
            rect.origin.y = rect.origin.y - 10;
        }
        destAnimationFrame = rect;
        // 设置顶部导航栏分割线不可见
        if (!self.isNavigationBarBottomLineRealHide) {
            self.isNavigationBarBottomLineRealHide = YES;
        }
        
    } else {
        // 3、列表位置小于详情内容区域高度，评论切换View显示并且是跟随列表滚动
        if (topTipAnimateView == self.screenTipToolView) {
            rect.origin.y = sectionHeaderY;
            if (nil == self.tableView.tableHeaderView && self.headView.superview) {
                rect.origin.y = rect.origin.y - 10;
            }
        }
        CGPoint contentOffset = self.tableView.contentOffset;
        if (contentOffset.y > 10 && self.isNavigationBarBottomLineRealHide) {// 顶部导航栏应该显示
            UIImage *image = [UIImage imy_imageFromColor:[UIColor imy_colorForKey:kCK_Black_J]  andSize:CGSizeMake(SCREEN_WIDTH, 1.0/SCREEN_SCALE)];
            self.isNavigationBarBottomLineRealHide = NO;
        }
    }
    
    // 执行动画
    [self updateTipViewWithType:animationType
                           rect:rect
             destAnimationFrame:destAnimationFrame
                 topAnimateView:topTipAnimateView];
}

- (void)updateTipViewWithType:(TTQTipToolViewAnimationType)animationType
                         rect:(CGRect)rect
           destAnimationFrame:(CGRect)destAnimationFrame
               topAnimateView:(UIView *)topAnimateView {
    NSLog(@"===Animation updateTipViewWithType animationType=%@  "
          @"screenTipToolView.state=%@",
          @(animationType), @(self.screenTipToolView.state));
    // 1、动画执行过程中不处理；2、隐藏状态需要执行异常动画不处理
    if (self.screenTipToolView.state ==
        TTQTipToolViewVisiableStateHideAnimating ||
        self.screenTipToolView.state ==
        TTQTipToolViewVisiableStateShowAnimating ||
        (self.screenTipToolView.state == TTQTipToolViewVisiableStateHide &&
         animationType == TTQTipToolViewAnimationTypeHide)) {
        // 不做处理
        NSLog(@"===Animation 不做处理");
    } else if ((self.screenTipToolView.state == TTQTipToolViewVisiableStateShow &&
                animationType == TTQTipToolViewAnimationTypeHide) ||
               (self.screenTipToolView.state == TTQTipToolViewVisiableStateHide &&
                animationType == TTQTipToolViewAnimationTypeShow)) {
        [self doAnimationWithType:animationType
                             rect:rect
               destAnimationFrame:destAnimationFrame
                   topAnimateView:topAnimateView];
    } else {
        NSLog(@"===Animation 设置Frame");
        // 1、显示状态，执行显示动画； 2、或者没有动画效果
        topAnimateView.frame = rect;
        if (topAnimateView.superview != self.tableView) {
            [topAnimateView removeFromSuperview];
            [self.tableView addSubview:topAnimateView];
            [self.tableView bringSubviewToFront:topAnimateView];
        }
    }
    [self.tableView bringSubviewToFront:self.screenTipToolView];
}

- (void)doAnimationWithType:(TTQTipToolViewAnimationType)animationType
                       rect:(CGRect)rect
         destAnimationFrame:(CGRect)destAnimationFrame
             topAnimateView:(UIView *)topAnimateView {
    return;
    if (topAnimateView.superview != self.view) {
        [topAnimateView removeFromSuperview];
        topAnimateView.imy_top = 0;
        topAnimateView.imy_left = 0;
        [self.view addSubview:topAnimateView];
        [self.view bringSubviewToFront:topAnimateView];
    }
    
    NSLog(@"===Animation 执行动画");
    // 计算评论输入框执行动画的位置
    CGRect inputViewFrame = CGRectZero;
    if (animationType == TTQTipToolViewAnimationTypeHide) {
        if ([self containRelatedQuestionOrAnswer]) {
            inputViewFrame = CGRectMake(
                                        0, self.view.imy_height - self.inputTipToolView.imy_height,
                                        self.inputTipToolView.imy_width, self.inputTipToolView.imy_height);
        } else {
            inputViewFrame =
            CGRectMake(0, self.view.imy_height, self.inputTipToolView.imy_width,
                       self.inputTipToolView.imy_height);
        }
    } else {
        inputViewFrame = CGRectMake(
                                    0, self.view.imy_height - self.inputTipToolView.imy_height,
                                    self.inputTipToolView.imy_width, self.inputTipToolView.imy_height);
    }
    
    CGRect topTabViewFrame = CGRectZero;
    CGFloat statusHeight = 0.0;
    if (_topicStatusTopView != nil) {
        statusHeight = 36;
    }
    if (animationType == TTQTipToolViewAnimationTypeHide) {
        topTabViewFrame =
        CGRectMake(0, -topAnimateView.imy_height+statusHeight, topAnimateView.imy_width,
                   topAnimateView.imy_height);
    } else {
        topTabViewFrame =
        CGRectMake(0, statusHeight, topAnimateView.imy_width, topAnimateView.imy_height);
    }
    
    // 更新TableView的高度
    CGFloat tableViewHeight = self.tableView.imy_height;
    if (animationType == TTQTipToolViewAnimationTypeHide) {
        if ([self containRelatedQuestionOrAnswer]) {
            tableViewHeight = self.view.imy_height - self.inputTipToolView.imy_height;
        } else {
            tableViewHeight = self.view.imy_height;
        }
        
    } else if (animationType == TTQTipToolViewAnimationTypeShow) {
        tableViewHeight = self.view.imy_height - self.inputTipToolView.imy_height;
    }
    
    NSTimeInterval duration = 0.3;
    //    if (self.isScrollingToTop) {
    //        // 置顶的时候减小动画时间
    //        duration = 0.02;
    //    }
    [UIView animateWithDuration:duration
                     animations:^{
        topAnimateView.frame = topTabViewFrame;
        self.inputTipToolView.frame = inputViewFrame;
        self.tableView.imy_height = tableViewHeight;
    }
                     completion:^(BOOL finished) {
        // 更新状态
        if (animationType == TTQTipToolViewAnimationTypeHide) {
            self.screenTipToolView.state = TTQTipToolViewVisiableStateHide;
        } else if (animationType == TTQTipToolViewAnimationTypeShow) {
            self.screenTipToolView.state = TTQTipToolViewVisiableStateShow;
        }
        
        // 更新消息按钮
        //                         [self.floatMsgButton
        //                         mas_updateConstraints:^(MASConstraintMaker
        //                         *make) {
        //                             if (animationType ==
        //                             TTQTipToolViewAnimationTypeHide) {
        //                                 if ([self
        //                                 containRelatedQuestionOrAnswer]) {
        //                                    make.bottom.mas_equalTo(-(25 +
        //                                    self.inputTipToolView.imy_height));
        //                                 } else {
        //                                     make.bottom.mas_equalTo(-(25));
        //                                 }
        //                             } else if (animationType ==
        //                             TTQTipToolViewAnimationTypeShow) {
        //                                 make.bottom.mas_equalTo(-(25 +
        //                                 self.inputTipToolView.imy_height));
        //                             }
        //                         }];
        [UIView animateWithDuration:0.3
                         animations:^{
            [self.view layoutIfNeeded];
        }];
    }];
    if (animationType == TTQTipToolViewAnimationTypeHide) {
        self.screenTipToolView.state = TTQTipToolViewVisiableStateHideAnimating;
    } else if (animationType == TTQTipToolViewAnimationTypeShow) {
        self.screenTipToolView.state = TTQTipToolViewVisiableStateShowAnimating;
    }
}

- (void)updateForumView {
    [self.tableView bringSubviewToFront:self.forumView];
}

- (void)resetToolTipView {
    TTQTipToolViewAnimationType animationType = TTQTipToolViewAnimationTypeShow;
    
    UIView *topAnimateView = self.screenTipToolView;
    
    CGFloat sectionHeaderY = [self firstSectionHeaderY];
    CGRect rect = self.screenTipToolView.frame;
    rect.origin.y = sectionHeaderY;
    if (nil == self.tableView.tableHeaderView) {
        rect.origin.y = rect.origin.y - 10;
    }
    
    [topAnimateView.layer removeAllAnimations];
    NSTimeInterval duration = 0.3;
    [UIView animateWithDuration:duration
                     animations:^{
        topAnimateView.frame = rect;
    }
                     completion:^(BOOL finished) {
        // 更新状态
        if (animationType == TTQTipToolViewAnimationTypeHide) {
            self.screenTipToolView.state = TTQTipToolViewVisiableStateHide;
        } else if (animationType == TTQTipToolViewAnimationTypeShow) {
            self.screenTipToolView.state = TTQTipToolViewVisiableStateShow;
        }
        
        // 更新TableView的高度
        if (animationType == TTQTipToolViewAnimationTypeHide) {
            if ([self containRelatedQuestionOrAnswer]) {
                self.tableView.imy_height =
                self.view.imy_height - self.inputTipToolView.imy_height;
            } else {
                self.tableView.imy_height = self.view.imy_height;
            }
        } else if (animationType == TTQTipToolViewAnimationTypeShow) {
            self.tableView.imy_height =
            self.view.imy_height - self.inputTipToolView.imy_height;
        }
        
        [UIView animateWithDuration:0.3
                         animations:^{
            [self.view layoutIfNeeded];
        }];
    }];
}

- (void)updateTableViewToFirstOrTopContent {
    if (self.tableView.tableHeaderView) {
        CGFloat sectionHeaderY = [self firstSectionHeaderY];
        if (self.tableView.contentOffset.y > sectionHeaderY) {
            self.tableView.contentOffset = CGPointMake(0, sectionHeaderY);
        }
    } else {
        if (self.tableView.contentOffset.y != 0) {
            self.tableView.contentOffset = CGPointMake(0, 0);
        }
    }
}

/**
 修正TableView的偏移，有可能偏移超过了TableView的高度
 */
- (void)updateTableViewToMaxOffset:(BOOL)animted {
    CGFloat maxOffsetY =
    MAX(0, self.tableView.contentSize.height - self.tableView.imy_height);
    if (self.tableView.contentOffset.y > maxOffsetY) {
        if ((maxOffsetY + self.tableView.mj_footer.imy_height) <=
            self.tableView.contentOffset.y) {
            maxOffsetY += self.tableView.mj_footer.imy_height;
        }
        [self.tableView setContentOffset:CGPointMake(0, maxOffsetY)
                                animated:animted];
    }
}

- (void)updateTableViewContentOffsetWith:(TTQSegmentModel *)nextSegmentModel
                        lastSegmentModel:(TTQSegmentModel *)lastSegmentModel {
    CGFloat sectionHeaderY = [self firstSectionHeaderY];
    if (nextSegmentModel.contentOffSet.y < sectionHeaderY) {
        if (lastSegmentModel.contentOffSet.y < sectionHeaderY) {
            nextSegmentModel.contentOffSet =
            CGPointMake(0, lastSegmentModel.contentOffSet.y);
        } else {
            nextSegmentModel.contentOffSet = CGPointMake(0, sectionHeaderY);
        }
    }
    self.tableView.contentOffset = nextSegmentModel.contentOffSet;
    imy_asyncMainBlock(0.5, ^{
        [self updateTableViewToMaxOffset:NO];
    });
}

- (void)updatePublisherInfoView {
}

- (void)updateTopicDetailHeadView {
    BOOL isSHow = self.viewModel.topic.publisher.error == 0 &&
    self.viewModel.topic.publisher.userID !=
    [[IMYPublicAppHelper shareAppHelper].userid integerValue];
    [(IMYQATopicDetailHeadView *)self.tableView.tableHeaderView
     updatefollowButtonShow:isSHow
     status:self.viewModel.topic.is_followup];
}

- (void)changeOrderFilter:(TTQOrderByFilter)orderByFilter {
    [self hideKeyboard];
    self.viewModel.gotoID = -1;
    //有可能有插楼。所以不能直接倒序
    [self.tableView imy_headerEndRefreshing];
    [self.tableView imy_footerEndRefreshing];
    [self.viewModel.requestRemoteDataCommand cancel];
    self.originalFilter = self.viewModel.orderByFilter;
    self.viewModel.orderByFilter = orderByFilter;
    [self updateFooterWithMoreAction:YES];
    [self.screenTipToolView startLoading];
    [self.tableView.mj_footer setState:MJRefreshStateRefreshing];
    self.isChangeFilterFailed = NO;
    @weakify(self);
    [self requestRefresh:^(NSError *error) {
        @strongify(self);
        if (self.isChangeFilterFailed) {
            self.viewModel.orderByFilter = self.viewModel.datasourceOrderFilter = self.originalFilter;
            [self.screenTipToolView setOrderAppearbyFilter:self.viewModel.orderByFilter];
        }
        //    self.viewModel.dataSource = nil;
        [self refreshTableView];
        [self setupTableHeader];
        [self bringScreenTipToolToFront];
        [self updateTableViewToFirstOrTopContent];
    }
            successBlock:^{
        @strongify(self);
        [self updateTableViewToFirstOrTopContent];
    }];
}

- (void)changeCommentFilter:(TTQCommentFilter)commentFilter
                orderFilter:(TTQOrderByFilter)orderByFilter
                     action:(BOOL)isAction {
    if (self.viewModel.commentFilter != commentFilter ||
        self.viewModel.orderByFilter != orderByFilter) {
        commentFilter = [self commentFilter:commentFilter];
        orderByFilter =
        [self orderByFilter:orderByFilter commentFilter:commentFilter];
        if (isAction) {
            [self hideKeyboard];
            [self.tableView imy_headerEndRefreshing];
            [self.tableView imy_footerEndRefreshing];
            [self.viewModel.requestRemoteDataCommand cancel];
            
            TTQSegmentModel *lastSegmentModel =
            self.segmentModels[self.viewModel.commentFilter];
            lastSegmentModel.contentOffSet = self.tableView.contentOffset;
            lastSegmentModel.dataSource = self.viewModel.dataSource;
            if (self.viewModel.datasourceOrderFilter ==
                self.viewModel.orderByFilter) {
                lastSegmentModel.orderByFilter = self.viewModel.orderByFilter;
            }
            lastSegmentModel.hasTableFooter = self.tableView.tableFooterView != nil;
            lastSegmentModel.gotoID = self.viewModel.gotoID;
            lastSegmentModel.automaticallyRefresh =
            self.viewModel.automaticallyRefresh;
            lastSegmentModel.commentFilter = self.viewModel.commentFilter;
            // 保存recommendHeaderStartY
            lastSegmentModel.recommendHeaderStartY =
            self.viewModel.recommendHeaderStartY;
            
            self.viewModel.commentFilter = commentFilter;
            self.viewModel.orderByFilter = orderByFilter;
            self.viewModel.datasourceCommentFilter = commentFilter;
            self.viewModel.datasourceOrderFilter = orderByFilter;
            
            TTQSegmentModel *nextSegmentModel = self.segmentModels[commentFilter];
            // 7.3需求 如果是只看楼主,则不需要跳楼
            self.viewModel.gotoID = nextSegmentModel.gotoID;
            if (commentFilter == TTQTopicFilterOwner) {
                // 7.3需求 如果是只看楼主,则不需要跳楼
                self.viewModel.gotoID = 0;
            }
            self.viewModel.automaticallyRefresh =
            nextSegmentModel.automaticallyRefresh;
            // 还原recommendHeaderStartY
            self.viewModel.recommendHeaderStartY =
            nextSegmentModel.recommendHeaderStartY;
            
            if ([self.tableView.mj_footer
                 isKindOfClass:TTQRefreshAutoNormalFooter.class]) {
                TTQRefreshAutoNormalFooter *footer =
                (TTQRefreshAutoNormalFooter *)self.tableView.mj_footer;
                footer.automaticallyRefresh = self.viewModel.automaticallyRefresh;
                [footer setTitle:(self.viewModel.automaticallyRefresh
                                  ? IMYString(@"上拉加载更多")
                                  : IMYString(@"已显示全部"))forState
                                :MJRefreshStateIdle];
            }
            
            if (nextSegmentModel.dataSource.count <= 1) {
                nextSegmentModel.orderByFilter = orderByFilter;
                if (lastSegmentModel.commentFilter == TTQTopicFilterNone &&
                    commentFilter == TTQTopicFilterOwner) {
                    NSIndexPath *indexPath =
                    [self.tableView indexPathsForVisibleRows].lastObject;
                    if (indexPath.section > 0 &&
                        self.tableView.contentOffset.y > SCREEN_HEIGHT) {
                        TTQCommentModel *model = nil;
                        if (indexPath.row == 0) {
                            model =
                            [self.viewModel tableCellCommentModelAtIndexPath:indexPath];
                        }
                        while (!([model isKindOfClass:TTQCommentModel.class] &&
                                 model.publisher.userID == self.viewModel.topic.user_id &&
                                 model.appoint == nil)) {
                            indexPath = [NSIndexPath indexPathForRow:0
                                                           inSection:indexPath.section - 1];
                            if (indexPath.section == 0) {
                                model = nil;
                                break;
                            }
                            model =
                            [self.viewModel tableCellCommentModelAtIndexPath:indexPath];
                        }
                        self.viewModel.gotoID = model.commentID;
                    }
                    self.viewModel.dataSource = nil;
                    [self updateFooterWithMoreAction:YES];
                    imy_asyncMainBlock(0.1, ^{
                        [self.tableView.mj_footer setState:MJRefreshStateRefreshing];
                    });
                    [self requestGoto];
                } else {
                    self.viewModel.dataSource = nil;
                    [self updateFooterWithMoreAction:YES];
                    imy_asyncMainBlock(0.1, ^{
                        [self.tableView.mj_footer setState:MJRefreshStateRefreshing];
                    });
                    [self requestRefresh:nil successBlock:nil];
                }
            } else {
                self.viewModel.dataSource = nextSegmentModel.dataSource;
                self.captionView.state = IMYCaptionViewStateHidden;
                if (nextSegmentModel.orderByFilter != orderByFilter) {
                    self.viewModel.datasourceOrderFilter = nextSegmentModel.orderByFilter;
                    [self changeOrderFilter:orderByFilter];
                } else {
                    [self requestAdList];
                }
            }
            
            // 设置shouldRequestPreviousPage为NO，在切换的另一个Tab的有数据的时候不请求上一页的数据，防止页面出现闪烁的情况
            if (nextSegmentModel.dataSource.count > 0) {
                self.viewModel.shouldRequestPreviousPage = NO;
                dispatch_after(
                               dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.2 * NSEC_PER_SEC)),
                               dispatch_get_main_queue(), ^{
                                   self.viewModel.shouldRequestPreviousPage = YES;
                               });
            } else {
                self.viewModel.shouldRequestPreviousPage = YES;
            }
            
            if (ABS(lastSegmentModel.contentOffSet.y -
                    nextSegmentModel.contentOffSet.y) > 2000) {
                self.ignoreUpdateTipView = YES;
            }
            
            ///是否需要显示原生广告
            [self setupTableHeader];
            [self bringScreenTipToolToFront];
            
            [self refreshTableView];
            [self updateTableViewContentOffsetWith:nextSegmentModel
                                  lastSegmentModel:lastSegmentModel];
            if (self.tableView.tableHeaderView && self.dataSource.count > 1 &&
                nextSegmentModel.orderByFilter == orderByFilter) {
                [self updateTableViewToMaxOffset:NO];
            }
            //切换标签的话，就改成是回复楼主
            self.viewModel.selectedReplyIndex = nil;
            self.viewModel.inputDefaultText = nil;
        }
    }
}

// 刷新table数据时，附加的一些操作
- (void)refreshTableView {
    [self.tableView reloadData];
    [self updateRecommendHeaderStartY];
    [self updateScreenTipToolView];
    // 这里使用延迟处理是因为reloadData调用之后和界面渲染完成之间有时间上的延迟，
    // 延迟0.1s等界面渲染完成之后再进行UI的布局
    // @see【【iOS】一进入帖子详情页未下发相关问答，再次刷新后下发相关问答，客户端显示异常】
    // https://www.tapd.cn/22362561/bugtrace/bugs/view/1122362561001044096
    imy_asyncMainBlock(0.1, ^{
        [self updateRecommendHeaderStartY];
        [self updateScreenTipToolView];
        [self updateForumView];
        [self bringScrollIndicatorToFront];
    });
}

- (void)bringScrollIndicatorToFront {
    if (!IOS13) {
        return;
    }
    //暂时的获取当前表格的进度条，这个最好类别方法
    if (self.scrollIndicator == nil) {
        for (UIView *view in self.tableView.subviews) {
            if ([view isKindOfClass:[UIImageView class]]) {
                self.scrollIndicator = view;
                break;
            }
        }
    }
    if (self.scrollIndicator) {
        [self.tableView bringSubviewToFront:self.scrollIndicator];
    }
}

- (void)bringScreenTipToolToFront {
    if (self.screenTipToolView) {
        [self.tableView bringSubviewToFront:self.screenTipToolView];
    }
}

// MARK: - 评论完成后销毁表情也图层
- (void)clearEmitterLayer {
    [self.emitterLayer removeFromSuperlayer];
    [self.emitterLayer removeAllAnimations];
    self.emitterLayer = nil;
}

// MARK: - Business

// MARK: 点赞

- (void)doPriaseWithModel:(TTQHome5TopicModel *)model
                hasPraise:(BOOL)hasPraise
            shouldRequest:(BOOL)shouldRequest
               completion:(void (^_Nullable)(BOOL isSuccess))completion {
    
    NSString *userId = [IMYPublicAppHelper shareAppHelper].userid;
    NSDictionary *param = @{
        @"topic_id" : @(model.topic_id),
        @"forum_id" : @(model.forum_id),
        @"owner_id" : userId,
        @"is_praise" : @(hasPraise),
        @"is_ask" : @NO
    };
    if (!shouldRequest) {
        return;
    }
    [self.viewModel
     doPriaseWithParam:param
     completion:^(id _Nullable resData, NSError *_Nullable error) {
        BOOL isSuccess = [resData boolValue];
        !completion ?: completion(isSuccess);
        if (!isSuccess) {
            [UIWindow imy_showHUDwithNetworkError:error
                                andResponseObject:error.af_responseData];
            model.praise_num -= 1;
            model.has_praise = NO;
        }
    }];
}

- (void)doHotPriaseWithModel:(TTQHotCommentModel *)hotModel
               andTopicModel:(TTQHome5TopicModel *)topicModel
                   hasPraise:(BOOL)hasPraise
               shouldRequest:(BOOL)shouldRequest
                  completion:(void (^_Nullable)(BOOL isSuccess))completion {
    NSString *userId = [IMYPublicAppHelper shareAppHelper].userid;
    NSDictionary *param = @{
        @"topic_id" : @(hotModel.topic_id),
        @"forum_id" : @(topicModel.forum_id),
        @"review_id" : @(hotModel.hotCommentID),
        @"owner_id" : userId,
        @"is_praise" : @YES,
        @"is_ask" : @NO
    };
    [self.viewModel
     doPriaseWithParam:param
     completion:^(id _Nullable resData, NSError *_Nullable error) {
        BOOL isSuccess = [resData boolValue];
        !completion ?: completion(isSuccess);
        if (!isSuccess) {
            [UIWindow imy_showHUDwithNetworkError:error
                                andResponseObject:error.af_responseData];
            hotModel.praise_num -= 1;
            hotModel.has_praise = NO;
        }
    }];
}

// MARK: 分享

-(void)checkUserCanShare{
    // 检查用户是否被禁止分享
    NSDictionary *parameters = @{ @"item_id": @(self.viewModel.topicUserID)};
    @weakify(self);
    [[[IMYServerRequest postPath:@"v5/check_user_status" host:circle_seeyouyima_com params:parameters headers:nil] deliverOnMainThread] subscribeNext:^(id  _Nullable x) {
        @strongify(self);
        NSDictionary *dic = [x responseObject];
        NSInteger canshare = [[dic valueForKey:@"is_article_share"] integerValue];
        self.canShare = canshare;
        self.hasCheckShare = YES;
    } error:^(NSError * _Nullable error) {
    }];
}


- (void)shareForDetail {
    [IMYEventHelper event:@"wdxqy_fx"
               attributes:@{
        @"mode" : @([IMYPublicAppHelper shareAppHelper].userMode)
    }];
    @weakify(self);
    IMYCoolShareConfig *config = [IMYCoolShareConfig baseShareConfig];
    
    IMYCoolShareItem *shareItem =
    [IMYCoolShareItem itemWithName:@"复制链接"
                              icon:@"all_share_btn_copylink"
                               tag:100001];
    shareItem.shareBlock = ^{
        UIPasteboard *pasteboard = [UIPasteboard generalPasteboard];
        pasteboard.string = self.viewModel.topic.share_url;
        [UIWindow imy_showTextHUD:@"复制成功"];
        [self postBIShareWithType:IMYCoolShareSheetTypeCopyLink];
    };
    
    IMYCoolShareItem *shareItem2 =
    [IMYCoolShareItem itemWithName:@"举报"
                              icon:@"all_share_btn_report"
                               tag:100000];
    shareItem2.shareBlock = ^{
        @strongify(self);
        if (![self loginActicon]) {
            return;
        }
        [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"dsq_nrxqy_gdan",@"public_type":@"举报",@"info_type":@12,@"info_id":@(self.viewModel.topic_id),@"fuid":@(self.viewModel.topic.publisher.userID),@"action":@2} headers:nil completed:nil];
        [TTQDetailHelper reportTopicAction:self.viewModel.topic_id postType:1 topicId:self.viewModel.topic_id callback:nil];
    };
    
    IMYCoolShareItem *shareItemDelete =
    [IMYCoolShareItem itemWithName:@"删除"
                              icon:@"all_share_btn_delete"
                               tag:100002];
    shareItemDelete.shareBlock = ^{
        // 删除问答帖
        @strongify(self);
        [self deleteNews];
    };
    
    NSString *userId = [NSString stringWithFormat:@"%@",@(self.viewModel.topic.publisher.userID)];
    
    if ([IMYPublicAppHelper isYunqi]) {
        //柚宝宝需要收藏按钮
        NSString *imageName = @"all_share_btn_favorited";
        NSString *title = @"收藏";
        if (self.viewModel.topic.is_favorite) {
            imageName = @"all_share_btn_favorited_hover";
            title = @"取消收藏";
        }
        IMYCoolShareItem *collectItem =
        [IMYCoolShareItem itemWithName:title icon:imageName tag:100002];
        collectItem.shareBlock = ^{
            @strongify(self);
            if ([IMYPublicAppHelper shareAppHelper].hasLogin) {
                [self.viewModel.favoriteCommand execute:nil];
            } else {
                [UIWindow imy_showTextHUD:kStatusText_unLogin];
                [[IMYURIManager shareURIManager] runActionWithString:@"login"];
            }
            self.viewModel.topic.is_favorite = !self.viewModel.topic.is_favorite;
        };
        if (![userId isEqualToString:[IMYPublicAppHelper shareAppHelper].userid]) {
            [config addShareItemsAtSecondShareItemsGroup:@[
                collectItem, shareItem, shareItem2
            ]];
        } else {
            [config addShareItemsAtSecondShareItemsGroup:@[
                collectItem, shareItem2
            ]];
        }
        
    } else {
        if (![userId isEqualToString:[IMYPublicAppHelper shareAppHelper].userid]) {
            [config addShareItemsAtSecondShareItemsGroup:@[ shareItem, shareItem2 ]];
        } else {
            [config addShareItemsAtSecondShareItemsGroup:@[ shareItem, shareItemDelete ]];
        }
        
    }
    
    NSMutableArray *configList = [NSMutableArray array];
    NSArray *firstItemGroup = [config createItems];
    if (firstItemGroup.count > 0) {
        [configList addObject:firstItemGroup];
    }
    if (config.secondShareItemsGroup.count > 0) {
        [configList addObject:config.secondShareItemsGroup];
    }
    // 如果帖子违规 只展示删除，其他均不展示
    if(![self.viewModel checkTopicValidate] && [userId isEqualToString:[IMYPublicAppHelper shareAppHelper].userid]){
        NSArray *arrDelete = @[shareItemDelete];
        [configList removeAllObjects];
        [configList addObject:arrDelete];
    }
    
    [IMYCoolShareSheet
     customShareInViewController:self.navigationController
     configList:[configList copy]
     indexBlock:^(IMYCoolShareSheetType itemType,
                  NSInteger shareType) {
        
        @strongify(self);
        if (shareType == -1) {
            return;
        }
        
        if (shareType < 9999) {
            /// locationID需要从外部传入
            /// 区分是顶部的分享还是底部bar上的分享
            if (self.hasCheckShare) {
                if (!self.canShare) {
                    [UIWindow imy_showTextHUD:@"内容限制分享"];
                    return;
                }
            }
            if (shareType > 0 &&
                [TTQABTestConfig disableInteractByType:
                 TTQ_Interact_type_share]) {
                return;
            }
            NSDictionary *paramsBI = [self
                                      getBIEventParamsByType:itemType
                                      topicID:self.viewModel.topic_id];
            [IMYGAEventHelper postWithPath:@"event"
                                    params:paramsBI
                                   headers:nil
                                 completed:nil];
            
            
            [self postBIShareWithType:itemType];
            
            [IMYPublicShareManager new]
                .title(self.viewModel.shareBody.title)
                .content(
                         imy_isNotEmptyString(
                                              self.viewModel.shareBody.content)
                         ? self.viewModel.shareBody.content
                         : [NSString stringWithFormat:
                            @"发现一个你可能感兴趣的"
                            @"问题\"%@\"",
                            self.viewModel.shareBody
                            .title])
                .shareType(shareType)
                .fromURL(self.viewModel.topic.share_url)
                .callback(^(BOOL isSuccess) {
                    if (isSuccess) {
                    }
                })
                .share();
        } else {
        }
        
    }];
}

- (NSDictionary *)getBIEventParamsByType:(IMYCoolShareSheetType)type
                                 topicID:(NSInteger)topicID {
    NSInteger tagID = 0;
    if (type == IMYCoolShareSheetTypeSinaWeibo) {
        tagID = 1;
    } else if (type == IMYCoolShareSheetTypeWeixiSession) {
        tagID = 2;
    } else if (type == IMYCoolShareSheetTypeWeixiTimeline) {
        tagID = 3;
    } else if (type == IMYCoolShareSheetTypeQQSpace) {
        tagID = 4;
    } else if (type == IMYCoolShareSheetTypeQQSession) {
        tagID = 5;
    }
    NSInteger locationFinal = 1;
    return @{
        @"topic_id" : @(topicID),
        @"event" : @"tzxqy_fx",
        @"action" : @2,
        @"location_id" : @(locationFinal),
        @"tag_id" : @(tagID)
    };
}

- (void)postBIShareWithType:(IMYCoolShareSheetType)type {
    NSString *public_type = @"";
    if (type == IMYCoolShareSheetTypeWeixiTimeline) {
        public_type = @"微信朋友圈";
    } else if (type == IMYCoolShareSheetTypeWeixiSession) {
        public_type = @"微信好友";
    } else if (type == IMYCoolShareSheetTypeQQSpace) {
        public_type = @"QQ空间";
    } else if (type == IMYCoolShareSheetTypeQQSession) {
        public_type = @"QQ好友";
    } else if (type == IMYCoolShareSheetTypeCopyLink) {
        public_type = @"复制链接";
    } else if (type == IMYCoolShareSheetTypeSinaWeibo) {
        public_type = @"微博";
    }
    
    [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"dsq_nrxqy_fxtz",@"action":@2,@"public_type":public_type,@"info_type":@12,@"info_id":@(self.viewModel.topic.topic_id),@"fuid":@(self.viewModel.topic.publisher.userID),@"is_on_period":@([IMYUGCEventHelper userIsInPhysiological]),@"redirect_url":[IMYUGCEventHelper currentPageRedirectUrl]} headers:nil completed:nil];
}


// MARK: 删除帖子

- (void)deleteNews {
    @weakify(self);
    [UIAlertController imy_showAlertViewWithTitle:nil message:@"要删除该内容吗？" cancelButtonTitle:IMYString(@"取消") otherButtonTitles:@[IMYString(@"删除")] handler:^(UIAlertController *alertController, NSInteger buttonIndex) {
        @strongify(self);
        if (buttonIndex == 1) {
            if (![IMYNetState networkEnable]) {
                [UIView imy_showTextHUD:MT_Request_NoNetToast];
                return;
            }
            [UIWindow imy_showLoadingHUD];
            [[[IMYServerRequest postPath:@"v5/user_article_delete" host:circle_seeyouyima_com params:@{@"item_id":@(self.viewModel.topic_id),@"item_type":@1,@"operate_type":@1} headers:nil] deliverOnMainThread] subscribeNext:^(id  _Nullable x) {
                @strongify(self);
                [UIView imy_showTextHUD:IMYString(@"已删除")];
                [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"dsq_nrxqy_gdan",@"public_type":@"删除",@"info_type":@12,@"info_id":@(self.viewModel.topic_id),@"fuid":@(self.viewModel.topic.publisher.userID),@"action":@2} headers:nil completed:nil];
                //个人主页刷新
                [[NSNotificationCenter defaultCenter] postNotificationName:@"kArticleDeleteRefreshNotification" object:@{@"itemId":@(self.viewModel.topic_id),@"qa":@1}];
                [self imy_pop:YES];
            } error:^(NSError * _Nullable error) {
                id idTemp = [error af_responseData];
                NSDictionary *dicValue = [idTemp imy_jsonObject];
                NSInteger code = [dicValue[@"code"] integerValue];
                if (code == 13000800) {
                    [UIWindow imy_hideHUD];
                    [UIAlertController imy_showAlertViewWithTitle:IMYString(@"作品暂不支持删除，\n可以到“帮助与反馈”里申诉反馈")  message:nil cancelButtonTitle:IMYString(@"取消") otherButtonTitles:@[IMYString(@"去反馈")] handler:^(UIAlertController *alertController, NSInteger buttonIndex) {
                        if (buttonIndex == 1) {
                            [[IMYURIManager shareURIManager] runActionWithString:@"qiyu/feedback"];
                        }
                    }];
                } else {
                    NSString *message = [dicValue valueForKey:@"message"];
                    [UIView imy_showTextHUD:message.length?message:IMYString(@"网络缓慢，请稍后再试")];
                }
            }];
        }
    }];
}

// MARK: 弹出输入框逻辑


- (void)forcedToReply {
    if ([IMYRewardAlertView showInWindow]) {
        return;
    }
    if (self.viewModel.becomeFirstResponder &&
        (self.viewModel.topic || self.viewModel.dataSource.count > 1) &&
        self.isViewDidAppeared) {
        self.viewModel.becomeFirstResponder = NO;
        [self.inputContentsView.textView becomeFirstResponder];
    }
}

- (void)replyToTopic {
    BOOL blocked = [self judgeBlocked];
    if (blocked) {
        return;
    }
    if ([TTQABTestConfig disableInteractByType:TTQ_Interact_type_comment]) {
        return;
    }
    if (![self inputViewShouldBeginEdit:self.inputContentsView]) {
        return;
    }
    if ([IMYRewardAlertView showInWindow]) {
        return;
    }
    
    if (self.viewModel.selectedReplyIndex) {
        [self.inputContentsView hidenCameraButton];
    } else {
        [self.inputContentsView showCameraButton];
    }
    [self.navigationController.view
     bringSubviewToFront:self.inputContentsView.myMaskView];
    [self.navigationController.view bringSubviewToFront:self.inputContentsView];
    [self.inputContentsView.textView becomeFirstResponder];
}

/**
 请求广告
 */
- (void)requestAdList {
    //只看楼主，不请求广告
    if (!self.adManager) {
        @weakify(self);
        imy_asyncMainBlock(^{
            @strongify(self);
            [self requestAdList];
        });
        return;
    }
    @weakify(self);
    imy_asyncMainBlock(0.15, ^{
        @strongify(self);
        [self.adManager.adInfo unlock];
        [self.adManager.adInfo appendUserInfo:@{@"fresh":@([self.viewModel commentCount])}];
        [self.adManager.adInfo lock];
        [self checkNeedShowAD];
        [self.adManager refreshData];
        if (!self.hasRequestSideAd) {
            // 贴边广告刷新【1、首次请求；下拉刷新】 @see
            // 【话题详情页，切换评论tab时去除贴片广告请求】
            // https://www.tapd.cn/34134977/prong/stories/view/1134134977001040635
            [self.sideAdManager refreshData];
            self.hasRequestSideAd = YES;
        }
        [self bringScreenTipToolToFront];
        
    });
}

- (void)requestMoreAdList {
    if (self.hasRequestSideAd && self.viewModel.lastCommentCount > 0 && self.viewModel.lastResType == 1) {
        [self checkNeedShowAD];
        [self.adManager loadMoreData:([self.viewModel commentCount] - self.viewModel.lastCommentCount) appendCount:self.viewModel.lastCommentCount];
    }
}

- (void)checkNeedShowAD {
    BOOL hasPopReply = [self isArray:0];
    BOOL hasShowAD = self.viewModel.commentFilter == TTQTopicFilterNone;
    BOOL hasGoto = self.viewModel.gotoID > 0 ? NO : YES;
    
    if (!self.viewModel.dataSourceChangeBlock) {
        @weakify(self);
        self.viewModel.dataSourceChangeBlock = ^{
            imy_asyncMainBlock(^{
                @strongify(self);
                NSInteger commentsCount = [self.adManager.adInfo[@"commentsCount"] integerValue];
                if (commentsCount != [self.viewModel commentCount]) {
                    [self.adManager.adInfo unlock];
                    [self.adManager.adInfo appendUserInfo:@{@"commentsCount": @([self.viewModel commentCount])}];
                    [self.adManager.adInfo appendUserInfo:@{@"totalCommentsCount":@(self.viewModel.topic.main_total_review)}];
                    [self.adManager.adInfo lock];
                    [self.tableView reloadData];
                }
            });
        };
    }
    [self.adManager.adInfo unlock];
    [self.adManager.adInfo appendUserInfo:@{
        @"totalDataSourceCount" : @(self.viewModel.topic.main_total_review)
    }];
    [self.adManager.adInfo appendUserInfo:@{
        @"deleteIdxs" : self.viewModel.deleteDataSource
    }];
    [self.adManager.adInfo appendUserInfo:@{
        @"forumId" : @(self.viewModel.forum_id)
    }];
    [self.adManager.adInfo appendUserInfo:@{
        @"publisherId" : @(self.viewModel.topic.publisher.userID)
    }];
    [self.adManager.adInfo appendUserInfo:@{ @"hasPopReply" : @(hasPopReply) }];
    [self.adManager.adInfo appendUserInfo:@{
        @"commentsCount" : @([self.viewModel commentCount])
    }];
    [self.adManager.adInfo appendUserInfo:@{
        @"totalCommentsCount" : @(self.viewModel.topic.main_total_review)
    }];
    [self.adManager.adInfo appendUserInfo:@{ @"is_video" : @(NO) }];
    if (self.headView) {
        NSMapTable *mapTable = [NSMapTable weakToWeakObjectsMapTable];
        [mapTable setObject:self.headView forKey:@"headView"];
        [self.adManager.adInfo appendUserInfo:@{@"headView" : mapTable}];
    }
    [self.adManager.adInfo appendUserInfo:@{@"no_setContentOffset":@(hasGoto)}];

    [self.adManager.adInfo lock];
    
    [self.sideAdManager.adInfo unlock];
    [self.sideAdManager.adInfo appendUserInfo:@{
        @"forumId" : @(self.viewModel.forum_id)
    }];
    [self.sideAdManager.adInfo appendUserInfo:@{
        @"publisherId" : @(self.viewModel.topic.publisher.userID)
    }];
    [self.adManager.adInfo lock];
}

/**
 广告监测
 */
- (void)monitorADUrl {
    if (self.viewModel.topic.ad_monitor_url.count && !self.isMonitorADUrl) {
        self.isMonitorADUrl = YES;
        NSArray *ADUrls = self.viewModel.topic.ad_monitor_url;
        @weakify(self);
        [ADUrls bk_each:^(NSString *obj) {
            // get请求一下喽
            if (obj) {
                NSMutableURLRequest *request =
                [NSMutableURLRequest requestWithURL:[NSURL URLWithString:obj]];
                request.HTTPMethod = @"GET";
                if ([IMYMeetyouHTTPHooks isMeiYouSite:obj]) {
                    NSString *ua = [UIDevice imy_userAgent];
                    [request setValue:ua forHTTPHeaderField:@"ua"];
                }
                [IMYMeetyouHTTPHooks addRequestHeadersToRequest:request];
                [IMYServerRequest
                 dataTaskWithRequest:request
                 completionHandler:^(NSData *data, NSURLResponse *response,
                                     NSError *error) {
                    @strongify(self);
                    if (error) {
                        self.isMonitorADUrl = NO;
                    }
                }];
            }
        }];
    }
}

// MARK: - UIGestureRecognizer

- (BOOL)gestureRecognizer:(UIGestureRecognizer *)gestureRecognizer
       shouldReceiveTouch:(UITouch *)touch {
    if (touch.gestureRecognizers.count >= 2) {
        return NO;
    }
    return YES;
}

// MARK: - 观察者需要实现的方法

- (void)observeValueForKeyPath:(NSString *)keyPath
                      ofObject:(id)object
                        change:(NSDictionary *)change
                       context:(void *)context {
    id newValue = change[NSKeyValueChangeNewKey];
    id oldValue = change[NSKeyValueChangeOldKey];
    if ([newValue isEqual:oldValue]) {
        return;
    }
    if ([keyPath isEqualToString:@"contentOffset"] ||
        [keyPath isEqualToString:@"frame"]) {
        [self navBarTitle];
        [self updateScreenTipToolView];
        [self updateForumView];
        [self bringScrollIndicatorToFront];
        imy_asyncMainBlock(^{
            [self updateCommentBIView];
        });
    }
}

// MARK: 跳转详情

- (void)jumpToAnswerDetail:(NSIndexPath *)indexPath needScrollToComment:(BOOL)scroll {
    TTQCommentModel *model = [self tableCellModelAtIndexPath:indexPath];
    [self hideKeyboard];
    self.viewModel.selectedReplyIndex = indexPath;
    TTQTopicReferenceViewModel *referenceVM =
    [self.viewModel createTopicReferenceViewModel];
    referenceVM.animationCommentIdWhenAppear = self.viewModel.sub_review_id;
    NSDictionary *referenceVMJsonData = [referenceVM yy_modelToJSONObject];
    void (^commentResultBlock)(BOOL has_praise, NSInteger praise_num,
                               NSInteger referenced_num,
                               NSArray *commentDataDictArray) =
    ^(BOOL has_praise, NSInteger praise_num, NSInteger referenced_num,
      NSArray *commentDataDictArray) {
        referenceVM.commentModel.has_praise = has_praise;
        referenceVM.commentModel.praise_num = praise_num;
        referenceVM.commentModel.referenced_num = referenced_num;
        if ([commentDataDictArray isKindOfClass:NSArray.class]) {
            referenceVM.commentModel.referenceds =
            (NSArray<TTQCommentModel> *)[commentDataDictArray
                                         toModels:TTQCommentModel.class];
        }
    };
    NSMutableDictionary *params = [@{
        @"viewModel" : referenceVMJsonData ?: @"",
        @"inputDefaultText" :
            [NSString stringWithFormat:@"@%@：", model.publisher.screen_name],
        @"becomeFirstResponder" : @(YES),
        @"fromTopicID" : @(self.viewModel.topic_id),
        @"isFromTopicDetail" : @(YES),
        @"commentResultBlock" : commentResultBlock,
        @"cardShareUrlString" : self.viewModel.cardShareUrlString
        ? self.viewModel.cardShareUrlString
        : @"",
        @"topicTitle" : self.viewModel.topic.title ? self.viewModel.topic.title
        : @""
        
    } mutableCopy];
    if (self.fromURI.params[@"entrance"]) {
        params[@"entrance"] = self.fromURI.params[@"entrance"];
    }
    if (self.fromURI.params[@"position"]) {
        params[@"position"] = self.fromURI.params[@"position"];
    }
    if (scroll) {
        params[@"locate_to_comment"] = @YES;
    }
    [[IMYURIManager shareURIManager]
     runActionWithURI:[IMYURI uriWithPath:@"answer/reply/detail"
                                   params:params
                                     info:nil]];
    self.viewModel.selectedReplyIndex = nil;
    
    [IMYEventHelper event:@"wdxqy_hf"
               attributes:@{
        @"mode" : @([IMYPublicAppHelper shareAppHelper].userMode)
    }];
    if (indexPath.row > 0) {
        /// 子评论点击
        [self postCommentBiFeedsView:2 commentId:referenceVM.referenced_id floor:indexPath.section clickpos:33];
    }
    self.viewModel.sub_review_id = 0;

}

// MARK: - UITableViewDataSource methods

- (UITableViewCell *)tableView:(UITableView *)tableView
         cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    UITableViewCell *cell =
    [super tableView:tableView cellForRowAtIndexPath:indexPath];
    id model = [self tableCellModelAtIndexPath:indexPath];
    [cell ttq_removeGestureRecognizer];
    @weakify(self,tableView);
    if ([cell isKindOfClass:IMYQATopicDetailCell.class]) {
        IMYQATopicDetailCell *topicDetailCell = (IMYQATopicDetailCell *)cell;
        topicDetailCell.isOpenCardShare = !!self.viewModel.cardShareUrlString;
        topicDetailCell.delegate = self;
        topicDetailCell.dtContentLabel.delegate = self;
        [topicDetailCell setCommentButtonTapBlock:^{
            @strongify(self);
            [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"dsq_nrxqypl_plqpl",@"action":@2,@"info_type":@12,@"info_id":@(self.viewModel.topic_id),@"fuid":@(self.viewModel.topicUserID)} headers:nil completed:nil];
            [self jumpToAnswerDetail:indexPath needScrollToComment:YES];
        }];
        [topicDetailCell setHartButtonTapBlock:^{
            @strongify(tableView);
            [tableView reloadData];
        }];
        
        UILongPressGestureRecognizer *longPressGestureRecognizer =
        [[UILongPressGestureRecognizer alloc]
         initWithTarget:self
         action:@selector(longPressAction:)];
        [cell addGestureRecognizer:longPressGestureRecognizer];
        if (indexPath.section == 0 || indexPath.section == 1) {
            topicDetailCell.lineView.hidden = YES;
        } else {
            topicDetailCell.lineView.hidden = NO;
        }
        [topicDetailCell imy_lineViewWithDirection:IMYDirectionUp
                                              show:YES
                                            margin:12];
        NSInteger commentId = ((TTQCommentModel *)model).commentID;
        topicDetailCell.biFeedsView.imyut_eventInfo.eventName = [NSString stringWithFormat:@"ttq_comment_feeds_%ld",commentId];
        topicDetailCell.biFeedsView.imyut_eventInfo.edgeOffset = UIEdgeInsetsMake(0, 0, 50+SCREEN_TABBAR_SAFEBOTTOM_MARGIN, 0);
        [topicDetailCell.biFeedsView.imyut_eventInfo setExposuredBlock:^(__kindof UIView *view, NSDictionary *params) {
            @strongify(self);
            [self postCommentBiFeedsView:1 commentId:commentId floor:indexPath.section clickpos:32];
        }];

    } else if ([cell isKindOfClass:TTQNewFirstPageCell.class]) {
        TTQNewFirstPageCell *firstPageCell = (TTQNewFirstPageCell *)cell;
        
        // 处理Cell事件
        // 点赞
        firstPageCell.priaseAction = ^(TTQHome5TopicModel *_Nonnull cellModel,
                                       BOOL hasPraise, BOOL shouldRequest) {
            @strongify(self);
            [self doPriaseWithModel:cellModel
                          hasPraise:hasPraise
                      shouldRequest:shouldRequest
                         completion:^(BOOL isSuccess) {
                if (isSuccess) {
                    NSMutableDictionary *params = [NSMutableDictionary new];
                    params[@"topic_id"] = @(cellModel.topic_id);
                    params[@"event"] = @"sq_xgtjdz";
                    [TTQCommonHelp GAEventForEventWith:params];
                }
            }];
        };
                
        // 跳转圈子处理
        firstPageCell.gotoCirclePostHandler =
        ^(TTQHome5TopicModel *_Nonnull cellModel) {
            NSMutableDictionary *params = [NSMutableDictionary new];
            params[@"topic_id"] = @(self.viewModel.topic.topic_id);
            params[@"event"] = @"sq_xgtjqzkp";
            [TTQCommonHelp GAEventForEventWith:params];
        };
    } else if ([cell isKindOfClass:TTQNewFirstPageVideoCell.class]) {
        
    } else if ([cell isKindOfClass:IMYQADetailListCommonHeaderCell.class]) {
        CGRect rect = [self.tableView rectForRowAtIndexPath:indexPath];
        NSLog(@"rect = %@", NSStringFromCGRect(rect));
        self.viewModel.recommendHeaderStartY = rect.origin.y;
    } else if ([cell isKindOfClass:IMYQARecommendCommentCell.class]) {
        [cell imy_lineViewWithDirection:IMYDirectionDown show:YES margin:0];
    } else if ([cell isKindOfClass:IMYQASearchCell.class]) {
        IMYQASearchCell *searchCell = (IMYQASearchCell *)cell;
        searchCell.isRecommand = YES;
    }
    if ([model isKindOfClass:TTQCommentModel.class]) {
        TTQCommentModel *comment = model;
        if (indexPath.row == 0) {
            cell.imyut_eventInfo.eventName = [NSString
                                              stringWithFormat:@"ttq_detail_comment_%@", @(comment.commentID)];
            cell.imyut_eventInfo.eventValue = @{
                @"topicId" : @(comment.topic_id),
                @"commentId" : @(comment.commentID)
            };
        } else {
            cell.imyut_eventInfo.eventName = [NSString
                                              stringWithFormat:@"ttq_detail_subComment_%@", @(comment.commentID)];
            NSIndexPath *mainCommentIndex =
            [NSIndexPath indexPathForRow:0 inSection:indexPath.section];
            TTQCommentModel *mainComment =
            [self tableCellModelAtIndexPath:mainCommentIndex];
            cell.imyut_eventInfo.eventValue = @{
                @"topicId" : @(comment.topic_id),
                @"commentId" : @(comment.commentID),
                @"maincommentId" : @(mainComment.commentID)
            };
        }
    } else if ([model isKindOfClass:TTQHome5TopicModel.class]) {
        // 相关推荐埋点
        TTQHome5TopicModel *topicModel = model;
        NSUInteger floor =
        [self.viewModel.recommend_topic indexOfObject:topicModel] + 1;
        NSMutableDictionary *params = [NSMutableDictionary dictionary];
        if (self.viewModel.entrance == 1       // 首页
            || self.viewModel.entrance == 2    // 她她圈
            || self.viewModel.entrance == 5) { // 柚宝宝
            params[@"entrance"] = @(self.viewModel.entrance);
        }
        if (topicModel.redirect_url)
            params[@"redirect_url"] = topicModel.redirect_url;
        params[@"floor"] = @(floor);
        cell.imyut_eventInfo.eventName =
        [NSString stringWithFormat:@"ttq_recommend_%@", @(topicModel.topic_id)];
        cell.imyut_eventInfo.eventValue = params;
        cell.imyut_eventInfo.exposuredBlock =
        ^(__kindof UIView *view, NSDictionary *params) {
            NSDictionary *settledParams = params[@"detailInfo"];
            [TTQCommonHelp GAEventForInformationWithURL:nil
                                                  floor:0
                                                 action:1
                                                 params:settledParams];
            [IMYEventHelper event:@"htxq-tjt"];
        };
        cell.imyut_eventInfo.clickedBlock =
        ^(__kindof UIView *view, NSDictionary *params) {
            NSDictionary *settledParams = params[@"detailInfo"];
            [TTQCommonHelp GAEventForInformationWithURL:nil
                                                  floor:0
                                                 action:2
                                                 params:settledParams];
            [IMYEventHelper event:@"htxq-tjtdj"];
            if ([topicModel isKindOfClass:[TTQTopicModel class]]) {
                [IMYEventHelper event:@"ckzt"
                              addType:IMYEventAddTypeAll
                           attributes:@{@"来源" : @"话题详情页"}];
            }
        };
    } else if ([model isKindOfClass:IMYQAQuestionAnswerModel.class]) {
        IMYQAQuestionAnswerModel *cellModel = (IMYQAQuestionAnswerModel *)model;
        if (imy_isNotEmptyString(cellModel.answer.audio.audio_url) &&
            imy_isEmptyString(cellModel.answer.video.video_url)) {
            //相关回答
            IMYQAHomeExpertCell *recommendCell = (IMYQAHomeExpertCell *)cell;
            [recommendCell imy_lineViewWithDirection:IMYDirectionDown
                                                show:YES
                                              margin:12];
            [recommendCell setDataModel:cellModel
                               position:IMYQAAudioPositionDetailRelationAnswer
                              indexPath:indexPath
                                  catid:self.catid];
        } else {
            //图片, 视频类型都用这个cell
            //除了音频之外的所有
            IMYQAHomeCell *mediaCell = (IMYQAHomeCell *)cell;
            [mediaCell setDataModel:model];
            if (indexPath.section != self.viewModel.dataSource.count - 1) {
                [mediaCell hideLine:NO];
            } else {
                [mediaCell hideLine:YES];
            }
//            [mediaCell imy_showLineForRow:indexPath.row leftMargin:15 rowCount:1];
        }
    }
    return cell;
}

- (void)presetTableView:(UITableView *)tableView
                   cell:(UITableViewCell *)cell
                  model:(id)model
              indexPath:(NSIndexPath *)indexPath {
    if ([cell isKindOfClass:TTQNewFirstPageCell.class]) {
        TTQNewFirstPageCell *firstPageCell = (TTQNewFirstPageCell *)cell;
        firstPageCell.fromType = 2;
        firstPageCell.praiseType = self.viewModel.praiseType;
    } else if ([cell isKindOfClass:TTQNewFirstPageVideoCell.class]) {
        TTQNewFirstPageVideoCell *firstPageCell = (TTQNewFirstPageVideoCell *)cell;
        firstPageCell.fromType = 2;
        firstPageCell.pageVideoView.useEmbedBiPost = YES;
        firstPageCell.pageVideoView.biExtraParams = @{ @"entrance" : @(9) };
        firstPageCell.praiseType = self.viewModel.praiseType;
    } else if ([cell isKindOfClass:IMYQATopicDetailCell.class]) {
        IMYQATopicDetailCell *topicDetailCell = (IMYQATopicDetailCell *)cell;
        topicDetailCell.praiseType = self.viewModel.praiseType;
    }
}

- (BOOL)tableView:(UITableView *)tableView
shouldHighlightRowAtIndexPath:(NSIndexPath *)indexPath {
    TTQCommentModel *model = [self tableCellModelAtIndexPath:indexPath];
    if ([model isKindOfClass:TTQCommentModel.class] && indexPath.row == 0) {
        [self
         doSelectAllCellsInSameSection:indexPath
         isHighlighted:NO]; //不显示高亮的灰色底 cell选中状态去除
    }
    return YES;
}

- (void)tableView:(UITableView *)tableView
didUnhighlightRowAtIndexPath:(NSIndexPath *)indexPath {
    TTQCommentModel *model = [self tableCellModelAtIndexPath:indexPath];
    if ([model isKindOfClass:TTQCommentModel.class] && indexPath.row == 0) {
        UITableViewCell *cell = [tableView cellForRowAtIndexPath:indexPath];
        if (!cell.isSelected) {
            [self doSelectAllCellsInSameSection:indexPath isHighlighted:NO];
        }
    }
}

- (nullable NSIndexPath *)tableView:(UITableView *)tableView
           willSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    if (_inputContentsView.isFirstResponder) {
        [self hideKeyboard];
        return nil;
    }
    return indexPath;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    if (indexPath == nil) {
        return;
    }
    TTQCommentModel *model = [self tableCellModelAtIndexPath:indexPath];
    if ([model isKindOfClass:TTQCommentModel.class]) {
        [super tableView:tableView didSelectRowAtIndexPath:indexPath];
        [IMYEventHelper event:@"qzxq-plxq"];
        
        [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"dsq_nrxqypl_plqpl",@"action":@2,@"info_type":@12,@"info_id":@(self.viewModel.topic_id),@"fuid":@(self.viewModel.topicUserID)} headers:nil completed:nil];
        
        [self jumpToAnswerDetail:indexPath needScrollToComment:NO];
        [self postCommentBiFeedsView:2 commentId:model.commentID floor:indexPath.section clickpos:32];
    } else if ([model isKindOfClass:TTQEmptyCommentModel.class]) {
        if ([IMYRewardAlertView showInWindow]) {
            return;
        }

        // 空白视图点击事件
        [self.inputContentsView.textView becomeFirstResponder];
        [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"dsq_nrxqy_wpldj",@"action":@2,@"info_type":@12,@"info_id":@(self.viewModel.topic_id),@"fuid":@(self.viewModel.topicUserID)} headers:nil completed:nil];
    } else if ([model isKindOfClass:TTQTopicModel.class]) {
        if (imy_isNotEmptyString(((TTQTopicModel *)model).redirect_url)) {
            IMYURI *uri = [IMYURI uriWithURIString:((TTQTopicModel *)model).redirect_url];
            [[IMYURIManager shareURIManager] runActionWithURI:uri];
        }
        
        TTQTopicModel *model = [self tableCellModelAtIndexPath:indexPath];
        
        NSUInteger index = [self.viewModel.recommendQATopics indexOfObject:model];
        if (index == NSNotFound) {
        } else {
            [self recommendCellTrackIndex:index action:(@2)model:model];
        }
    } else if ([model isKindOfClass:TTQHome5TopicModel.class]) {
        [super tableView:tableView didSelectRowAtIndexPath:indexPath];
        TTQHome5TopicModel *topicModel = (TTQHome5TopicModel *)model;
        if (imy_isNotEmptyString(topicModel.redirect_url)) {
            IMYURI *uri = [IMYURI uriWithURIString:topicModel.redirect_url];
            [[IMYURIManager shareURIManager] runActionWithURI:uri];
        }
    } else if ([model isKindOfClass:IMYQAQuestionAnswerModel.class]) {
        NSInteger length =
        indexPath.section - (self.viewModel.dataSource.count -
                             self.viewModel.recommendQuestions.count);
        IMYQAQuestionAnswerModel *theModel = (IMYQAQuestionAnswerModel *)model;
        [self relatedCellTrackIndex:length action:2 model:theModel];
        // 相关问答详情
        [tableView deselectRowAtIndexPath:indexPath animated:YES];
        IMYQAQuestionAnswerModel *qa = (IMYQAQuestionAnswerModel *)model;
        if (qa.url) {
            IMYURI *uri = [IMYURI uriWithURIString:qa.url];
            [[IMYURIManager shareURIManager] runActionWithURI:uri];
        }
    }
}

- (CGFloat)tableView:(UITableView *)tableView
heightForHeaderInSection:(NSInteger)section {
    NSInteger length = 0;
    if ([self containRelatedQuestion]) {
        length = self.viewModel.dataSource.count -
        self.viewModel.recommendQuestions.count;
    } else if ([self containRelatedAnswers]) {
        length = self.viewModel.dataSource.count -
        self.viewModel.recommendQATopics.count;
    }
    if (section == 0) {
        self.screenTipToolView.hidden = NO;
        NSString *answer = self.viewModel.topic.main_total_review > 0 ? [NSString stringWithFormat:@"回答%ld条",self.viewModel.topic.main_total_review] : @"回答";
        [self.answerCountlabel imy_setText:answer];
        @weakify(self);
        [RACObserve(self.viewModel.topic, main_total_review) subscribeNext:^(id x) {
            @strongify(self);
            NSString *answer = self.viewModel.topic.main_total_review > 0 ? [NSString stringWithFormat:@"回答%ld条",self.viewModel.topic.main_total_review] : @"回答";
            [self.answerCountlabel imy_setText:answer];
        }];
        return 53;
    } else if ([self containRelatedQuestionOrAnswer] && section == length) {
        //推荐问题头部
        return 44;
    } else if (section == 1) {
        return CGFLOAT_MIN;
    } else {
        if ([self containRelatedQuestionOrAnswer] && section == (length - 1)) {
            return 10;
        }
        return 0;
    }
}

- (UIView *)tableView:(UITableView *)tableView
viewForHeaderInSection:(NSInteger)section {
    NSInteger length = 0;
    if ([self containRelatedQuestion]) {
        length = self.viewModel.dataSource.count -
        self.viewModel.recommendQuestions.count;
    } else if ([self containRelatedAnswers]) {
        length = self.viewModel.dataSource.count -
        self.viewModel.recommendQATopics.count;
    }
    if (section == 0) {
        return self.headView;
    } else if ([self containRelatedQuestionOrAnswer] && section == length) {
        UIView *header =
        [[UIView alloc] initWithFrame:CGRectMake(0, 9, SCREEN_WIDTH, 44)];
        [header imy_setBackgroundColorForKey:kCK_White_AN];
        UILabel *label = [[UILabel alloc]
                          initWithFrame:CGRectMake(15, 0, SCREEN_WIDTH - 30, 44)];
        label.font = [UIFont boldSystemFontOfSize:17];
        [label imy_setTextColor:kCK_Black_A];
        if ([self containRelatedQuestion]) {
            label.text = @"相关问答";
        } else if ([self containRelatedAnswers]) {
            label.text = @"其他解答";
        }
//        [header imy_lineViewWithDirection:IMYDirectionDown show:YES margin:0];
        [header addSubview:label];
        return header;
    } else if (section == 1) {
        return nil;
    } else {
        if ([self containRelatedQuestionOrAnswer] && section == (length - 1)) {
            UIView *view = [UIView new];
            [view imy_setBackgroundColorForKey:kCK_Black_F];
            return view;
        }
        return nil;
    }
}

- (void)tableView:(UITableView *)tableView
  willDisplayCell:(UITableViewCell *)cell
forRowAtIndexPath:(NSIndexPath *)indexPath {
    if ([cell isKindOfClass:IMYQARecommendCommentCell.class]) {
        TTQTopicModel *model = [self tableCellModelAtIndexPath:indexPath];
        
        NSUInteger index = [self.viewModel.recommendQATopics indexOfObject:model];
        if (index == NSNotFound || nil == model || YES == model.didBITrack) {
            
        } else {
            model.didBITrack = YES;
            
            [self recommendCellTrackIndex:index action:(@1)model:model];
        }
    } else if ([cell isKindOfClass:IMYQAHomeExpertCell.class] ||
               [cell isKindOfClass:IMYQASearchCell.class] ||
               [cell isKindOfClass:IMYQAHomeCell.class]) {
        NSInteger length =
        indexPath.section - (self.viewModel.dataSource.count -
                             self.viewModel.recommendQuestions.count);
        TTQTopicModel *model = [self tableCellModelAtIndexPath:indexPath];
        IMYQAQuestionAnswerModel *theModel = (IMYQAQuestionAnswerModel *)model;
        [self relatedCellTrackIndex:length action:1 model:theModel];
    }
}

// MARK: - IMYVideoViewDelegate

- (void)playerView:(IMYVideoView *)playerView
 willChangeStateTo:(LLVideoPlayerState)state {
    if (state == LLVideoPlayerStateContentPlaying) {
        [self pauseBackgroundSoundWithError:nil];
    }
}

- (void)playerView:(IMYVideoView *)playerView
didChangeStateFrom:(LLVideoPlayerState)state {
    if (playerView.videoType == TTQVideoViewTypeRecommend) {
        // 相关推荐的视频播放不处理
        return;
    }
    if ((state == LLVideoPlayerStateContentPaused ||
         state == LLVideoPlayerStateDismissed ||
         state == LLVideoPlayerStateUnknown) &&
        [IMYNetState isWWAN] && self.lastTimeNetState == 0) {
        [UIWindow imy_showTextHUD:@"正在使用移动网络"];
        self.lastTimeNetState = 1;
    }
    if ((state == LLVideoPlayerStateContentPaused ||
         state == LLVideoPlayerStateDismissed ||
         state == LLVideoPlayerStateUnknown) &&
        [IMYNetState isWiFi] && self.lastTimeNetState == 1) {
        [UIWindow imy_showTextHUD:@"已切换至WiFi网络"];
        self.lastTimeNetState = 0;
    }
    
    // bi埋点
    // 记录开始的时间
    if ([playerView isPlaying] &&
        [playerView.track isKindOfClass:LLVideoTrack.class]) {
        LLVideoTrack *track = playerView.track;
        NSUInteger second = [track.lastWatchedDuration floatValue];
        self.bi_startDuration = second * 1000;
    }
    // 暂停、退出播放停止的埋点
    if ([playerView isPaused] &&
        [playerView.track isKindOfClass:LLVideoTrack.class]) {
        LLVideoTrack *track = playerView.track;
        NSUInteger second = [track.lastWatchedDuration floatValue];
        self.bi_endDuration = second * 1000;
        self.bi_endType = 2;
        
        [self postBiVideoEventWithVideoView:playerView];
    }
}

- (void)playerViewDidFinishPlaying:(IMYVideoView *)playerView {
    // 相关推荐的视频自动播放
    if (playerView.videoType == TTQVideoViewTypeRecommend) {
        if ([IMYNetState isWiFi] && playerView.superview) {
            [playerView replayByOutterVideoView];
        }
    } else {
        // 详情页视频处理
        // 不需要自动重播，显示重播按钮
        // bi埋点
        // 播放完成的埋点
        if ([playerView.track isKindOfClass:LLVideoTrack.class]) {
            LLVideoTrack *track = playerView.track;
            NSUInteger second = [track.totalDuration floatValue];
            self.bi_endDuration = second * 1000;
            self.bi_endType = 1;
            if (self.firstEnterDetail && [IMYNetState isWiFi]) {
                self.firstEnterDetail = NO;
                self.bi_startType = 6;
            }
        }
        [self postBiVideoEventWithVideoView:playerView];
    }
}

- (void)playerView:(IMYVideoView *)playerView
willChangeToOrientation:(UIInterfaceOrientation)orientation {
    if (orientation == UIInterfaceOrientationLandscapeLeft ||
        orientation == UIInterfaceOrientationLandscapeRight) {
        [IMYEventHelper event:@"spqpbf"];
    }
}

- (void)playerView:(IMYVideoView *)playerView
 didControlByEvent:(NSInteger)event
          userInfo:(id)userInfo {
    if (playerView.videoType == TTQVideoViewTypeRecommend) {
        // 相关推荐的视频播放不处理
        return;
    }
    if (event == kVControlEventShare) {
        if (![self loginActicon]) {
            return;
        };
        [TTQShareView
         shareViewForOnlyShareWith:self.viewModel
         shareEventBlock:^(TTQDetailShareBtnType shareType,
                           NSInteger shareState) {
            switch (shareType) {
                case TTQDetailShareBtnTypeWXLine:
                    if (shareState == 0) {
                        [IMYEventHelper event:@"spjs-fx"
                                   attributes:@{@"来源" : @"朋友圈"}];
                    }
                    break;
                case TTQDetailShareBtnTypeWXFriend:
                    if (shareState == 0) {
                        [IMYEventHelper event:@"spjs-fx"
                                   attributes:@{@"来源" : @"微信好友"}];
                    }
                    break;
                case TTQDetailShareBtnTypeQQFriend:
                    if (shareState == 0) {
                        [IMYEventHelper event:@"spjs-fx"
                                   attributes:@{@"来源" : @"qq好友"}];
                    }
                    break;
                case TTQDetailShareBtnTypeQQZone:
                    if (shareState == 0) {
                        [IMYEventHelper event:@"spjs-fx"
                                   attributes:@{@"来源" : @"qq空间"}];
                    }
                    break;
                case TTQDetailShareBtnTypeWeibo:
                    if (shareState == 0) {
                        [IMYEventHelper event:@"spjs-fx"
                                   attributes:@{@"来源" : @"新浪微博"}];
                    }
                    break;
                case TTQDetailShareBtnTypeMeeYou:
                    if (shareState == 0) {
                        [IMYEventHelper event:@"pjs-fx"
                                   attributes:@{@"来源" : @"我的动态"}];
                    }
                    break;
                default:
                    break;
            }
        }];
    } else if (event == kVControlEventReplay) {
        self.bi_startType = 2;
        [self postPlayCountStatistaics];
        [IMYEventHelper event:@"spjs-cb"];
    } else if (event == kVControlEventWWANContinue) {
        [IMYEventHelper event:@"spts" attributes:@{@"来源" : @"播放"}];
    } else if (event == kVControlEventCollect) {
        [IMYEventHelper event:@"spts" attributes:@{@"来源" : @"收藏"}];
        if ([IMYPublicAppHelper shareAppHelper].hasLogin) {
            [self.viewModel.favoriteCommand execute:nil];
        } else {
            [UIWindow imy_showTextHUD:kStatusText_unLogin];
            [[IMYURIManager shareURIManager] runActionWithString:@"login"];
        }
    } else if (event == kVControlEventPlayInCelluar) {
        [IMYEventHelper event:@"sptscx"];
        
    } else if (event == kVControlEventPlay) {
        
        [self showPlayInCelluarTipsWithVideoView:playerView];
        
        if (playerView.isPausedByUser) {
            // BI 埋点暂停上报
            self.bi_startType = 1;
            [self postPlayCountStatistaics];
        }
    }
}

- (void)playerViewDidTapToPlay:(IMYVideoView *)playerView {
    [self showPlayInCelluarTipsWithVideoView:playerView];
}

- (CGFloat)offsetTopForHidenNav {
    return 0;
}

// MARK: - IMYFullPopGestureRecognizerDelegate  左滑手势
/**
 视屏区域左滑手势屏蔽掉
 */
- (BOOL)fullPopGestureRecognizerShouldBegin:
(UIPanGestureRecognizer *)gestureRecognizer {
    // 输入评论框存在时，禁止左滑
    if (_inputContentsView.imy_top == SCREEN_STATUSBAR_HEIGHT) {
        return NO;
    }
    CGPoint location = [gestureRecognizer locationInView:self.view];
    if ([self isVideoViewInPosition:location]) {
        return NO;
    }
    return TRUE;
}

/**
 判断触摸点位置是否是视频的View，全屏返回手势和视频播放的进度条拖动手势有冲突，需要特殊处理
 */
- (BOOL)isVideoViewInPosition:(CGPoint)point {
    IMYQATopicDetailHeadView *headView =
    (IMYQATopicDetailHeadView *)self.tableView.tableHeaderView;
    for (IMYVideoView *videoView in self.weakVideoViews.allObjects) {
        CGRect videoViewRectInView =
        [headView.richParserView convertRect:videoView.frame toView:self.view];
        if (CGRectContainsPoint(videoViewRectInView, point)) {
            return YES;
        }
    }
    return NO;
}

// MARK: - IMYREasyInputViewDelegate   输入框相关代理
// changeHeight:改变的高度,正数为增加，负数为减小
- (void)inputViewDidChangeHeight:(IMYREasyInputView *)inputView
                    changeHeight:(float)changeHeight {
    self.tableView.imy_height =
    self.inputTipToolView.imy_top - self.tableView.imy_top;
}

- (BOOL)inputViewOfTextView:(HPGrowingTextView *)growingTextView
    shouldChangeTextInRange:(NSRange)range
            replacementText:(NSString *)text {
    if (range.length + range.location > growingTextView.text.length) {
        return NO;
    }
    
    // 超过最大字符限制时，表情图片无法显示
    NSInteger count = kQuestionDetailVCInputViewMaxTextLength -
    growingTextView.text.length; // 余下可输入字符
    if (count < text.length && growingTextView.internalTextView.attributedText) {
        NSMutableAttributedString *attributeString =
        [[NSMutableAttributedString alloc]
         initWithAttributedString:growingTextView.internalTextView
            .attributedText];
        NSString *subtext = [text substringToIndex:count];
        [attributeString replaceCharactersInRange:range withString:subtext];
        [UIWindow
         imy_showTextHUD:[NSString stringWithFormat:
                          @"内容最多%ld字",
                          kQuestionDetailVCInputViewMaxTextLength]];
        growingTextView.internalTextView.attributedText = attributeString;
        [self setupInputViewStatusWithTextLength:
         kQuestionDetailVCInputViewMaxTextLength
                                        withText:text];
        [_inputContentsView textCountLabel:growingTextView.text.length];
        return NO;
    }
    
    NSString *oldText = [NSString imy_isBlankString:growingTextView.text]
    ? @""
    : growingTextView.text;
    NSString *newText = @"";
    if (oldText.length && (range.length + range.location <= oldText.length)) {
        newText =
        [oldText stringByReplacingCharactersInRange:range withString:text];
    }
    
    if (newText.length > kQuestionDetailVCInputViewMaxTextLength) {
        [UIWindow
         imy_showTextHUD:[NSString stringWithFormat:
                          @"内容最多%ld字",
                          kQuestionDetailVCInputViewMaxTextLength]];
        
        growingTextView.text =
        [newText substringToIndex:kQuestionDetailVCInputViewMaxTextLength];
        
        [self setupInputViewStatusWithTextLength:
         kQuestionDetailVCInputViewMaxTextLength
                                        withText:text];
        
        return NO;
    } else {
        [self setupInputViewStatusWithTextLength:newText.length withText:text];
        [_inputContentsView textCountLabel:newText.length];
        if (self.canReportInputViewChange) {
            self.canReportInputViewChange = NO;
            [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"dsq_nrxqy_wzsr",@"action":@1,@"info_type":@12,@"info_id":@(self.viewModel.topic_id),@"fuid":@(self.viewModel.topicUserID)} headers:nil completed:nil];
        }

        return YES;
    }
}

- (void)inputViewOfTextViewDidChange:(HPGrowingTextView *)growingTextView {
    NSInteger textLength = growingTextView.text.ttq_textLength;
    if (growingTextView.internalTextView.markedTextRange == nil &&
        textLength > kQuestionDetailVCInputViewMaxTextLength) {
        [UIWindow imy_showTextHUD:IMYString(@"最多回复500字哦~")];
        growingTextView.text = [growingTextView.text
                                ttq_subChapterToIndex:kQuestionDetailVCInputViewMaxTextLength];
    }
    
    // 更新提示文字
    [self setupInputViewStatusWithTextLength:growingTextView.text.length
                                    withText:growingTextView.text];
    [_inputContentsView textCountLabel:growingTextView.text.length];
}

- (BOOL)inputViewShouldBeginEdit:(IMYREasyInputView *)inputView {
    self.canReportInputViewChange = YES;
    return YES;
}

- (void)inputViewWillResignFirstResponder:(IMYREasyInputView *)inputView
                           keyboardHeight:(CGFloat)height
                        animationDuration:(CGFloat)duration
                                   option:(UIViewAnimationOptions)option {
    self.inputContentsView.emoticonButton.selected = false;
    self.inputContentsView.cameraButton.selected = false;
    
    CGRect rect =
    [inputView convertRect:inputView.frame
                    toView:[UIApplication sharedApplication].keyWindow];
    if (rect.origin.x != 0) {
        return;
    }
}

- (void)inputViewWillBecomeFirstResponder:(IMYREasyInputView *)inputView
                           keyboardHeight:(CGFloat)height
                        animationDuration:(CGFloat)duration
                                   option:(UIViewAnimationOptions)option {
    [IMYEventHelper event:@"wdxqy_hd"
               attributes:@{
        @"mode" : @([IMYPublicAppHelper shareAppHelper].userMode)
    }];
    if (self.inputContentsView.textView.internalTextView.inputView == nil) {
        self.inputContentsView.emoticonButton.selected = false;
    } else {
        self.inputContentsView.emoticonButton.selected = true;
    }
    self.inputContentsView.cameraButton.selected = false;
    if (imy_isEmptyString(self.inputContentsView.textView.text)) {
        self.inputContentsView.textView.text = @"";
    }
    
    inputView.textView.textColor = IMY_COLOR_KEY(kCK_Black_A);
    [self.view bringSubviewToFront:self.captionView];
}

- (void)inputViewWillSend:(IMYREasyInputView *)inputView {
    [self inputViewSendFromKeyboard:YES];
}

// MARK: - IMYRM80AttributedLabelDelegate

- (void)m80AttributedLabel:(IMYRM80AttributedLabel *)label
              longedOnLink:(IMYRM80AttributedLabelURL *)linkURL {
    NSString *copyString = linkURL.linkData;
    IMYRM80AttributedLabelURL *tmpLinkURL = linkURL;
    //这里为了统计，要不然都可以直接在解析时的linkData直接为url就好了。。。都是为了统计啊呀
    if ([copyString isEqualToString:IMYQATopicContentLinkTypeHotTopic]) {
        // 热议话题点击
        [IMYEventHelper event:@"htxq-ryht" attributes:nil];
        tmpLinkURL = [IMYRM80AttributedLabelURL
                      urlWithLinkData:self.viewModel.topic.subject.redirect_url
                      range:linkURL.range
                      color:linkURL.color];
    } else if ([copyString isEqualToString:IMYQATopicContentLinkTypeTheme]) {
        // 主题点击
        IMYURI *uri = [IMYURI uriWithPath:@"circles/video/theme/gather"
                                   params:@{
            @"theme_id" : @(self.viewModel.topic.theme_id)
        }
                                     info:nil];
        tmpLinkURL = [IMYRM80AttributedLabelURL urlWithLinkData:uri.uri
                                                          range:linkURL.range
                                                          color:linkURL.color];
    }
    //    [super m80AttributedLabel:label longedOnLink:tmpLinkURL];
}

- (void)m80AttributedLabel:(IMYRM80AttributedLabel *)label
             clickedOnLink:(IMYRM80AttributedLabelURL *)linkURL {
    NSString *copyString = linkURL.linkData;
    IMYRM80AttributedLabelURL *tmpLinkURL = linkURL;
    // 这里为了统计，要不然都可以直接在解析时的linkData直接为url就好了。。。都是为了统计啊呀
    if ([copyString isEqualToString:IMYQATopicContentLinkTypeHotTopic]) {
        // 热议话题点击
        [IMYEventHelper event:@"htxq-ryht" attributes:nil];
        tmpLinkURL = [IMYRM80AttributedLabelURL
                      urlWithLinkData:self.viewModel.topic.subject.redirect_url
                      range:linkURL.range
                      color:linkURL.color];
    } else if ([copyString isEqualToString:IMYQATopicContentLinkTypeTheme]) {
        // 主题点击
        IMYURI *uri = [IMYURI uriWithPath:@"circles/video/theme/gather"
                                   params:@{
            @"theme_id" : @(self.viewModel.topic.theme_id)
        }
                                     info:nil];
        tmpLinkURL = [IMYRM80AttributedLabelURL urlWithLinkData:uri.uri
                                                          range:linkURL.range
                                                          color:linkURL.color];
    }
    [self ttq_m80AttributedLabel:label clickedOnLink:tmpLinkURL];
}

/**
 1  '外链',
 2  '内链',
 3  '主题商城',
 4  '皮肤详情',
 5  '话题',          xixiaoyou.com?__type=5&topic_id=234325
 6  '话题专题',        xixiaoyou.com?__type=6&catid=0&specialid=0
 7  '消息',        xixiaoyou.com?__type=7
 8  '意见反馈',      xixiaoyou.com?__type=8
 9  '柚子街',        xixiaoyou.com?__type=9
 10  '我的柚币',     xixiaoyou.com?__type=10
 11  '签到',         xixiaoyou.com?__type=11
 12  '求助区入口',   xixiaoyou.com?__type=12&forum_id=0
 //已经删除了，没有求助区了哟 13  '达人堂入口'
 xixiaoyou.com?__type=13&forum_id=0
 */
- (void)ttq_m80AttributedLabel:(IMYRM80AttributedLabel *)label
                 clickedOnLink:(IMYRM80AttributedLabelURL *)linkURL {
    NSString *copyString = linkURL.linkData;
    if ([copyString isEqualToString:@"from"] ||
        [copyString isEqualToString:@"to-reply"]) {
        TTQTopicQuoteCell *cell =
        [label imy_findParentViewWithClass:[TTQTopicQuoteCell class]];
        TTQCommentModel *model =
        [self tableCellModelAtIndexPath:[self.tableView indexPathForCell:cell]];
        TTQPublisherModel *publisher = nil;
        if ([copyString isEqualToString:@"to-reply"]) {
            publisher = model.replygoal;
        } else {
            publisher = model.publisher;
        }
        if (publisher.userID > 0 && publisher.error == 0) {
            IMYURI *uri = [IMYURI uriWithPath:@"user/dynamic"
                                       params:@{
                @"userID" : @(publisher.userID),
                @"source" : @"话题详情"
            }
                                         info:nil];
            [[IMYURIManager shareURIManager] runActionWithURI:uri];
        } else if (publisher.error == 1) {
            [UIView imy_showTextHUD:kStatusText_UserAnonymous];
        } else if (publisher.error == 2) {
            [UIView imy_showTextHUD:@"该用户主页暂未开放"];
        } else if (publisher.error == 3){
            [UIView imy_showTextHUD:IMYString(kStatusText_homePageNotOpen)];
        }
    } else if ([copyString isEqualToString:@"to"]) {
        TTQTopicQuoteCell *cell =
        [label imy_findParentViewWithClass:NSClassFromString(
                                                             @"TTQTopicReferenceCell")];
        TTQCommentModel *model =
        [self tableCellModelAtIndexPath:[self.tableView indexPathForCell:cell]];
        if (model.replygoal.userID > 0 && model.publisher.error == 0) {
            IMYURI *uri = [IMYURI uriWithPath:@"user/dynamic"
                                       params:@{
                @"userID" : @(model.replygoal.userID),
                @"source" : @"话题详情"
            }
                                         info:nil];
            [[IMYURIManager shareURIManager] runActionWithURI:uri];
        } else if (model.publisher.error == 1) {
            [UIView imy_showTextHUD:kStatusText_UserAnonymous];
        } else if (model.publisher.error == 2) {
            [UIView imy_showTextHUD:@"该用户主页暂未开放"];
        } else if (model.publisher.error == 3){
            [UIView imy_showTextHUD:IMYString(kStatusText_homePageNotOpen)];
        }
    } else {
        [self clickWithLink:copyString];
    }
}

// MARK: - TTQTopicRichWithParserViewDelegate
- (void)clickWithLink:(NSString *)linkUrl {
    NSURL *url = [NSURL URLWithString:linkUrl];
    if ([url.absoluteString containsString:@"itunes.apple.com"]) {
        [[UIApplication sharedApplication] openURL:url options:nil completionHandler:nil];
    } else {
        IMYURI *uri = nil;
        if (linkUrl) {
            uri = [IMYURI uriWithURIString:linkUrl];
            if ([[IMYURIManager shareURIManager] containScheme:uri.scheme] &&
                [[IMYURIManager shareURIManager] runActionWithURI:uri]) {
                return;
            }
        }
        
        NSDictionary *dic = [linkUrl imy_queryDictionary];
        
        NSString *uriString = dic[@"uri"];
        if (uriString) {
            uri =
            [IMYURI uriWithURIString:[uriString imy_base64DecodedSafeURLString]];
            if ([[IMYURIManager shareURIManager] containScheme:uri.scheme] &&
                [[IMYURIManager shareURIManager] runActionWithURI:uri]) {
                return;
            }
        }
        NSString *topic_id = dic[@"topic_id"];
        //旧的跳转方式.只提供topic_id.没有提供__type
        if (topic_id) {
            [IMYEventHelper event:@"ttq-2"];
            [[IMYURIManager shareURIManager]
             runActionWithURI:[IMYURI uriWithPath:@"circles/group/topic"
                                           params:@{@"topicID" : topic_id}
                                             info:nil]];
            return;
        }
        NSString *type = dic[@"__type"];
        if (type) {
            [IMYEventHelper event:[NSString stringWithFormat:@"ttq-%@", type]];
            if ([type isEqualToString:@"1"]) {
                [[UIApplication sharedApplication] openURL:url options:nil completionHandler:nil];
            } else if ([type isEqualToString:@"2"]) {
                NSDictionary *params = @{
                    @"url" : url.absoluteString,
                    @"usingWK" : @(YES),
                };
                [[IMYURIManager shareURIManager] runActionWithPath:@"web"
                                                            params:params
                                                              info:nil];
            } else if ([type isEqualToString:@"3"]) {
                [IMYEventHelper event:@"gxzt"
                              addType:0
                           attributes:@{@"来源" : @"话题-主题商城"}];
                //跳到主题商城
                [[IMYURIManager shareURIManager] runActionWithPath:@"theme"
                                                            params:nil
                                                              info:nil];
                return;
            } else if ([type isEqualToString:@"4"]) {
                [IMYEventHelper event:@"gxzt"
                              addType:0
                           attributes:@{@"来源" : @"话题-皮肤详情"}];
                //跳到主题详情
                [[IMYURIManager shareURIManager]
                 runActionWithPath:@"theme/detail"
                 params:@{@"themeID" : dic[@"skinid"]}
                 info:nil];
                return;
            } else if ([type isEqualToString:@"5"]) {
                // 5  '话题',           xixiaoyou.com?__type=5&topic_id=234325
                [IMYEventHelper event:@"ttq-2"];
                [[IMYURIManager shareURIManager]
                 runActionWithURI:[IMYURI
                                   uriWithPath:@"circles/group/topic"
                                   params:@{@"topicID" : dic[@"topic_id"]}
                                   info:nil]];
            } else if ([type isEqualToString:@"6"]) {
                // 6  '话题专题',         xixiaoyou.com?__type=6&catid=0&specialid=0
                IMYURI *uri = [IMYURI uriWithPath:@"news/special" params:dic info:nil];
                [[IMYURIManager shareURIManager] runActionWithURI:uri];
            } else if ([type isEqualToString:@"7"]) {
                // 7  '消息',            xixiaoyou.com?__type=7
                [[IMYURIManager shareURIManager]
                 runActionWithURI:[IMYURI uriWithPath:@"msg/entrance"
                                               params:nil
                                                 info:nil]];
            } else if ([type isEqualToString:@"8"]) {
                // 8  '意见反馈',      xixiaoyou.com?__type=8
                [[IMYURIManager shareURIManager] runActionWithString:@"feedback"];
                
            } else if ([type isEqualToString:@"9"]) {
                // 9  '柚子街',        xixiaoyou.com?__type=9
                [[IMYURIManager shareURIManager] runActionWithString:@"sale/home"];
            } else if ([type isEqualToString:@"10"]) {
                // 10  '我的柚币',     xixiaoyou.com?__type=10
                [[IMYURIManager shareURIManager] runActionWithString:@"youbi"];
            } else if ([type isEqualToString:@"11"]) {
                // 11  '签到',         xixiaoyou.com?__type=11
                [[IMYURIManager shareURIManager] runActionWithString:@"sale/sign"];
            } else if ([type isEqualToString:@"13"]) {
                // 13  '达人堂入口'    xixiaoyou.com?__type=13&forum_id=0
                if ([IMYPublicAppHelper shareAppHelper].hasLogin) {
                    [[IMYURIManager shareURIManager]
                     runActionWithURI:[IMYURI uriWithPath:@"circles/honorhall"
                                                   params:@{
                        @"forum_id" : @([dic[@"forum_id"]
                                         integerValue])
                     }
                                                     info:nil]];
                } else {
                    [IMYEventHelper event:@"dl"
                                  addType:0
                               attributes:@{@"来源" : @"话题详情"}];
                    [UIWindow imy_showTextHUD:kStatusText_unLogin];
                    [[IMYURIManager shareURIManager] runActionWithString:@"login"];
                }
            } else {
                NSLog(@"传入的type有问题");
                return;
            }
        } else {
            [self imy_push:[IMYVKWebViewController
                            webWithURLString:url.absoluteString]];
        }
    }
}

// MARK: - TTQTopicRichWithParserViewDelegate

- (void)shutOffGestureRecognizer:(BOOL)shutOff {
    // 选择文字的情况下取消手势返回
    self.navigationController.interactivePopGestureRecognizer.enabled = !shutOff;
}

- (void)attributedLabel:(IMYSelectableAttributedLabel *)attributedLabel
selectorPanGestureDidChangedAtPoint:(CGPoint)point {
    CGPoint pointInView = [attributedLabel convertPoint:point toView:self.view];
    CGFloat topOffset = self.forumView.imy_height + 72;
    CGFloat bottomOffSet =
    self.view.imy_height - 72 - self.inputTipToolView.imy_height;
    if (pointInView.y < topOffset) {
        CGFloat deltaOffset = topOffset - pointInView.y;
        CGPoint contentOffset = self.tableView.contentOffset;
        contentOffset.y = MAX(contentOffset.y - deltaOffset, 0);
        self.tableView.contentOffset = contentOffset;
    } else if (pointInView.y > bottomOffSet) {
        CGFloat deltaOffset = pointInView.y - bottomOffSet;
        CGPoint contentOffset = self.tableView.contentOffset;
        contentOffset.y =
        MIN(contentOffset.y + deltaOffset, self.tableView.contentSize.height);
        self.tableView.contentOffset = contentOffset;
    }
    NSLog(@"======%@", NSStringFromCGPoint(pointInView));
}

/**
 选择位置改变回调，外部决定复制按钮显示的位置
 */
- (CGRect)attributedLabel:(IMYSelectableAttributedLabel *)attributedLabel
selectedAreaChangedWithSelectedRects:(NSArray<NSString *> *)selectedRects {
    if (selectedRects.count == 0) {
        return CGRectZero;
    }
    
    CGFloat topLimitValue = self.forumView.imy_height;
    CGFloat bottomLimitValue =
    self.view.imy_height - self.inputTipToolView.imy_height;
    
    CGRect topLineRect = CGRectZero;
    CGRect bottomLineRect = CGRectZero;
    if (selectedRects.count == 1) {
        topLineRect = bottomLineRect =
        [attributedLabel convertRect:CGRectFromString(selectedRects.firstObject)
                              toView:self.view];
    } else {
        topLineRect =
        [attributedLabel convertRect:CGRectFromString(selectedRects.firstObject)
                              toView:self.view];
        bottomLineRect =
        [attributedLabel convertRect:CGRectFromString(selectedRects.lastObject)
                              toView:self.view];
    }
    
    CGRect destShowRectInSelfView = CGRectZero;
    
    // 1、选择区域位于上半部分，菜单按钮显示在底部
    if (CGRectGetMinY(topLineRect) < topLimitValue &&
        CGRectGetMaxY(bottomLineRect) < bottomLimitValue &&
        CGRectGetMaxY(bottomLineRect) > topLimitValue) {
        destShowRectInSelfView = bottomLineRect;
    }
    
    // 2、选择区域位于下半部分，菜单按钮显示在顶部
    if (CGRectGetMinY(topLineRect) < bottomLimitValue &&
        CGRectGetMinY(topLineRect) > topLimitValue &&
        CGRectGetMaxY(bottomLineRect) > bottomLimitValue) {
        destShowRectInSelfView = topLineRect;
    }
    
    // 3、选择区域位于中间，菜单按钮显示在底部
    if (CGRectGetMinY(topLineRect) < bottomLimitValue &&
        CGRectGetMinY(topLineRect) > topLimitValue &&
        CGRectGetMaxY(bottomLineRect) < bottomLimitValue &&
        CGRectGetMaxY(bottomLineRect) > topLimitValue) {
        destShowRectInSelfView = bottomLineRect;
    }
    
    // 4、选择区域位于屏幕之外，全部在上面和全部在下面，菜单按钮不显示
    if (CGRectGetMinY(topLineRect) > bottomLimitValue ||
        CGRectGetMaxY(bottomLineRect) < topLimitValue) {
        destShowRectInSelfView = CGRectZero;
    }
    
    // 5、选择区域横跨整个屏幕，菜单按钮显示在中间
    if (CGRectGetMinY(topLineRect) < topLimitValue &&
        CGRectGetMaxY(bottomLineRect) > bottomLimitValue) {
        destShowRectInSelfView =
        CGRectMake(0, self.view.imy_height / 2, SCREEN_WIDTH, 30);
    }
    if (CGRectIsEmpty(destShowRectInSelfView)) {
        return CGRectZero;
    }
    return
    [attributedLabel convertRect:destShowRectInSelfView fromView:self.view];
}

- (void)didAddVideoView:(IMYVideoView *)videoView {
}

- (void)postYouPinBIData:(NSDictionary *)paramsDic {
    [self.viewModel postYouPinBIData:paramsDic];
}

// MARK: - IMYNewPageVideoViewDelegate

- (void)sto_autoPlayVideoCell:(TTQNewPageVideoView *)cell toMute:(BOOL)isMute {
    self.isMute = isMute;
    // 通知外部
    [[NSNotificationCenter defaultCenter]
     postNotificationName:TTQSetMuteNotification
     object:@{
        TTQSetMuteKey : @(self.isMute)
    }];
}

// MARK: - 事件

- (void)doSelectAllCellsInSameSection:(NSIndexPath *)indexPath
                        isHighlighted:(BOOL)isHighlighted {
}

- (void)followQuestionAction:(UIButton *)btn {
    //断网弹出吐司
    if (![IMYNetState networkEnable]) {
        [UIWindow imy_showTextHUD:MT_Request_NoNetToast];
        return;
    }
    if ([self hasLogin]) {
        NSString *topic_id =
        [@[ @(self.viewModel.topic.topic_id) ] componentsJoinedByString:@","];
        BOOL isCancel = self.viewModel.topic.is_followup == 1;
        RACSignal *signal = [[[[TTQHttpHelper postPath:@"v2/user_topic_follow"
                                                params:@{
            @"topic_id" : topic_id,
            @"type" : @(isCancel ? 2 : 1)
        }]
                               deliverOnMainThread] doNext:^(id<IMYHTTPResponse> response) {
            NSString *public_type = @"";
            if (isCancel) {
                [UIWindow imy_showTextHUD:IMYString(@"取消关注成功")];
                public_type = IMYString(@"取消关注");
            } else {
                [UIWindow imy_showTextHUD:IMYString(@"关注成功")];
                public_type = IMYString(@"关注问题");
                [IMYEventHelper event:@"wdxqy_gzwt"
                           attributes:@{
                    @"mode" : @([IMYPublicAppHelper shareAppHelper].userMode)
                }];
            }
            
            [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"dsq_nrxqy_gzwt",@"action":@2,@"info_type":@12,@"info_id":@(self.viewModel.topic_id),@"fuid":@(self.viewModel.topicUserID),@"public_type":public_type} headers:nil completed:nil];
        }] doError:^(NSError *error) {
            if (isCancel) {
                [UIWindow imy_showTextHUD:IMYString(@"取消关注失败，请重试")];
            } else {
                if (error.code == 13087007) {
                    [UIWindow imy_showTextHUD:IMYString(@"帖子审核中，请稍后")];
                } else {
                    [UIWindow imy_showTextHUD:IMYString(@"关注失败，请重试")];
                }
            }
        }];
        
        [signal subscribeNext:^(id x) {
            self.viewModel.topic.is_followup =
            self.viewModel.topic.is_followup == 0 ? 1 : 0;
            if (self.viewModel.topic.is_followup == 1) {
                self.viewModel.topic.followup_num++;
                self.viewModel.topic.followup_count++;
            } else {
                self.viewModel.topic.followup_num--;
                self.viewModel.topic.followup_count--;
            }
            
            [(IMYQATopicDetailHeadView *)self.tableView.tableHeaderView
             updatefollowButtonShow:YES
             status:self.viewModel.topic.is_followup];
        }];
    }
}

- (BOOL)hasLogin {
    if (![IMYPublicAppHelper shareAppHelper].hasLogin) {
        [UIWindow imy_showTextHUD:kStatusText_unLogin];
        [[IMYURIManager shareURIManager] runActionWithString:@"login"];
        return NO;
    }
    
    return YES;
}

- (BOOL)loginActicon {
    if (![IMYPublicAppHelper shareAppHelper].hasLogin) {
        [IMYEventHelper event:@"dl" addType:0 attributes:@{@"来源" : @"话题详情"}];
        [UIWindow imy_showTextHUD:kStatusText_unLogin];
        [[IMYURIManager shareURIManager] runActionWithString:@"login"];
        return NO;
    } else {
        return YES;
    }
}

// MARK: - 缓存

- (NSDictionary *)infoForTable {
    NSMutableDictionary *dic = [NSMutableDictionary dictionary];
    NSIndexPath *indexPath = [self curFirstVisableIndexPath];
    if (indexPath) {
        dic[@"firstIndexPath"] = indexPath;
        if (indexPath.section < self.tableView.numberOfSections) {
            dic[@"indexPathOffset"] =
            @([self.tableView rectForRowAtIndexPath:indexPath].origin.y);
        }
    }
    indexPath = [self curLastVisableIndexPath];
    if (indexPath) {
        dic[@"lastIndexPath"] = indexPath;
    }
    dic[@"contentOffset"] = @(self.tableView.contentOffset.y);
    dic[@"hasTableHead"] = @(self.tableView.tableHeaderView != nil);
    return dic;
}

- (NSIndexPath *)curLastVisableIndexPath {
    NSIndexPath *indexPath = self.tableView.indexPathsForVisibleRows.lastObject;
    if (indexPath == nil && self.viewModel.dataSource.count > 1) {
        CGFloat contentOffset = self.tableView.contentOffset.y;
        if (indexPath == nil && contentOffset + self.tableView.imy_height >
            self.tableView.tableHeaderView.imy_height) {
            if (self.tableView.tableFooterView &&
                contentOffset + self.tableView.imy_height >=
                self.tableView.tableFooterView.imy_top) {
                indexPath =
                [NSIndexPath indexPathForRow:0
                                   inSection:self.viewModel.dataSource.count - 1];
            } else if (self.tableView.tableFooterView == nil &&
                       contentOffset + self.tableView.imy_height >=
                       self.tableView.contentSize.height - 10) {
                indexPath =
                [NSIndexPath indexPathForRow:0
                                   inSection:self.viewModel.dataSource.count - 1];
            }
        }
    }
    return indexPath;
}

- (NSIndexPath *)curFirstVisableIndexPath {
    NSArray *visibleRows = [self.tableView indexPathsForVisibleRows];
    NSIndexPath *indexPath = visibleRows.firstObject;
    if (indexPath.section == 0 &&
        visibleRows.count >
        1) { //这里做这样的操作，是因为数据源第一个数据是硬塞进去的字串
        indexPath = visibleRows[1];
    }
    if (indexPath.row > 0) {
        indexPath = [NSIndexPath indexPathForRow:0 inSection:indexPath.section];
    }
    return indexPath;
}

- (void)requestOffsetCache {
    self.captionView.allTapToRetry = true;
    @weakify(self);
    self.captionView.retryBlock = ^{
        @strongify(self);
        if (![IMYNetState networkEnable]) {
            self.captionView.state = IMYCaptionViewStateRetry;
            [UIView imy_showTextHUD:IMYString(@"网络不见了，请检查网络")];
            return;
        }
        if (self.viewModel.gotoID > 0) {
            [self requestGoto];
        } else {
            [self refresh:false];
        }
    };
    //从消息进来的话，优先跳到具体的楼层
    if (self.viewModel.gotoID > 0) {
        if (self.captionView.retryBlock) {
            self.captionView.retryBlock();
        }
    } else {
        @weakify(self);
        [[[self.viewModel requestTopicCache]
          deliverOnMainThread] subscribeNext:^(TTQTopicCache *look) {
            @strongify(self);
            [self changeCommentFilter:self.viewModel.commentFilter
                          orderFilter:self.viewModel.orderByFilter
                               action:NO];
            if (self.viewModel.dataSource.count > 1) { //无网络情况
                //要调用finishedRequest。因为里面会发送tcp
                if (look.hasTableFoot) {
                    self.viewModel.automaticallyRefresh = NO;
                }
                [self finishedRequest:nil];
                
                if (look.gotoID > 0) {
                    [self.viewModel.dataSource
                     enumerateObjectsUsingBlock:^(TTQCommentModel *model,
                                                  NSUInteger index, BOOL *stop) {
                        @strongify(self);
                        
                        if (![model isKindOfClass:[TTQCommentModel class]]) {
                            return;
                        }
                        if (model.commentID == look.gotoID) {
                            *stop = YES;
                            NSIndexPath *indexPath =
                            [NSIndexPath indexPathForRow:0 inSection:index];
                            self.tableView.contentOffset = CGPointMake(
                                                                       0, look.offset_y +
                                                                       [self.tableView rectForRowAtIndexPath:indexPath]
                                                                       .origin.y);
                        }
                    }];
                } else {
                    self.tableView.contentOffset = CGPointMake(0, look.offset_y);
                }
            } else {
                if (self.captionView.retryBlock) {
                    if (self.viewModel.gotoID > 0) {
                        [self requestGoto];
                    } else {
                        [self refresh:false];
                    }
                } else {
                    if (self.viewModel.gotoID > 0) {
                        [self requestGoto];
                    } else {
                        [self refresh:false];
                    }
                }
            }
        }];
    }
}

// MARK: - 请求数据

- (void)requestRefresh:(void (^)(NSError *error))errorBlock
          successBlock:(void (^)())successBlock {
    @weakify(self);
    self.hasRequest = YES;
    [[self requestRemoteDataForType:0 params:nil] subscribeNext:^(id x) {
        @strongify(self);
        // 这边的定位会受到广告的影响，offset_y记录的位置包含了广告的偏移，
        // 当前时机可能没有广告或者广告的数量和高度和上一次的不一致，这里设置的offset_y会和上一次浏览的不一样
        if (self.viewModel.offset_y > 0) {
            self.viewModel.offset_y = MAX(
                                          0, MIN(self.viewModel.offset_y, self.tableView.contentSize.height -
                                                 self.tableView.imy_height));
            if (self.viewModel.offset_y > 0) {
                self.tableView.ttq_contentOffsetY = self.viewModel.offset_y;
            }
        }
        if (successBlock) {
            successBlock();
        }
        self.viewModel.offset_y = 0;
        [self requestAdList]; // 第一轮请求
        
    }
                                                          error:^(NSError *error) {
        if (errorBlock) {
            errorBlock(error);
        }
    }];
}

- (void)requestGoto {
    @weakify(self);
    [[[self requestRemoteDataForType:3 params:nil]
      deliverOnMainThread] subscribeNext:^(id x) {
        @strongify(self);
        __block BOOL findID = false;
        if (self.viewModel.dataSource.count > 1 && self.viewModel.gotoID > 0) {
            __block BOOL canJump = NO;
            [self.viewModel.dataSource
             enumerateObjectsUsingBlock:^(TTQCommentModel *model, NSUInteger index,
                                          BOOL *stop) {
                @strongify(self);
                if (![model isKindOfClass:[TTQCommentModel class]]) {
                    return;
                }
                if (self.viewModel.gotoID > 0) {
                    if (model.commentID == self.viewModel.gotoID) {
                        //从消息进来的。要去查找回复的楼层
                        findID = true;
                        canJump = true;
                    } else {
                    }
                }
                if (canJump) {
                    *stop = YES;
                    CGRect rect = [self.tableView
                                   rectForRowAtIndexPath:[NSIndexPath indexPathForRow:0
                                                                            inSection:index]];
                    if (rect.origin.y - 44 < 0) {
                        rect.origin.y = 44;
                    }
                    imy_asyncMainBlock(0.3, ^{
                        [self.tableView setContentOffset:CGPointMake(0, rect.origin.y - 44) animated:YES];
                        [self hideWarmReviewHighlightAtSection:index data:model];
                        if (self.viewModel.sub_review_id > 0) {
                            [self tableView:self.tableView didSelectRowAtIndexPath:[NSIndexPath indexPathForRow:0 inSection:index]];
                        }
                    });
                }
            }];
            //不能定位的话，那就定位到最后一楼
            if (!canJump) {
                [self.tableView
                 scrollToRowAtIndexPath:
                     [NSIndexPath indexPathForRow:0
                                        inSection:[self.viewModel lastCommentIndex]]
                 atScrollPosition:UITableViewScrollPositionNone
                 animated:NO];
            }
        }
        [self requestAdList];
        if (self.viewModel.gotoID > 0 && !findID) {
            if (self.viewModel.orderByFilter != TTQOrderByFilterHot) {
                [UIWindow imy_showTextHUD:IMYString(@"该楼层已被删除")];
                [TTQMessageDetailViewModel
                 deleteMessageByReviewId:self.viewModel.gotoID
                 topic_id:self.viewModel.topic_id];
                self.viewModel.inputDefaultText = nil;
            }
        }
        [self forcedToReply];
    }];
}
- (void)finishedRequest:(NSError *)error {
    if (!error) { // 上拉加载更多
        [self requestMoreAdList];
    }
    
    @weakify(self);
    imy_asyncMainBlock(^{// 请求后矫正一下位置
        @strongify(self);
        CGRect frame = self.view.frame;
        self.inputTipToolView.imy_bottom = frame.size.height - SCREEN_STATUSBAR_HEIGHT;
        self.tableView.imy_top = SCREEN_NAVIGATIONBAR_HEIGHT;
        self.tableView.imy_height = self.inputTipToolView.imy_top - self.tableView.imy_top;
        self.captionView.imy_top = self.tableView.imy_top;
    });

    // 使用完重置状态
    self.viewModel.lastCommentCount = 0;
    self.viewModel.lastResType = -1;
    
    BOOL hasDataAndNet = YES;
    [self updateFooterWithMoreAction:NO];
    self.viewModel.datasourceOrderFilter = self.viewModel.orderByFilter;
    self.viewModel.datasourceCommentFilter = self.viewModel.commentFilter;
    [self.adManager.adInfo unlock];
    [self.adManager.adInfo appendUserInfo:@{
        @"showTableHeader" : @(self.viewModel.showTableHeader)
    }];
    [self.adManager.adInfo appendUserInfo:@{
        @"commentsCount" : @([self.viewModel commentCount])
    }];
    [self.adManager.adInfo lock];
    [self bringScreenTipToolToFront];
    [self setupTableHeader];
    [self setupTopicSatusTopView];
    [self.inputTipToolView.praiseView bindModel:self.viewModel.topic
                                      viewModel:self.viewModel];
    // 以服务端的数据为准，更新排序选项
    [self.screenTipToolView setOrderAppearbyFilter:self.viewModel.orderByFilter];
    [self.screenTipToolView stopLoadind];
    [super finishedRequest:error];
    if (error) {
        if (error.code % 1000 == 400) {
            [UIWindow imy_showHUDwithNetworkError:error
                                andResponseObject:error.af_responseData];
            if ([IMYNetState networkEnable]) {
                [self.captionView setTitle:MT_Request_Retry
                                  andState:IMYCaptionViewStateRetry];
            } else {
                [self.captionView setTitle:MT_Request_NoNet
                                  andState:IMYCaptionViewStateRetry];
                hasDataAndNet = NO;
            }
        } else if (error.code % 1000 == 404 || error.code % 1000 == 410) { //话题已删除
            self.viewModel.becomeFirstResponder = NO;
            IMYWebMessageModel *failModel =
            [error.af_responseData toModel:[IMYWebMessageModel class]];
            NSString *showMessage = failModel.message ?: IMYString(@"该话题已被删除");
            [self.captionView setTitle:showMessage
                              andState:IMYCaptionViewStateNoResult];
            [UIWindow imy_showTextHUD:@"%@", showMessage];
            @weakify(self);
            imy_asyncMainBlock(0.5, ^{
                @strongify(self);
                [self imy_pop:YES];
            });
        } else if ([self.viewModel isCurFilterDataSourceNil] &&
                   self.viewModel.topic == nil) {
            // 这里需要处理下详情页数据为空的情况，没有评论并且没有详情内容，父类TTQTableViewController使用dataSource判断在详情页的结构改造之后会有问题，
            if ([IMYNetState networkEnable]) {
                [self.captionView setTitle:MT_Request_Retry
                                  andState:IMYCaptionViewStateRetry];
            } else {
                [self.captionView setTitle:MT_Request_NoNet
                                  andState:IMYCaptionViewStateRetry];
                hasDataAndNet = NO;
            }
        } else {
            // 切换最新最热请求失败
            self.isChangeFilterFailed = YES;
            [UIWindow imy_showTextHUD:IMYString(@"网络缓慢，请稍后再试")];
            return;
        }
        // 错误的情况也需要reload
        // 1、更新数据源需要刷新
        [self refreshTableView];
        return;
    }
    
    BOOL isDisableShare = [TTQDoorConfig isDisableShare];
    if (isDisableShare) {
        self.topRightButton.hidden = YES;
    } else {
        self.topRightButton.hidden = !hasDataAndNet;
    }
    
    if (self.viewModel.topic) {
        if (error == nil) {
            self.captionView.state = IMYCaptionViewStateHidden;
            [self forcedToReply];
        } else {
            self.viewModel.becomeFirstResponder = NO;
        }
        if (self.viewModel.topic.is_followup == 1 && !self.sendTCP) {
            self.sendTCP = true;
            [self.viewModel sendTCPMessage:true];
        }
    } else if (self.viewModel.dataSource.count <= 1) {
        self.viewModel.becomeFirstResponder = NO;
        if (error == nil) {
            [self.captionView setTitle:MT_Request_Retry
                              andState:IMYCaptionViewStateRetry];
        } else {
            if ([IMYNetState networkEnable]) {
                [self.captionView setTitle:MT_Request_Retry
                                  andState:IMYCaptionViewStateRetry];
            } else {
                [self.captionView setTitle:MT_Request_NoNet
                                  andState:IMYCaptionViewStateRetry];
            }
        }
    }
    if (self.captionView.state == IMYCaptionViewStateLoading) {
        if ((self.viewModel.topic && self.viewModel.showTableHeader) ||
            self.viewModel.dataSource.count > 1) {
            self.captionView.state = IMYCaptionViewStateHidden;
        } else {
            [self.captionView setTitle:MT_Request_Retry
                              andState:IMYCaptionViewStateRetry];
        }
    }
    
    if (self.screenTipToolView) {
        [self.tableView bringSubviewToFront:self.screenTipToolView];
        [self bringScrollIndicatorToFront];
    }
    
    // 定位到评论区域的处理逻辑
    BOOL isGotoMode = self.viewModel.gotoID > 0;
    if (!isGotoMode && self.viewModel.locate_to_comment) {
        self.viewModel.locate_to_comment = NO;
        imy_asyncMainBlock(0.3, ^{
            CGFloat maxOffset = MIN(MAX(0, self.tableView.contentSize.height - self.tableView.imy_height - self.tableView.contentInset.top),[self firstSectionHeaderY]);
            
            [self.tableView setContentOffset:CGPointMake(0, maxOffset)
                                    animated:YES];

        });
    }
    
    // 修正TableView的偏移，有可能偏移超过了TableView的高度
    imy_asyncMainBlock(^{
        [self updateTableViewToMaxOffset:NO];
    });
    
    [self footerReload];
    
    // 处理视频播放
    [self forceVideoToReplay];
}

-(void)footerReload{
    // 处理Header/Footer
    if ([self.tableView.mj_footer
         isKindOfClass:TTQRefreshBackNormalFooter.class]) {
        TTQRefreshAutoNormalFooter *footer =
        (TTQRefreshAutoNormalFooter *)self.tableView.mj_footer;
        footer.automaticallyRefresh = self.viewModel.automaticallyRefresh;
        footer.hidden = NO;
//        self.screenTipToolView.orderbyButton.hidden = NO;
        if (self.viewModel.automaticallyRefresh) {
            [footer
             setTitle:(IMYString(@"上拉加载更多"))forState:MJRefreshStateIdle];
            [self.tableView imy_footerEndRefreshing];
        } else {
            if (self.viewModel.isCurFilterDataSourceNil) {
//                self.screenTipToolView.orderbyButton.hidden = YES;
                [footer setTitle:@"" forState:MJRefreshStateNoMoreData];
            } else {
                if ([self containRelatedQuestionOrAnswer]) {
                    [footer setTitle:@"" forState:MJRefreshStateNoMoreData];
                    self.tableView.mj_footer.hidden = YES;
                } else {
                    [footer setTitle:(IMYString(@"已显示全部"))forState
                                    :MJRefreshStateNoMoreData];
                }
            }
            [self.tableView imy_endFooterRefreshAndNoMore];
        }
    }
}

- (BOOL)isArray:(NSInteger)section {
    if (section < self.viewModel.dataSource.count) {
        return [self.viewModel.dataSource[section] isKindOfClass:[NSArray class]];
    }
    return NO;
}

// MARK: - 视频处理

- (void)addVideoViewToCheckList:(IMYVideoView *)videoView {
}

/**
 数据加载的场景使用，强制暂停正在播放的视频，然后重新执行视频自动播放流程
 */
- (void)forceVideoToReplay {
}

- (IMYVideoView *)handleFindVideoToPause:(IMYVideoView *)videoView
                         unvisableFactor:(CGFloat)unvisableFactor
                               edgeInset:(UIEdgeInsets)edgeInset {
    if (![videoView isKindOfClass:[IMYVideoView class]]) {
        return nil;
    }
    if (videoView.isPlaying) {
        CGRect containerFrame = self.tableView.frame;
        containerFrame.origin.y += edgeInset.top;
        containerFrame.size.height -= edgeInset.top;
        CGRect videoRect =
        [videoView convertRect:videoView.bounds toView:self.view];
        CGRect intersect = CGRectIntersection(containerFrame, videoRect);
        CGFloat intersectHeight = CGRectGetHeight(intersect);
        if (intersectHeight < videoView.imy_height * unvisableFactor) {
            return videoView;
        }
    }
    return nil;
}

- (IMYVideoView *)handleFindVideoToPaly:(IMYVideoView *)videoView
                          visableFactor:(CGFloat)visableFactor
                              edgeInset:(UIEdgeInsets)edgeInset {
    
    if (![videoView isKindOfClass:[IMYVideoView class]]) {
        return nil;
    }
    
    if (videoView.model) {
        CGRect containerFrame = self.tableView.frame;
        containerFrame.origin.y += edgeInset.top;
        containerFrame.size.height -= edgeInset.top;
        CGRect videoRect =
        [videoView convertRect:videoView.bounds toView:self.view];
        CGRect intersect = CGRectIntersection(containerFrame, videoRect);
        float intersectHeight = CGRectGetHeight(intersect);
        //当videoView和tableView交集的矩阵高度大于 1/3的videoView高度
        if (intersectHeight > videoView.imy_height * visableFactor) {
            return videoView;
        }
    }
    
    return nil;
}

/**
 视频播放：网络提示
 */
- (void)showPlayInCelluarTipsWithVideoView:(IMYVideoView *)videoView {
    // 相关推荐的视频网络提示在TTQNewPageVideoView类中处理，这里只需要处理详情页的播放控件即可
    if (videoView.videoType == TTQVideoViewTypeDetail) {
        if (![IMYNetState networkEnable]) {
            [UIView imy_showTextHUD:IMYString(@"网络不见了，请检查网络")];
            return;
        }
        if (![IMYNetState isWiFi] &&
            [TTQVideoPlayerManager sharedPlayerManager].firstPlayWith4G) {
            [TTQVideoPlayerManager sharedPlayerManager].firstPlayWith4G = NO;
            [UIWindow imy_showTextHUD:@"正使用移动网络播放"];
        }
    }
}

// MARK: - 音频控制

- (void)pauseBackgroundSoundWithError:(NSError **)error {
    [self imy_asyncBlock:^{
        if ([AVAudioSession sharedInstance].isOtherAudioPlaying) {
            // 先设置categoty为AVAudioSessionCategorySoloAmbient独占模式，然后设置active停止第三方音乐的播放。这种category会导致在静音下不播放声音
            [[AVAudioSession sharedInstance]
             setCategory:AVAudioSessionCategorySoloAmbient
             error:error];
            [[AVAudioSession sharedInstance] setActive:YES error:error];
            // 停止第三方音乐播放之后需要重新设置category为AVAudioSessionCategoryPlayback，在静音模式下也能播放声音
            [[AVAudioSession sharedInstance]
             setCategory:AVAudioSessionCategoryPlayback
             error:nil];
        }
    }];
}

// MARK: - bi统计

- (void)GAEventForPOP:(NSIndexPath *)indexPath_ {
    if (self.viewModel.topic == nil && self.viewModel.dataSource.count <= 1) {
        return;
    }
    if (self.viewModel.commentFilter != TTQTopicFilterNone) {
        return;
    }
    NSInteger floor = 0;
    
    if (self.viewModel.dataSource.count > 1) {
        NSIndexPath *indexPath = indexPath_;
        if (indexPath.section < self.viewModel.dataSource.count &&
            indexPath.section >= 0) {
            TTQCommentModel *model = self.viewModel.dataSource[indexPath.section];
            if ([model isKindOfClass:[TTQCommentModel class]]) {
                floor = model.floor_no;
            }
        }
    }
    [TTQCommonHelp GAEventForReviewWith:@{
        @"total_floor" : @(self.viewModel.topic.total_floor),
        @"floor" : @(floor),
        @"topic_id" : @(self.viewModel.topic_id)
    }];
}

- (void)postBiVideoEventWithVideoView:(IMYVideoView *)videoView {
    NSMutableDictionary *params = [NSMutableDictionary dictionaryWithCapacity:0];
    // 业务参数
    params[@"topic_id"] = @(self.viewModel.topic.topic_id);
    params[@"subject_id"] = @(self.viewModel.topic.theme_id);
    params[@"community_type"] = @1;
    params[@"entrance"] =
    self.viewModel.bi_video_play_entrance
    ? @(self.viewModel.bi_video_play_entrance)
    : (self.viewModel.entrance ? @(self.viewModel.entrance) : @(4));
    if (self.al_source)
        params[@"al_source"] = self.al_source;
    if (self.algorithm)
        params[@"algorithm"] = self.algorithm;
    params[@"duration"] = videoView.track.totalDuration;
    // 视频参数
    params[@"star_type"] = @(self.bi_startType ? self.bi_startType : 6);
    params[@"end_type"] = @(self.bi_endType);
    params[@"star_duration"] = @((NSInteger)self.bi_startDuration);
    params[@"end_duration"] = @((NSInteger)self.bi_endDuration);
    params[@"duration"] =
    @((NSInteger)(videoView.track.totalDuration.integerValue * 1000));
    [IMYGAEventHelper postWithPath:@"bi_ttqvideoplay"
                            params:params
                           headers:nil
                         completed:nil];
}

- (void)postPlayCountStatistaics {
    // 视频播放次数上报 @see
    // https://gitlab.meiyou.com/meetyou_interface_group/community_interface_project/blob/master/v2/%E8%A7%86%E9%A2%91-%E6%92%AD%E6%94%BE%E7%BB%9F%E8%AE%A1.md
    NSDictionary *params =
    @{ @"video_id" : @(self.viewModel.topic.topic_id),
       @"type" : @(1) };
    [[TTQHttpHelper postPath:@"v2/video_statistics" params:params]
     subscribeNext:^(id x){
    }];
}

// action: 曝光1 点击2
- (void)recommendCellTrackIndex:(NSUInteger)index
                         action:(NSNumber *)action
                          model:(TTQTopicModel *)model {
    if (model == nil) {
        return;
    }
    
    NSDictionary *params = @{
        @"question_id" : @(model.topic_id),
        @"action" : action,
        @"entrance_id" : self.entrance_id > 0 ? @(self.entrance_id) : @(5),
        @"floor" : @(index + 1),
    };
    if (self.catid > 0) {
        NSMutableDictionary *paramsDic =
        [[NSMutableDictionary alloc] initWithDictionary:params];
        [paramsDic setValue:@(self.catid) forKey:@"catid"];
        params = [paramsDic copy];
    }
    [[IMYQAGAEventHelper sharedGAEvnetHelper] postWithPathFor_bi_yqwd_bgdj:params
                                                                   headers:nil
                                                                 completed:nil];
}

// MARK: - Getter
- (UIView *)headView {
    if (!_headView) {
        _headView = [UIView new];
        [_headView imy_setBackgroundColorForKey:kCK_Black_F];
        _headView.userInteractionEnabled = YES;
        [_headView addObserver:self
                    forKeyPath:@"frame"
                       options:(NSKeyValueObservingOptionNew |
                                NSKeyValueObservingOptionOld)
                       context:@"frame"];
    }
    return _headView;
}

- (NSPointerArray *)weakVideoViews {
    if (!_weakVideoViews) {
        _weakVideoViews = [NSPointerArray weakObjectsPointerArray];
    }
    return _weakVideoViews;
}

- (UIButton *)topRightButton {
    if (!_topRightButton && self.navigationItem.rightBarButtonItems.count == 0) {
        IMYTouchEXView *btBox;
        CGRect boxFrame;
        
        if ([@"消息" isEqualToString:self.viewModel.source] &&
            [TTQPushManager bOpenPush] && [TTQPushManager bOpenMsg]) {
            boxFrame = CGRectMake(0, 0, 65, 44);
            btBox = [[IMYTouchEXView alloc] initWithFrame:boxFrame];
            btBox.extendTouchInsets = UIEdgeInsetsMake(20, 6, 20, 20);
            
            boxFrame.origin.x -= 5;
            boxFrame.size.width += 10;
        } else {
            boxFrame = CGRectMake(0, 0, 40, 44);
            btBox = [[IMYTouchEXView alloc] initWithFrame:boxFrame];
        }
        
        _topRightButton = [[IMYTouchEXButton alloc] initWithFrame:boxFrame];
        [(id)_topRightButton setExtendTouchAllValue:20];
        
        BOOL isNight = [IMYPublicAppHelper shareAppHelper].isNight;
        BOOL isWhite = [self imy_isWhiteTopBar];
        if (!isNight && isWhite) {
            [_topRightButton imy_setTitleColor:kCK_Black_A];
        } else {
            [_topRightButton
             imy_setTitleColor:kIMY_TopbarButtonTitleColor
             highl:kIMY_TopbarButtonTitleHighlightedColor];
        }
        
        _topRightButton.titleEdgeInsets = UIEdgeInsetsMake(0, 0, 0, 2.5);
        _topRightButton.imageEdgeInsets = UIEdgeInsetsMake(0, 0, 0, 2.5);
        _topRightButton.contentHorizontalAlignment =
        UIControlContentHorizontalAlignmentRight;
        _topRightButton.titleLabel.font = [UIFont systemFontOfSize:16];
        
        [btBox addSubview:_topRightButton];
        UIBarButtonItem *item = [[UIBarButtonItem alloc] initWithCustomView:btBox];
        NSArray *items = @[ item ];
        self.navigationItem.rightBarButtonItems = items;
    }
    return _topRightButton;
}

- (NSString *)filterLinkWithTitle:(NSString *)title {
    // eg:标题有图片链接<a href="https://www.baidu.com/?__type=2"
    // >我百度</a>内容...
    NSMutableString *filterTitle = [NSMutableString new];
    NSRange linkrange = [title rangeOfString:@"<a href="];
    NSRange scrRange = [title rangeOfString:@"<img src="];
    // 标题带有 图片或者链接的，统一不显示
    if (linkrange.location != NSNotFound) {
        // 简单的过滤下内容中的标签以及换行，进行置顶展示
        NSString *content = [title stringByReplacingOccurrencesOfString:@"<img[^>]*>" withString:@"" options:NSCaseInsensitiveSearch | NSRegularExpressionSearch range:NSMakeRange(0, [title length])];
        content = [content stringByReplacingOccurrencesOfString:@"</img>" withString:@""];
        NSString *prestr = [content stringByReplacingOccurrencesOfString:@"<a[^>]*>" withString:@"" options:NSCaseInsensitiveSearch | NSRegularExpressionSearch range:NSMakeRange(0, [content length])];
        prestr = [prestr stringByReplacingOccurrencesOfString:@"</a>" withString:@""];
        prestr = [prestr stringByReplacingOccurrencesOfString:@"\r" withString:@""];
        prestr = [prestr stringByReplacingOccurrencesOfString:@"\n" withString:@""];
        return prestr;
    } else if (scrRange.location != NSNotFound) {
        return @"";
    } else {
        return title;
    }
}

- (void)navBarTitle {
    IMYQATopicDetailHeadView *headView =
    (IMYQATopicDetailHeadView *)self.tableView.tableHeaderView;
    if (self.tableView.contentOffset.y > headView.titleLabel.imy_bottom) {
        if (imy_isEmptyString(self.viewModel.topic.title)) {
            // 过滤标题中链接
            NSString *displayString =
            [self filterLinkWithTitle:self.viewModel.topic.content];
            if (imy_isNotEmptyString(displayString)) {
                self.navTitleLabel.text = displayString;
            }
        } else {
            self.navTitleLabel.text = self.viewModel.topic.title;
        }
        
    } else {
        self.navTitleLabel.text = @"";
    }
    [self changeNavigaitonBarUIWithTableViewOffset:self.tableView.contentOffset.y];
}

// 用于区分相关解答/相关问答模块交互
- (BOOL)containRelatedQuestionOrAnswer {
    if ([self containRelatedQuestion] || [self containRelatedAnswers]) {
        return YES;
    }
    return NO;
}

- (BOOL)containRelatedQuestion {
    if (self.viewModel.recommendQuestions != nil &&
        self.viewModel.recommendQuestions.count > 0) {
        if (self.viewModel.dataSource.lastObject == self.viewModel.recommendQuestions.lastObject)  {
            return YES;
        }
    }
    return NO;
}

- (BOOL)containRelatedAnswers {
    if (self.viewModel.recommendQATopics != nil &&
        self.viewModel.recommendQATopics.count > 0) {
        if (self.viewModel.dataSource.lastObject == self.viewModel.recommendQATopics.lastObject)  {
            return YES;
        }
    }
    return NO;
}

// action: 曝光1 点击2
- (void)relatedCellTrackIndex:(NSUInteger)index
                       action:(NSUInteger)action
                        model:(IMYQAQuestionAnswerModel *)model {
    if (model == nil) {
        return;
    }
    if (action == 1 && model.isTracked == YES) {
        return;
    }
    
    model.isTracked = YES;
    NSDictionary *params = @{
        @"question_id" : @(model.question.id),
        @"action" : @(action),
        @"entrance_id" : @(14),
        @"floor" : @(index + 1),
    };
    if (self.catid > 0) {
        NSMutableDictionary *paramsDic =
        [[NSMutableDictionary alloc] initWithDictionary:params];
        [paramsDic setValue:@(self.catid) forKey:@"catid"];
        params = [paramsDic copy];
    }
    [[IMYQAGAEventHelper sharedGAEvnetHelper] postWithPathFor_bi_yqwd_bgdj:params
                                                                   headers:nil
                                                                 completed:nil];
}

- (void)inputViewSend {
    [self inputViewSendFromKeyboard:NO];
}
//发送
- (void)inputViewSendFromKeyboard:(BOOL)fromKeyboard {
    NSMutableDictionary *plParams = [@{@"event":@"dsq_nrxqy_djfs",@"action":@2,@"info_type":@12,@"info_id":@(self.viewModel.topic_id),@"fuid":@(self.viewModel.topicUserID)} mutableCopy];
    if (fromKeyboard) {
        plParams[@"public_info"] = @"输入法发送";
    }

    [IMYGAEventHelper postWithPath:@"event" params:plParams headers:nil completed:nil];

    // 点击发送，进行判断是否登录、设置昵称、绑定手机号、加入圈子、封号
    if (![self loginActicon]) {
        [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"dsq_nrxqy_fssb",@"action":@2,@"info_type":@12,@"info_id":@(self.viewModel.topic_id),@"fuid":@(self.viewModel.topicUserID),@"publi_info":@"未登录"} headers:nil completed:nil];
        return;
    }
    
    if ([NSString
         imy_isEmptyString:[IMYPublicAppHelper shareAppHelper].nickName]) {
        [UIWindow imy_showTextHUD:IMYString(@"请先设置你的昵称哦~")];
        [[NSNotificationCenter defaultCenter]
         postNotificationName:TTQCloseInputViewAndJumpToSetupNickname
         object:nil];
        [[IMYURIManager shareURIManager] runActionWithString:@"user/nickname"];
        [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"dsq_nrxqy_fssb",@"action":@2,@"info_type":@12,@"info_id":@(self.viewModel.topic_id),@"fuid":@(self.viewModel.topicUserID),@"publi_info":@"无昵称"} headers:nil completed:nil];
        return;
    }
    
    if ([IMYAccountCheckService checkPhoneShouldBeBindedWithCompletion:nil]) {
        [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"dsq_nrxqy_fssb",@"action":@2,@"info_type":@12,@"info_id":@(self.viewModel.topic_id),@"fuid":@(self.viewModel.topicUserID),@"publi_info":@"手机未绑定"} headers:nil completed:nil];
        return;
    }
    
    if (![self canReply]) {
        @weakify(self);
        [UIAlertView
         imy_showAlertViewWithTitle:nil
         message:IMYString(@"加入话题所在的圈子后才能回复哦")
         cancelButtonTitle:IMYString(@"取消")
         otherButtonTitles:@[ IMYString(@"加入圈子") ]
         handler:^(UIAlertView *alertView,
                   NSInteger buttonIndex) {
            @strongify(self);
            if (buttonIndex == 1) {
                [self joinForum];
            }
        }];
        return;
    }
    
    // [IMYPublicAppHelper shareAppHelper].cannotAccess
    // 该接口并未完善，所以使用接口下发的字段作为判断
    //  if (![self canAccess]) {
    //    [UIWindow
    //        imy_showTextHUD:IMYString(@"您因违反她她圈圈规，已被禁止该操作！")];
    //    return;
    //  }
    
    if ([IMYPublicAppHelper shareAppHelper].cannotAccess) {
        @weakify(self);
        [UIAlertController
         imy_showAlertViewWithTitle:IMYString(@"来自 美柚助手 通知")
         message:IMYString(@"你因违反她她圈圈规，已被封号！")
         cancelButtonTitle:IMYString(@"我知道了")
         otherButtonTitles:nil
         handler:^(UIAlertController *alertController,
                   NSInteger buttonIndex) {
            @strongify(self);
            if (buttonIndex == 0) {
                [[IMYURIManager shareURIManager]
                 runActionWithPath:@"msg/system"
                 params:nil
                 info:nil];
            }
        }];
        [self hideKeyboard];
        [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"dsq_nrxqy_fssb",@"action":@2,@"info_type":@12,@"info_id":@(self.viewModel.topic_id),@"fuid":@(self.viewModel.topicUserID),@"publi_info":@"被封号"} headers:nil completed:nil];
        return;
    }
    
    if (![IMYNetState networkEnable]) {
        [UIWindow imy_showTextHUD:MT_Request_NoNetToast];
        [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"dsq_nrxqy_fssb",@"action":@2,@"info_type":@12,@"info_id":@(self.viewModel.topic_id),@"fuid":@(self.viewModel.topicUserID),@"publi_info":@"无网络"} headers:nil completed:nil];
        return;
    }
    
    NSString *text = [self.inputContentsView.textView.text imy_trimString];
    if ([NSString imy_isEmptyString:text]) {
        self.inputContentsView.textView.text = text;
        [UIWindow imy_showTextHUD:IMYString(@"您的回复为空，多写一点吧")];
        [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"dsq_nrxqy_fssb",@"action":@2,@"info_type":@12,@"info_id":@(self.viewModel.topic_id),@"fuid":@(self.viewModel.topicUserID),@"publi_info":@"回复内容为空"} headers:nil completed:nil];
        return;
    }
    
    NSMutableArray *imageUploads = [NSMutableArray new];
    [self.inputContentsView.photoView.cameraButtons
     bk_each:^(TTQCameraButton *cameraButton) {
        if (cameraButton.originalImage) {
            id<IMYOSSFileObject> fileObject = nil;
            if (cameraButton.originalImage.images.count > 1 &&
                cameraButton.assetURL) {
                fileObject = [[IMYOSS defaultUploader]
                              fileObjectWithName:cameraButton.getImageName
                              assetUrl:cameraButton.assetURL];
            } else {
                fileObject = [[IMYOSS defaultUploader]
                              fileObjectWithName:cameraButton.getImageName
                              image:cameraButton.originalImage];
            }
            fileObject.uploadType = IMYOSSUploadTypeImage;
            fileObject.querys = @{@"scene":@14};
            [imageUploads addObject:fileObject];
        }
    }];
    if (self.cameraButton.originalImage) {
        id<IMYOSSFileObject> fileObject = nil;
        if (self.cameraButton.originalImage.images.count > 1) {
            fileObject = [[IMYOSS defaultUploader]
                          fileObjectWithName:self.cameraButton.getImageName
                          image:self.cameraButton.originalImage];
        } else {
            fileObject = [[IMYOSS defaultUploader]
                          fileObjectWithName:self.cameraButton.getImageName
                          image:self.cameraButton.originalImage];
        }
        fileObject.uploadType = IMYOSSUploadTypeImage;
        fileObject.querys = @{@"scene":@14};
        [imageUploads addObject:fileObject];
    }
    
    if (imageUploads.count > 0) {
        [self.progressHUD showAnimated:YES];
        [self setProcessHUD:0.2];
        @weakify(self);
        __block NSError *_error = nil;
        [[IMYOSS defaultUploader] uploadAllObjects:imageUploads
                                     progressBlock:^(id<IMYOSSFileObject> _Nonnull object, double progress) {
            @strongify(self);
            self.progressHUD.progress = 0.2 + progress * 0.7 / imageUploads.count;
        }
                                    complatedBlock:^(id<IMYOSSFileObject> _Nonnull object,
                                                     NSError *_Nullable error) {
            if (error) {
                _error = error;
            }
        }
                                 allComplatedBlock:^(
                                                     NSArray<id<IMYOSSFileObject>> *_Nonnull allObjects) {
                                                         @strongify(self);
                                                         if (_error) {
                                                             @strongify(self);
                                                             [self hidenProgressHUD];
                                                             [self hideKeyboard];
                                                             [UIWindow imy_hideHUD];
                                                             if (_error.code == -121) {
                                                                 [UIWindow imy_showTextHUD:@"图片大小须不超过20M"];
                                                             } else {
                                                                 if ([IMYNetState networkEnable] &&
                                                                     _error.code != NSURLErrorNetworkConnectionLost &&
                                                                     _error.code != NSURLErrorNotConnectedToInternet) {
                                                                     [UIWindow imy_showHUDwithNetworkError:_error];
                                                                 } else {
                                                                     [UIWindow imy_showTextHUD:MT_Request_NoNetToast];
                                                                 }
                                                             }
                                                             
                                                             //发送到bugly
                                                             NSDictionary *errorMap =
                                                             [NSObject imy_validJSONObject:_error.userInfo];
                                                             NSMutableDictionary *extraInfo = [NSMutableDictionary dictionary];
                                                             extraInfo[@"error"] = [errorMap imy_jsonString];
                                                             [[IMYAppReport shareInstance]
                                                              reportErrorWithCategory:3
                                                              name:@"OSS Upload Fail In TTQ!"
                                                              reason:_error.domain
                                                              ?: @"com.imyttq.uploadImage"
                                                              callStack:nil
                                                              extraInfo:extraInfo];
                                                             [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"dsq_nrxqy_fssb",@"action":@2,@"info_type":@12,@"info_id":@(self.viewModel.topic_id),@"fuid":@(self.viewModel.topicUserID),@"publi_info":@"图片上传失败"} headers:nil completed:nil];
                                                             NSMutableDictionary *errorData = [NSMutableDictionary dictionary];
                                                             if (_error.userInfo) {
                                                                 [errorData addEntriesFromDictionary:_error.userInfo];
                                                             }
                                                             [IMYErrorTraces postWithType:IMYErrorTraceTypeOthers pageName:NSStringFromClass([UIViewController imy_currentViewControlloer].class) category:IMYErrorTraceCategoryCommunity message:@"问答评论图片上传失败" detail:errorData];

                                                         } else {
                                                             @strongify(self);
                                                             
                                                             [self postWithImageNames:[allObjects
                                                                                       bk_map:^id(
                                                                                                  id<IMYOSSFileObject> fileObject) {
                                                                                                      return fileObject.url.absoluteString;
                                                                                                  }]];
                                                         }
                                                     }];
        
    } else {
        [self postWithImageNames:nil];
    }
}

//进度展示
- (MBProgressHUD *)progressHUD {
    if (_progressHUD == nil) {
        UIWindow *topWindow = [UIWindow imy_getShowTopWindow];
        //        UIView *topWindow = self.view;
        _progressHUD = [MBProgressHUD progressHUDForRound:topWindow];
    }
    return _progressHUD;
}

- (void)hidenProgressHUD {
    [UIWindow imy_hideHUD];
    if (_progressHUD) {
        [self.progressHUD removeFromSuperview];
        [self.progressHUD hideAnimated:YES];
        self.progressHUD = nil;
    }
}

- (void)setProcessHUD:(CGFloat)progress {
    self.progressHUD.progress = progress;
}

- (TTQCameraButton *)cameraButton {
    return self.inputContentsView.cameraButton;
}

- (void)postWithImageNames:(NSArray *)imageNames {
    @weakify(self);
    ProgressCallback progressBlock = nil;
    if (imageNames.count == 0) {
        [self hidenProgressHUD];
        [UIWindow imy_showLoadingHUDWithText:IMYString(@"发送中")];
    } else {
        [self setProcessHUD:0.9];
        progressBlock = ^(int64_t completedUints, int64_t totalUnits) {
            imy_asyncMainBlock(^{
                @strongify(self);
                [self setProcessHUD:0.1 * completedUints / (double)totalUnits + 0.9f];
            });
        };
    }
    __block NSMutableDictionary *params = [[NSMutableDictionary alloc] init];
    params[@"content"] = [self inputContentFromTextView];
    if (imageNames.count > 0) {
        params[@"images"] = imageNames;
        NSMutableArray *array = [NSMutableArray arrayWithCapacity:imageNames.count];
        for (NSString *imageUrl in imageNames) {
            NSMutableDictionary *imageDic = [NSMutableDictionary dictionaryWithCapacity:1];
            imageDic[@"url"] = [params[@"images"] firstObject];
            imageDic[@"referer"] = @0;
            [array addObject:imageDic];
        }
        params[@"images_v2"] = array;
    }
    [[[self.viewModel.replyCommand
       execute:(progressBlock == nil ? @[ params ] : @[ params, progressBlock ])]
      deliverOnMainThread] subscribeNext:^(id<IMYHTTPResponse> response) {
        @strongify(self);
        NSString *content = self.inputContentsView.textView.text;
        [self hidenProgressHUD];
        self.viewModel.inputDefaultText = nil;
        self.inputContentsView.textView.text = nil;
        [self.inputContentsView clearPhoto];
        
        // 需要在重置inputContentsView内容之后调用隐藏键盘，否则隐藏键盘的步骤会记录数据导致错误
        [self hideKeyboardForceDispatchToMain:NO];
        
        self.viewModel.selectedReplyIndex = nil;
        NSInteger timestamp = [response.responseObject[@"timestamp"] integerValue];
        NSInteger score = [response.responseObject[@"score"] integerValue];
        if (score) {
            [UIWindow imy_hideHUD];
            [[TTQNewbeeTaskManager sharedManager]
             showTipWithMessage:
                 [NSString stringWithFormat:@"恭喜完成评论帖子任务 贡献值+%li",
                  (long)score]];
            [self finishedReplyRequest:content timestamp:timestamp];
        } else {
            [self showReplySuccessHub];
        }
        [self finishedRequest:nil];
        if (score == 0) {
            [self finishedReplyRequest:content timestamp:timestamp];
        }
#if __has_include(<IMYEBPublic/IMYEBYoubiTaskManager.h>)
        [[IMYEBYoubiTaskManager shareManager] oprationTaskFinishedWithKey:@"community_review" uploadParams:nil];
#endif

    }
     error:^(NSError *error) {
        @strongify(self);
        [self hidenProgressHUD];
        [UIWindow imy_hideHUD];
        
        NSString *postFaildMsg = [[NSString alloc] initWithData:error.af_responseData encoding:NSUTF8StringEncoding];
        NSString *stringError = [NSString stringWithFormat:@"接口报错:%@",error.localizedDescription];
        stringError = imy_isNotEmptyString(postFaildMsg)?postFaildMsg:stringError;
        [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"dsq_nrxqy_fssb",@"action":@2,@"info_type":@12,@"info_id":@(self.viewModel.topic_id),@"fuid":@(self.viewModel.topicUserID),@"publi_info":stringError} headers:nil completed:nil];
        NSMutableDictionary *errorData = [NSMutableDictionary dictionary];
        if (error.userInfo) {
            [errorData addEntriesFromDictionary:error.userInfo];
        }
        if (params) {
            errorData[@"requestParams"] = params;
        }
        if (error.af_responseData) {
            errorData[@"responseData"] = [error.af_responseData responseString];
        }

        [IMYErrorTraces postWithType:IMYErrorTraceTypeOthers pageName:NSStringFromClass([UIViewController imy_currentViewControlloer].class) category:IMYErrorTraceCategoryCommunity message:@"问答评论失败" detail:errorData];

        /**
         //775 如果进入安全实验, 服务器返回403请求失败后,
         会用备用域名再次发起请求, 这样就导致服务器会返回error错误提示,
         认为在10s重复发帖, 返回提示信息,  这时候我们不应该展示提示信息
         【【iOS】触发403状态码，吐司提示：request
         failed：forbidden（403），优化去掉】
         https://www.tapd.cn/67189002/bugtrace/bugs/view/1167189002001087095
         */
        if (error.code % 1000 == 400) {
            [UIWindow imy_showHUDwithNetworkError:error
                                andResponseObject:error.af_responseData];
            [self clearInputView]; //清除输入框内容
        } else if (error.code == kPhoneDubiousErrorCode ||
                   error.code == kPhoneStolenErrorCode) {
            [self hideKeyboard];
            imy_asyncMainBlock(0.15, ^{
                NSInteger type = (error.code == kPhoneStolenErrorCode) ? 1 : 2;
                [[IMYURIManager shareURIManager]
                 runActionWithPath:@"account/phone/verify"
                 params:@{
                    @"type" : @(type)
                }
                 info:nil];
            });
        } else if (error.code % 1000 == 422) {
            NSString *message = nil;
            IMYWebMessageModel *failModel =
            [UIWindow imy_showHUDwithNetworkError:error
                                andResponseObject:error.af_responseData];
            if (imy_isNotEmptyString(failModel.message)) {
                message = failModel.message;
            }
            
            [UIWindow imy_showTextHUD:IMYString(message)];
            
            NSUInteger commentID = [params[@"referenced_id"] integerValue];
            if (commentID) {
                [TTQMessageDetailViewModel
                 deleteMessageByReviewId:commentID
                 topic_id:self.viewModel.topic_id
                 postNotification:NO];
            }
            
        } else {
            if (error.code != ********) {
                [self hideKeyboard];
            } else {
                //  ********作者开启了限制评论功能
                IMYWebMessageModel *failModel = [error.af_responseData toModel:[IMYWebMessageModel class]];
                if (failModel.message.length > 0) {
                    [UIView imy_showTextHUD:failModel.message];
                    return;
                }
            }
            /// 10000113 代表用户安全问题, 不需要额外弹窗 775
            NSData *responseData = error.af_responseData;
            NSDictionary *errorMap =
            [NSData imy_dictionaryWithJSONData:responseData];
            NSInteger errorCode = [[errorMap objectForKey:@"code"] integerValue];
            if (errorCode == 10000113) {
                return;
            }
            TTQTopicCurrentUserInfo *currentUserInfo =
            [errorMap[@"user_info"] toModel:[TTQTopicCurrentUserInfo class]];
            if ([currentUserInfo isKindOfClass:TTQTopicCurrentUserInfo.class] &&
                currentUserInfo.feedback_id) {
                [self judgeBlockedWithUserInfo:currentUserInfo];
                return;
            }
            IMYWebMessageModel *failModel = [error.af_responseData toModel:[IMYWebMessageModel class]];
            // code = 16/11的情况在底层未做处理，业务端处理
            //          if (failModel.code == 16 || failModel.code == 11) {
            //            if (failModel.message.length > 0) {
            //              [UIView imy_showTextHUD:failModel.message];
            //            } else {
            //              [UIView imy_showHUDwithNetworkError:error];
            //            }
            //          }
            [UIWindow imy_showTextHUD:failModel.message?:IMYString(@"发布失败，请重试")];
            
        }
    }];
}

- (void)clearInputView {
    if (self.inputContentsView.textView.internalTextView.inputView != nil) {
        self.inputContentsView.textView.internalTextView.inputView = nil;
        [self.inputContentsView.textView.internalTextView reloadInputViews];
        [self.inputContentsView restPoseForInputText];
    }
}

- (BOOL)canReply {
    return self.viewModel.canReply;
}

- (BOOL)canAccess {
    return self.viewModel.canAccess;
}

- (BOOL)hasBlocked {
    return self.viewModel.hasBlocked;
}

- (void)judgeBlockedWithUserInfo:(TTQTopicCurrentUserInfo *)userInfo {
    //禁言处理
    NSString *message =
    userInfo.error == 3
    ? @"违反圈规被禁言"
    : (userInfo.error == 2 ? @"违反圈规被封号" : @"违反圈规被禁言");
    [UIAlertController
     imy_showAlertViewWithTitle:message
     message:@"可以到\"帮助与反馈\"里申诉反馈"
     cancelButtonTitle:IMYString(@"取消")
     otherButtonTitles:@[ IMYString(@"去反馈") ]
     handler:^(UIAlertController *alertController,
               NSInteger buttonIndex) {
        if (buttonIndex == 1) {
            [[IMYURIManager shareURIManager] runActionWithString:@"meiyou:///qiyu/chat?params=eyJncm91cElkIjogNDgwODE1ODc4fQ=="];
        } else {
            [self resignFirstResponder];
        }
    }];
}

- (BOOL)judgeBlocked {
    //禁言处理
    if (self.viewModel.currentUserInfo.error == 2 || self.viewModel.currentUserInfo.error == 3) {
        [self judgeBlockedWithUserInfo:self.viewModel.currentUserInfo];
        return YES;
    }
    return NO;
}
/**
 加入圈子，子类实现
 */
- (void)joinForum {
    [self.viewModel joinForum];
}

- (NSString *)inputContentFromTextView {
    NSAttributedString *attributedText =
    self.inputContentsView.textView.internalTextView.attributedText;
    if (attributedText) {
        return [IMYREmoticonManager encodeEmojiText:attributedText];
    }
    return nil;
}

// MARK: - 注册cell相关
- (void)registerCellReuseIdentifier:(id)templateCell {
    if ([templateCell isKindOfClass:[NSString class]]) {
        self.reuseIdentifier = templateCell;
        NSString *nibPath =
        [[NSBundle mainBundle] pathForResource:templateCell ofType:@"nib"];
        if (nibPath.length) {
            [self.tableView registerNib:[UINib nibWithNibName:templateCell bundle:nil]
                 forCellReuseIdentifier:templateCell];
        } else {
            [self.tableView registerClass:NSClassFromString(templateCell)
                   forCellReuseIdentifier:templateCell];
        }
        
    } else if ([templateCell isKindOfClass:[UITableViewCell class]]) {
        UITableViewCell *tableViewCell = templateCell;
        self.reuseIdentifier = tableViewCell.reuseIdentifier;
        if (self.reuseIdentifier == nil) {
            self.reuseIdentifier = NSStringFromClass([tableViewCell class]);
        }
        [self.tableView registerClass:[tableViewCell class]
               forCellReuseIdentifier:self.reuseIdentifier];
    }
}

- (void)registerCells {
    self.templateCell = @[
        @"IMYQATopicDetailCell",
        @"TTQTopicScreenPlaceholderCell", @"IMYQACommentEmptyCell",
        @"IMYQARecommendCommentCell", @"TTQNewFirstPageCell",
        @"IMYQADetailListCommonHeaderCell", @"IMYQACommentShortEmptyCell",
        @"IMYQASearchCell", @"IMYQAHomeExpertCell", @"IMYQAHomeCell"
    ];
    NSArray *array = self.templateCell;
    for (id item in array) {
        [self registerCellReuseIdentifier:item];
    }
    //添加一个空白cell
    [self registerCellReuseIdentifier:[TTQTopicScreenPlaceholderCell new]];
}

- (void)remoteControlReceivedWithEvent:(UIEvent *)event {
    if (event.type == UIEventTypeRemoteControl) {
        NSInteger order = -1;
        switch (event.subtype) {
            case UIEventSubtypeRemoteControlPause:
                order = UIEventSubtypeRemoteControlPause;
                break;
            case UIEventSubtypeRemoteControlPlay:
                order = UIEventSubtypeRemoteControlPlay;
                break;
            case UIEventSubtypeRemoteControlNextTrack:
                order = UIEventSubtypeRemoteControlNextTrack;
                break;
            case UIEventSubtypeRemoteControlPreviousTrack:
                order = UIEventSubtypeRemoteControlPreviousTrack;
                break;
            case UIEventSubtypeRemoteControlTogglePlayPause:
                order = UIEventSubtypeRemoteControlTogglePlayPause;
                break;
            default:
                order = -1;
                break;
        }
        NSDictionary *orderDict = @{ @"order" : @(order) };
        [[NSNotificationCenter defaultCenter]
         postNotificationName:@"kAppDidReceiveRemoteControlNotification"
         object:nil
         userInfo:orderDict];
    }
}

#pragma - mark 评论cell长按弹窗 复制 举报
- (void)longPressAction:
(UILongPressGestureRecognizer *)longPressGestureRecognizer {
    if (longPressGestureRecognizer.state != UIGestureRecognizerStateBegan ||
        ![self loginActicon]) {
        return;
    }
    [self createUIMenuInCell:longPressGestureRecognizer];
}

- (void)createUIMenuInCell:
(UILongPressGestureRecognizer *)longPressGestureRecognizer {
    if (![longPressGestureRecognizer.view
          isKindOfClass:IMYQATopicDetailCell.class]) {
        return;
    }
    IMYQATopicDetailCell *tableViewCell =
    (IMYQATopicDetailCell *)longPressGestureRecognizer.view;
    //    [self.inputContentsView.textView resignFirstResponder];
    //    [self.inputContentsView restPose];
    NSIndexPath *indexPath = [self.tableView indexPathForCell:tableViewCell];
    TTQCommentModel *comment = [self tableCellModelAtIndexPath:indexPath];
    NSArray *titles = @[ IMYString(@"复制"), IMYString(@"举报") ];
    NSInteger destructiveIndex = -1;
    if (indexPath == nil) {
        if (self.viewModel.topic.is_deletable) {
            //            titles = @[IMYString(@"举报"), IMYString(@"复制"),
            //            IMYString(@"删帖")]; destructiveIndex = 3;
        }
    } else {
        if ([TTQCommonHelp deleteReview]) {
            if ([comment isKindOfClass:TTQCommentModel.class]) {
                if (comment.publisher.is_owner) {
                    titles = @[ IMYString(@"复制"), IMYString(@"删除") ];
                    destructiveIndex = 3;
                } else if (comment.publisher.userID > 0) {
                    if ([[IMYPublicAppHelper shareAppHelper].userid
                         isEqualToString:@(comment.publisher.userID).stringValue]) {
                        titles = @[ IMYString(@"复制"), IMYString(@"删除") ];
                        destructiveIndex = 3;
                    }
                }
            }
        }
    }
    [tableViewCell setTextBackgroundHilighted:YES];
    
    if ([tableViewCell isKindOfClass:[UITableViewCell class]]) {
        tableViewCell.selected = false;
        [self doSelectAllCellsInSameSection:indexPath isHighlighted:NO];
    }
    
    self.longPressIndexPath = indexPath;
    self.longPressCommentModel = comment;
    
    @weakify(self);
    NSString *string = [self.longPressCommentModel.content imyr_replaceEmotionStringByString:@""  withType:IMYRReplaceEmotionStringTypeDynamic];
    NSString *content = [NSString ttq_filterHtmlTag:string];
    IMYActionSheet *sheet = [[IMYActionSheet alloc] initWithWithCancelTitle:@"取消" destructiveTitle:nil otherTitles:titles summary:content style:YES showInView:nil];
    sheet.onActionBlock = ^(NSInteger index) {
        @strongify(self);
        if (index == 0) {
            [self hightlightCell];
            return;
        }
        NSString *title = titles[index - 1];
        if ([title isEqualToString:@"复制"]) {
            [self copyActionLongPress];
        } else if ([title isEqualToString:@"举报"]) {
            [self reportActionLongPress];
        } else if ([title isEqualToString:@"删除"]) {
            [self deleteActionReplyLongPress];
        }
    };
    [sheet show];
}

- (void)addHandleUIMenuItemView {
    UIWindow *keyWindow = [UIApplication sharedApplication].keyWindow;
    if ([keyWindow.subviews containsObject:self.handleUIMenuItemView]) {
        return;
    }
    [keyWindow addSubview:self.handleUIMenuItemView];
}

- (void)removeHandleUIMenuItemView {
    [self removeHandleUIMenuItemViewFromWindow];
    [self hideUIMenuController];
}

- (void)removeHandleUIMenuItemViewFromWindow {
    UIWindow *keyWindow = [UIApplication sharedApplication].keyWindow;
    if ([keyWindow.subviews containsObject:self.handleUIMenuItemView]) {
        [self.handleUIMenuItemView removeFromSuperview];
    }
}

- (void)changeCellLongPressBackground {
    [self removeHandleUIMenuItemViewFromWindow];
    [self hightlightCell];
}

- (void)hideUIMenuController {
    UIMenuController *menu = [UIMenuController sharedMenuController];
    if (menu.isMenuVisible) {
        [menu setMenuVisible:NO animated:YES];
    }
    [self hightlightCell];
}

- (void)hightlightCell {
    if ([[self.tableView cellForRowAtIndexPath:self.longPressIndexPath]
         isKindOfClass:IMYQATopicDetailCell.class]) {
        IMYQATopicDetailCell *tableViewCell =
        [self.tableView cellForRowAtIndexPath:self.longPressIndexPath];
        [tableViewCell setTextBackgroundHilighted:NO];
    }
}

#pragma mark-- 长按菜单
- (void)deleteActionReplyLongPress {
    [self removeHandleUIMenuItemView];
    @weakify(self);
    [IMYEventHelper event:@"qzxq-schf"];
    [UIAlertController
     imy_showAlertViewWithTitle:@"要删除该回答吗？"
     message:@"删除后，该回答下的互动都将被删除"
     cancelButtonTitle:IMYString(@"取消")
     otherButtonTitles:@[ IMYString(@"删除") ]
     handler:^(UIAlertController *alertController,
               NSInteger buttonIndex) {
        @strongify(self);
        if (buttonIndex == 1) {
            if (![IMYNetState networkEnable]) {
                [UIWindow
                 imy_showTextHUD:
                     IMYString(
                               @"网络不见了，请检查网络")];
                return;
            }
            [[[[self.viewModel
                deleteCommentWithID:
                    self.longPressCommentModel
                .commentID] doNext:^(id x) {
                    @strongify(self);
                    for (TTQSegmentModel *segmentModel in self
                         .segmentModels) {
                             segmentModel
                                 .dataSource = [segmentModel.dataSource
                                                bk_select:^BOOL(TTQCommentModel *model) {
                                     if ([model
                                          isKindOfClass:
                                              [TTQCommentModel class]]) {
                                         return model.commentID !=
                                         self.longPressCommentModel
                                             .commentID;
                                     }
                                     return NO;
                                 }];
                         }
                }] deliverOnMainThread] subscribeNext:^(id x) {
                    @strongify(self);
                    [self bringScreenTipToolToFront];
                    [self.tableView reloadData];
                }];
        }
    }];
}

- (void)reportActionLongPress {
    [self removeHandleUIMenuItemView];
    [IMYEventHelper event:@"qzxq-jb"];
    //    [self.inputContentsView.textView resignFirstResponder];
    [TTQDetailHelper reportTopicAction:self.longPressCommentModel.commentID postType:2 topicId:self.viewModel.topic_id callback:nil];
}

- (void)copyActionLongPress {
    [self removeHandleUIMenuItemView];
    [IMYEventHelper event:@"qzxq-fz"];
    NSMutableString *string = [NSMutableString new];
    if (self.longPressIndexPath == nil) {
        [string
         appendString:
             [self.viewModel.topic.content
              imyr_replaceEmotionStringByString:@""
              withType:
                  IMYRReplaceEmotionStringTypeDynamic]];
        ;
    } else {
        [string
         appendString:
             [self.longPressCommentModel.content
              imyr_replaceEmotionStringByString:@""
              withType:
                  IMYRReplaceEmotionStringTypeDynamic]];
    }
    IMYSwitchModel *door =
    [[IMYDoorManager sharedManager] switchForType:@"copy_copyright"];
    NSString *words = door.dataDictionary[@"words"];
    if (words != nil) {
        [string appendString:words];
    }
    [UIPasteboard generalPasteboard].string = string;
    [self imy_showTextHUD:IMYString(@"复制成功")];
}

- (UIView *)handleUIMenuItemView {
    if (!_handleUIMenuItemView) {
        _handleUIMenuItemView = [[UIView alloc]
                                 initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT)];
        @weakify(self);
        [_handleUIMenuItemView bk_whenTapped:^{
            @strongify(self);
            [self removeHandleUIMenuItemView];
        }];
    }
    return _handleUIMenuItemView;
}

-(IMYUGCTopicDetailStatusBar *)topicStatusTopView{
    if(!_topicStatusTopView){
        _topicStatusTopView = [[IMYUGCTopicDetailStatusBar alloc] initWithFrame:CGRectMake(0, [self offsetTopForHidenNav], SCREEN_WIDTH, 36)];
        [self.view addSubview:_topicStatusTopView];
        _topicStatusTopView.layer.zPosition = FLT_MAX;
    }
    return _topicStatusTopView;
}

-(void)setupTopicSatusTopView{
    if(_topicStatusTopView != nil){
        [_topicStatusTopView removeFromSuperview];
        _topicStatusTopView = nil;
    }
    if (self.viewModel.topic.publisher.userID == [[IMYPublicAppHelper shareAppHelper].userid intValue]){
        // 1 4 9 12这四种状态需要展示已违规状态条，详见， 改为服务端下发tip字段控制
        //https://alidocs.dingtalk.com/i/nodes/20eMKjyp81R0pdzmigpd40MNWxAZB1Gv?iframeQuery=sheet_range%3Dkgqie6hm_9_12_1_1
        if(![self.viewModel checkTopicValidate]){
            // 帖子状态不正常
            [self.topicStatusTopView setStatusBarTitle:self.viewModel.topic.tip[@"title"]];
            @weakify(self);
            [self.topicStatusTopView bk_whenTapped:^{
                @strongify(self);
                IMYURI *uri = [IMYURI uriWithURIString:self.viewModel.topic.tip[@"redirect_url"]];
                [[IMYURIManager shareURIManager] runActionWithURI:uri];
            }];
            self.tableView.imy_top = [self offsetTopForHidenNav] + 36;
            self.tableView.imy_height = self.view.imy_height - self.tableView.imy_top;
        }
    }
}

#pragma mark - comment bi

- (void)addCommentBiView {
    @weakify(self);
    void (^commentViewExposureBlock)(BOOL isHot,__kindof UIView *view, NSDictionary *params) = ^(BOOL isHot,__kindof UIView *view, NSDictionary *params) {
        @strongify(self);
        NSInteger entrance = [self.fromURI.params[@"entrance"] integerValue];
        NSMutableDictionary *biParams = [@{@"event":@"dsq_nrxqy_plqll", @"entrance":@(entrance),@"position":@141,@"info_type":@12,@"info_id":@(self.viewModel.topic_id),@"index":@1} mutableCopy];
        if (isHot) {
            biParams[@"channel_id"] = @1;
        } else {
            biParams[@"channel_id"] = @2;
        }
        if (view.imyut_eventInfo.isVisible) {
            biParams[@"action"] = @3;
        } else {
            biParams[@"action"] = @4;
        }
        [IMYGAEventHelper postWithPath:@"event" params:biParams headers:nil completed:nil];
    };

    /// 为了解决切换排序时，要上报旧排序的退出，和新排序的进入，所以这里对两个排序单独做了曝光view
    self.commentBiViewHot = [[UIView alloc] init];
    self.commentBiViewHot.imy_height = 10;
    self.commentBiViewHot.imy_width = 20;
    [self.tableView addSubview:self.commentBiViewHot];
    self.commentBiViewHot.hidden = YES;
    
    self.commentBiViewHot.imyut_eventInfo.eventName = [NSString stringWithFormat:@"topicDetail_comment_%p",self];
    self.commentBiViewHot.imyut_eventInfo.type = IMYUTExposureTypeReal;
    self.commentBiViewHot.imyut_eventInfo.edgeOffset = UIEdgeInsetsMake(44, 0, SCREEN_TABBAR_SAFEBOTTOM_MARGIN + 52, 0);
    
    [self.commentBiViewHot.imyut_eventInfo setExposuredBlock:^(__kindof UIView *view, NSDictionary *params) {
        commentViewExposureBlock(YES, view, params);
    }];
    
    self.commentBiViewNew = [[UIView alloc] init];
    self.commentBiViewNew.imy_height = 10;
    self.commentBiViewNew.imy_width = 20;
    [self.tableView addSubview:self.commentBiViewNew];
    self.commentBiViewNew.hidden = YES;
    
    self.commentBiViewNew.imyut_eventInfo.eventName = [NSString stringWithFormat:@"topicDetail_comment_new_%p",self];
    self.commentBiViewNew.imyut_eventInfo.type = IMYUTExposureTypeReal;
    self.commentBiViewNew.imyut_eventInfo.edgeOffset = UIEdgeInsetsMake(44, 0, SCREEN_TABBAR_SAFEBOTTOM_MARGIN + 52, 0);
    [self.commentBiViewNew.imyut_eventInfo setExposuredBlock:^(__kindof UIView *view, NSDictionary *params) {
        commentViewExposureBlock(NO, view, params);
    }];
}

- (void)updateCommentBIView {
    if (self.viewModel.commentCount && self.captionView.state == IMYCaptionViewStateHidden) {
        self.commentBiViewHot.hidden = YES;
        self.commentBiViewNew.hidden = YES;
        UIView *currentCommentView = self.viewModel.orderByFilter == TTQOrderByFilterLastest? self.commentBiViewNew : self.commentBiViewHot;
        currentCommentView.hidden = NO;
        CGRect headerFrame = [self.tableView rectForHeaderInSection:0];
        CGFloat minTop = headerFrame.origin.y + headerFrame.size.height + 15 + 32;
        NSInteger lastSection = self.viewModel.lastCommentIndex;
        if (self.viewModel.lastCommentIndex > [self numberOfSectionsInTableView:self.tableView]) {
            // bugly 报了request for rect of invalid section (41)，可能是刷新后数据不对
            lastSection = 0;
        }
        CGRect lastSectionFrame = [self.tableView rectForSection:lastSection];
        CGFloat maxOffset = lastSectionFrame.origin.y + lastSectionFrame.size.height;

        if (self.tableView.contentOffset.y > maxOffset - 44) {
            /// 减去头部的高度44
            currentCommentView.hidden = YES;
        } else {
            currentCommentView.imy_top = MAX(minTop, self.tableView.contentOffset.y + 44);
        }
    } else {
        self.commentBiViewHot.hidden = YES;
        self.commentBiViewNew.hidden = YES;
    }
}

- (void)postCommentBiFeedsView:(NSInteger)action commentId:(NSInteger)commentId floor:(NSInteger)floor clickpos:(NSInteger)clickpos {
    NSInteger entrance = [self biFeedsEntrance];

    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    params[@"action"] = @(action);
    params[@"entrance"] = @(entrance);
    params[@"position"] = @141;
    params[@"channel_id"] = self.viewModel.orderByFilter == TTQOrderByFilterLastest ?@2:@1;
    params[@"clickpos_feedscard"] = @(clickpos);
    params[@"info_type"] = @63;
    params[@"info_id"] = @(commentId);
    params[@"floor"] = @(floor);
    params[@"data_id"] = @(self.viewModel.topic_id);
    [IMYGAEventHelper postWithPath:@"bi_feeds_view" params:params headers:nil completed:nil];
}

- (NSInteger)floorForCommentId:(NSInteger)commentId {
    TTQCommentModel *model = [self.viewModel.dataSource match:^BOOL(id  _Nonnull element) {
        if ([element isKindOfClass:TTQCommentModel.class] && [element commentID] == commentId) {
            return YES;
        }
        return NO;
    }];
    return model?[self.viewModel.dataSource indexOfObject:model]:0;
}
/// 后期BI的entrance, 首页过来是14，她她圈是2
- (NSInteger)biFeedsEntrance {
    NSInteger entrance = [self.fromURI.params[@"entrance"] integerValue];
    return entrance;
}

#pragma mark - 高亮

- (void)hideWarmReviewHighlightAtSection:(NSInteger)section data:(TTQCommentModel *)model {
    ///持有一下
    @weakify(model);
    imy_asyncMainBlock(0.5, ^{
        @strongify(model);
        /// 设置数据已展示高亮
        model.hasShowHighlight = YES;
        NSArray *cells = [self.tableView visibleCells];
        [cells bk_each:^(UITableViewCell *obj) {
            if ([obj respondsToSelector:@selector(hideWarmreviewHighlighted)]) {
                [obj performSelector:@selector(hideWarmreviewHighlighted)];
            }
        }];
    });
}

- (UIView *)bottomLineView {
    if (!_bottomLineView) {
        _bottomLineView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, 1 / [UIScreen mainScreen].scale)];
        [_bottomLineView imy_setBackgroundColorForKey:kCK_Black_J];
        _bottomLineView.alpha = 0;
    }
    return _bottomLineView;
}
@end
