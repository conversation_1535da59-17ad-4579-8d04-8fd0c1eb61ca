//
//  IMYUTExposureTrackManager.m
//  IMYUserTracer
//
//  Created by ponyo on 2018/4/12.
//

#import "IMYUTExposureTrackManager.h"
#import "IMYUTEventInfo+Private.h"
#import "IMYUTExposureStatus.h"
#import "IMYUTViewPath.h"
#import "IMYUserTrackManager.h"
#import "UITableView+IMYUserTrack.h"
#import "UIView+IMYUserTrack.h"
#import "IMYPublic.h"

static inline CGRect IMYUTRectInteger(CGRect rect) {
    return CGRectMake(floor(rect.origin.x), floor(rect.origin.y), floor(rect.size.width), floor(rect.size.height));
}

BOOL IMYUTExposureDebug = NO;
static BOOL IMYUTMainRunning = NO;
static BOOL IMYUTDidEnterBackground = NO;

@interface IMYUTExposureTrackManager () {
    NSInteger _interval;
    BOOL _isRunning;
    BOOL _shouldRunning;
    CFTimeInterval _lastRunningTime;
    NSMutableSet<UIView *> *_listenViews;
    NSMutableSet<UIView *> *_tabBars;
    NSMutableSet<UIView *> *_navigationBars;
    NSInteger _keyboardHeight;
}
@end

@implementation IMYUTExposureTrackManager

IMY_KYLIN_FUNC_LAUNCHED_ASYNC {
    IMYUTMainRunning = YES;
}

+ (void)setDebug:(BOOL)debug {
#ifdef DEBUG
    IMYUTExposureDebug = debug;
#endif
}

#ifdef DEBUG
- (void)setVisible:(BOOL)visible forView:(UIView *)view {
    if (IMYUTExposureDebug) {
        if (visible) {
            view.layer.borderColor = [UIColor redColor].CGColor;
            view.layer.borderWidth = 2;
        } else {
            view.layer.borderColor = [UIColor clearColor].CGColor;
        }
    }
}
#endif

+ (void)listenExposureForView:(UIView *)view {
    if (!view) {
        return;
    }
    dispatch_async(dispatch_get_main_queue(), ^{
        // 在每个 view 要监听时，都启动下 runloop，保证监听timer的开启
        [[self sharedInstance] _listenExposureForView:view];
        [[self sharedInstance] runloop];
    });
}

+ (void)runloop {
    [[self sharedInstance] runloop];
}

+ (void)runloopForView:(UIView *)view {
    if (!view) {
        return;
    }
    dispatch_async(dispatch_get_main_queue(), ^{
        // 在每个 view 要监听时，都启动下 runloop，保证监听timer的开启
        [[self sharedInstance] runloopForView:view];
        [[self sharedInstance] runloop];
    });
}

+ (void)forceStopVisibleForView:(UIView *)view {
    [[self sharedInstance] forceStopVisibleForView:view];
}

+ (void)fixWindowBoxWithXXBar:(UIView *)xxBar {
    [[self sharedInstance] fixWindowBoxWithXXBar:xxBar];
}

+ (instancetype)sharedInstance {
    static id instance;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[self alloc] init];
    });
    return instance;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        _listenViews = [NSMutableSet set];
        _tabBars = [NSMutableSet set];
        _navigationBars = [NSMutableSet set];
        if (IMYSystem.iPhoneVersion > 12) {
            // 12 以上机型使用 100ms 间隔
            _interval = 100;
        } else if (IMYSystem.iPhoneVersion > 10) {
            // xs 以上机型使用 150ms 间隔
            _interval = 150;
        } else {
            // 低于Xs的机型使用 200ms 间隔
            _interval = 200;
        }
        
        // 退到后台, 所有控件不可见
        [[NSNotificationCenter defaultCenter] addObserverForName:UIApplicationDidEnterBackgroundNotification
                                                          object:nil
                                                           queue:nil
                                                      usingBlock:^(NSNotification *_Nonnull note) {
            IMYUTDidEnterBackground = YES;
            // 业务会在 enter background 通知做 处理数据，所以不能立即停止曝光
            [self runloop];
        }];
        // 遇到闪退 or 用户杀死App
        [[NSNotificationCenter defaultCenter] addObserverForName:IMYGABeginApplicationHoldRunning
                                                          object:nil
                                                           queue:nil
                                                      usingBlock:^(NSNotification *_Nonnull note) {
            IMYUTDidEnterBackground = YES;
            [self _runloopAction];
        }];
        // 从后台启动的时候，自动触发检测
        [[NSNotificationCenter defaultCenter] addObserverForName:UIApplicationDidBecomeActiveNotification
                                                          object:nil
                                                           queue:nil
                                                      usingBlock:^(NSNotification *_Nonnull note) {
            IMYUTDidEnterBackground = NO;
            IMYUTMainRunning = YES;
            [self runloop];
        }];
        
        // 监听键盘弹起事件，被遮挡的范围不算曝光
        [[NSNotificationCenter defaultCenter] addObserverForName:UIKeyboardWillChangeFrameNotification object:nil queue:nil usingBlock:^(NSNotification * _Nonnull note) {
            CGRect keyboardFrame = [note.userInfo[UIKeyboardFrameEndUserInfoKey] CGRectValue];
            if (keyboardFrame.origin.y >= SCREEN_HEIGHT - 1) {
                self->_keyboardHeight = 0;
            } else {
                self->_keyboardHeight = keyboardFrame.size.height;
            }
        }];
    }
    return self;
}

- (BOOL)exposureViewFinished:(UIView *)view {
    if (!view) {
        return YES;
    }
    IMYUTEventInfo *eventInfo = view.imyut_eventInfo;
    NSString *eventName = eventInfo.eventName;
    if (!eventName) {
        return YES;
    }
    BOOL canExposure = YES;
    if (IMYUTDidEnterBackground) {
        // App 处于后台模式，都不曝光
        canExposure = NO;
    }
    // 外部可设置是否允许曝光
    if (canExposure && eventInfo.shouldExposureDetectingBlock) {
        canExposure = eventInfo.shouldExposureDetectingBlock(view);
    }
    // 获取所属容器 VC
    IMYUTBindingData *bindingData = view.imyut_bindingData;
    UIViewController<IMYViewControllerProtocol> *showInPage = (id)bindingData.viewController;
    // 获取所属视图控制器
    UIViewController<IMYViewControllerProtocol> *parentPage = (id)bindingData.insideVC;
    if (!parentPage) {
        parentPage = showInPage;
    }
    /// parent vc 是否支持 isViewActived 方法
    if (canExposure && [parentPage respondsToSelector:@selector(isViewActived)]) {
        /// VC 还处于不活跃状态，则不进行曝光
        if (!parentPage.isViewActived) {
            canExposure = NO;
        }
    }
    // 获取父视图 曝光信息，并判断是否能曝光
    IMYUTExposureStatus *pageStatus = [parentPage imyut_exposureStatus];
    IMYUTExposureStatus *showInPageStatus = [showInPage imyut_exposureStatus];
    // 必须所属 VC 可曝光，并且 大VC 也允许曝光
    if (canExposure && (!showInPageStatus.isActived || !pageStatus.isActived)) {
        canExposure = NO;
    }
    /// 当有 presentedViewController 的时候，不能算曝光，自己被盖住了
    if (canExposure && parentPage.presentedViewController.isViewLoaded) {
        canExposure = NO;
    }

    BOOL isFinished = NO;
    const BOOL enableSDKUpload = !showInPageStatus.disableSDKUpload && !pageStatus.disableSDKUpload;
    
    const IMYUTExposureType exposureType = eventInfo.type;
    NSMutableSet *exposuredIds = pageStatus.exposuredIds;
    NSMutableSet *showingIds = pageStatus.showingIds;
    NSMutableSet *preloadIds = pageStatus.preloadIds;
    
    // 只要求曝光一次
    BOOL isUnique = (exposureType == IMYUTExposureTypeUnique || exposureType == IMYUTExposureTypeTimeAndUnique || exposureType == IMYUTExposureTypeRealAndUnique);
    // 是否处于已曝光状态
    BOOL isVisibled = NO;
    // 无相关block，则当做已预加载 or 已预加载
    BOOL isPreloaded = !eventInfo.preloadBlock || [preloadIds containsObject:eventName];
    
    // 判断是否结束 曝光检测
    if (isUnique) {
        isVisibled = [exposuredIds containsObject:eventName];
        if (isVisibled) {
#ifdef DEBUG
            [self setVisible:YES forView:view];
#endif
            // 已预加载过，可直接返回，跳过后续的 预加载阈值时长 判断
            if (isPreloaded) {
                return YES;
            }
        }
    }

    // 判断 View 是否在可见区域内，这边采用所属VC 来进行校验
    if (canExposure && [self isVisibleForView:view eventInfo:eventInfo parentPage:parentPage pageStatus:pageStatus]) {
#ifdef DEBUG
        [self setVisible:YES forView:view];
#endif
        // 进入曝光模式（不会重复进入曝光）
        [eventInfo beginVisible];
        
        // 预加载回调逻辑
        if (!isPreloaded) {
            [eventInfo resumeVisible];
            if (eventInfo.time > eventInfo.preloadAwaitTime) {
                isPreloaded = YES;
                [preloadIds addObject:eventName];
                // 超过预加载阈值，进行回调
                if (eventInfo.preloadBlock) {
                    eventInfo.preloadBlock(view, nil);
                }
            }
        }
        
        // 曝光回调逻辑
        if (isUnique && isVisibled) {
            // 处于已曝光状态，啥事也不干
        } else if (exposureType == IMYUTExposureTypeUnique) {
            // 如果是标准统计，只需曝光一次即可
            // 预加载完成 && 曝光完成 则不在监听
            isFinished = YES && isPreloaded;
            [exposuredIds addObject:eventName];
            // 是否能被清除
            if (eventInfo.ableToClean) {
                [pageStatus.ableToCleanIds addObject:eventName];
            }
            // 上报统计
            [self trackExposureEventWithView:view
                                  showInPage:showInPage
                                   eventInfo:eventInfo
                                  pageStatus:pageStatus
                                      upload:YES && enableSDKUpload];
        } else {
            // 不是标准模式 && 不可见 => 可见
            if (![showingIds containsObject:eventName]) {
                [showingIds addObject:eventName];
                if (exposureType == IMYUTExposureTypeRepeat ||
                    exposureType == IMYUTExposureTypeReal ||
                    exposureType == IMYUTExposureTypeRealAndUnique) {
                    // 只有 Repeat 才要上报到 无痕埋点，如果是 Real 模式，不上报到无痕埋点，但是要给外部回调
                    BOOL upload = (exposureType == IMYUTExposureTypeRepeat);
                    [self trackExposureEventWithView:view
                                          showInPage:showInPage
                                           eventInfo:eventInfo
                                          pageStatus:pageStatus
                                              upload:upload && enableSDKUpload];
                }
            }
        }
    } else {
        // 判断是否达到 不可见的 阈值时间
        if (![eventInfo shouldEndVisible]) {
            return NO;
        }
        // 由业务判断是否允许结束曝光
        if (eventInfo.shouldUnexposedBlock &&
            eventInfo.shouldUnexposedBlock(view) == NO) {
            return NO;
        }
        // 无VC 的特殊情况，
        BOOL isBail = (!showingIds && eventInfo.visible);
        // 可见 => 不可见
        [eventInfo endVisible];
        // 曝光回调逻辑
        if ([showingIds containsObject:eventName] || isBail) {
            [showingIds removeObject:eventName];
            if (exposureType == IMYUTExposureTypeTimeAndUnique ||
                exposureType == IMYUTExposureTypeRealAndUnique) {
                // 预加载完成 && 曝光完成 则不在监听
                isFinished = YES && isPreloaded;
                [exposuredIds addObject:eventName];
                // 是否能被清除
                if (eventInfo.ableToClean) {
                    [pageStatus.ableToCleanIds addObject:eventName];
                }
            }
            if (exposureType != IMYUTExposureTypeUnique &&
                exposureType != IMYUTExposureTypeRepeat) {
                // 上报统计
                [self trackExposureEventWithView:view
                                      showInPage:showInPage
                                       eventInfo:eventInfo
                                      pageStatus:pageStatus
                                          upload:YES && enableSDKUpload];
            }
        }
        // 如果不在 window 上，则不继续监听
        if (!view.window) {
            isFinished = YES;
        }
#ifdef DEBUG
        [self setVisible:NO forView:view];
        if (eventInfo.cannotExposesBlock) {
            eventInfo.cannotExposesBlock(view, eventInfo.eventValue);
        }
#endif
    }
    return isFinished;
}

+ (BOOL)isHiddenForView:(UIView * const)view {
    BOOL isHidden = NO;
    UIView *superView = view;
    NSUInteger index = 0;
    // 对所有父视图进行 hidden 判断，只向上递归20个层级
    while (superView != nil && index < 20) {
        if (superView.isHidden || superView.alpha < 0.01) {
            isHidden = YES;
            break;
        } else {
            superView = superView.superview;
            index += 1;
        }
    }
    return isHidden;
}

// 返回当前视图是否可见，超过多少百分比 才算曝光 [0,1]
- (BOOL)isVisibleForView:(UIView *)view
               eventInfo:(IMYUTEventInfo *)eventInfo
              parentPage:(UIViewController *)parentPage
              pageStatus:(IMYUTExposureStatus *)pageStatus {
    UIWindow * const inWindow = view.window;
    if (!inWindow || [IMYUTExposureTrackManager isHiddenForView:view]) {
        return NO;
    }

    Class adWindowClass = [IMYCoverWindow class];
    NSArray *allWindows = [UIApplication sharedApplication].windows;
    for (UIWindow *otherWindow in allWindows) {
        if (otherWindow == inWindow) {
            continue;
        }
        // 正在显示广告window
        if ([otherWindow isKindOfClass:adWindowClass] && !otherWindow.isHidden && otherWindow.alpha > 0.01) {
            return NO;
        }
    }
    
    // 是否中线曝光模式
    if (IMYUTExposureShowRadiusTypeScreenMiddle == eventInfo.showRadiusType) {
        // 计算控件在window上的坐标
        CGRect rectInWindow = IMYUTRectInteger([view convertRect:view.bounds toView:inWindow]);
        CGRect windowBox = IMYUTRectInteger(inWindow.bounds);

        // 计算曝光区域
        NSInteger showBottom = CGRectGetMaxY(windowBox) / 2;
        NSInteger showTop = showBottom;
        NSInteger showLeft = CGRectGetMinX(windowBox);
        NSInteger showRight = CGRectGetMaxX(windowBox);
        
        // 判断是否要获取 VC 和 window 的交集，作为显示空间，需要一定的性能损耗
        if (pageStatus.showBoundInVC && parentPage.isViewLoaded && parentPage.view.window == inWindow) {
            CGRect parentViewBox = IMYUTRectInteger([parentPage.view convertRect:parentPage.view.bounds toView:inWindow]);
            NSInteger parentLeft = CGRectGetMinX(parentViewBox);
            NSInteger parentRight = CGRectGetMaxX(parentViewBox);
            if (parentLeft >= showRight || parentRight <= showLeft) {
                // 无交集，显示边界设置为空
                showLeft = showTop = NSIntegerMax;
                showRight = showBottom = NSIntegerMin;
            } else {
                // 中线曝光的策略，只对 左右边距 做交集
                if (parentLeft > showLeft && parentLeft < showRight) {
                    showLeft = parentLeft;
                }
                if (parentRight > showLeft && parentRight < showRight) {
                    showRight = parentRight;
                }
            }
        }
#ifdef DEBUG
        // 更新计算后的曝光范围
        [eventInfo updateWindowRect:rectInWindow
                           viewRect:CGRectMake(showLeft, showTop, showRight, showBottom)];
#endif
        // 跟屏幕中线相交，才算曝光
        BOOL isVisibled = (CGRectGetMinY(rectInWindow) <= showBottom &&
                           CGRectGetMaxY(rectInWindow) > showTop &&
                           CGRectGetMaxX(rectInWindow) > showLeft &&
                           CGRectGetMinX(rectInWindow) < showRight);
        return isVisibled;
    } else {
        // 修复 window 上的 UITabBar 和 UINavigationBar 的覆盖问题，如果是 present 上来的，也不检测相关bar
        const BOOL fixXXBar = !([parentPage isKindOfClass:[UITabBarController class]] ||
                                [parentPage isKindOfClass:[UINavigationController class]] ||
                                parentPage.presentingViewController != nil);
        NSInteger navBarHeight = 0;
        NSInteger tabBarHeight = 0;
        if (fixXXBar) {
            for (UIView *bar in _navigationBars.objectEnumerator) {
                if (bar.window == inWindow && !bar.hidden && bar.alpha > 0.01) {
                    navBarHeight = bar.frame.size.height;
                    break;
                }
            }
            for (UIView *bar in _tabBars.objectEnumerator) {
                if (bar.window == inWindow && !bar.hidden && bar.alpha > 0.01) {
                    tabBarHeight = bar.frame.size.height;
                    break;
                }
            }
        }
        
        // 修复部分添加在 TabVC、NavVC 上的 View 也会被算遮挡
        if (tabBarHeight > 0 || navBarHeight > 0) {
            IMYUTBindingData *bindingData = view.imyut_bindingData;
            UIViewController *realInsideVC = [bindingData realInsideVC];
            if (!realInsideVC ||
                [realInsideVC isKindOfClass:[UITabBarController class]] ||
                [realInsideVC isKindOfClass:[UINavigationController class]]) {
                tabBarHeight = 0;
                navBarHeight = 0;
            }
        }
        
        // 如果有弹键盘，则用键盘高度 代替 tabBarHeight
        if (pageStatus.enableKeyboardCoverOut && _keyboardHeight > 0) {
            tabBarHeight = _keyboardHeight;
        }
        
        // 计算控件在window上的坐标
        CGRect rectInWindow = IMYUTRectInteger([view convertRect:view.bounds toView:inWindow]);
        CGRect windowBox = IMYUTRectInteger(inWindow.bounds);
        
        CGFloat showRadius = eventInfo.showRadius;
        if (IMYUTExposureShowRadiusTypeIndependent == eventInfo.showRadiusType) {
            // 使用独立的曝光比例
            if (eventInfo.isVisible) {
                showRadius = eventInfo.showEndRadius;
            } else {
                showRadius = eventInfo.showBeginRadius;
            }
        }
        
        UIEdgeInsets edgeOffset = eventInfo.edgeOffset;
        
        NSInteger widthDiff = rectInWindow.size.width * showRadius;
        NSInteger heightDiff = rectInWindow.size.height * showRadius;
        
        /// 修正可见区域
        NSInteger showBottom = CGRectGetMaxY(windowBox) - heightDiff - tabBarHeight - edgeOffset.bottom;
        NSInteger showTop = CGRectGetMinY(windowBox) + heightDiff + navBarHeight + edgeOffset.top;
        NSInteger showLeft = CGRectGetMinX(windowBox) + widthDiff + edgeOffset.left;
        NSInteger showRight = CGRectGetMaxX(windowBox) - widthDiff - edgeOffset.right;
        
        /// 判断是否要获取 VC 和 window 的交集，作为显示空间，需要一定的性能损耗
        if (pageStatus.showBoundInVC && parentPage.isViewLoaded && parentPage.view.window == inWindow) {
            CGRect parentViewBox = IMYUTRectInteger([parentPage.view convertRect:parentPage.view.bounds toView:inWindow]);
            NSInteger parentLeft = CGRectGetMinX(parentViewBox);
            NSInteger parentRight = CGRectGetMaxX(parentViewBox);
            NSInteger parentTop = CGRectGetMinY(parentViewBox);
            NSInteger parentBottom = CGRectGetMaxY(parentViewBox);
            if (parentLeft >= showRight ||
                parentRight <= showLeft ||
                parentTop >= showBottom ||
                parentBottom <= showTop) {
                // 无交集，显示边界设置为空
                showLeft = showTop = NSIntegerMax;
                showRight = showBottom = NSIntegerMin;
            } else {
                if (parentLeft > showLeft && parentLeft < showRight) {
                    showLeft = parentLeft;
                }
                if (parentRight > showLeft && parentRight < showRight) {
                    showRight = parentRight;
                }
                if (parentTop > showTop && parentTop < showBottom) {
                    showTop = parentTop;
                }
                if (parentBottom > showTop && parentBottom < showBottom) {
                    showBottom = parentBottom;
                }
            }
        }
#ifdef DEBUG
        // 更新计算后的曝光范围
        [eventInfo updateWindowRect:rectInWindow
                           viewRect:CGRectMake(showLeft, showTop, showRight, showBottom)];
#endif
        // 在可见区域内
        BOOL isVisibled = (CGRectGetMinY(rectInWindow) <= showBottom &&
                           CGRectGetMaxY(rectInWindow) >= showTop &&
                           CGRectGetMaxX(rectInWindow) >= showLeft &&
                           CGRectGetMinX(rectInWindow) <= showRight);
        return isVisibled;
    }
}

// 修复Window可视化窗口坐标
- (void)fixWindowBoxWithXXBar:(UIView *)xxBar {
    if ([xxBar isKindOfClass:UITabBar.class]) {
        [_tabBars removeObject:xxBar];
        if (xxBar.window) {
            [_tabBars addObject:xxBar];
        }
    } else if ([xxBar isKindOfClass:UINavigationBar.class]) {
        [_navigationBars removeObject:xxBar];
        if (xxBar.window) {
            [_navigationBars addObject:xxBar];
        }
    }
}

/// 中途更换 eventName 等情况，需要强制终止曝光
- (void)forceStopVisibleForView:(UIView *)view {
    IMYUTEventInfo *eventInfo = view.imyut_eventInfo;
    NSString *eventName = eventInfo.eventName;
    if (!eventName) {
        return;
    }
    // 获取所属容器 VC
    IMYUTBindingData *bindingData = view.imyut_bindingData;
    UIViewController<IMYViewControllerProtocol> *showInPage = (id)bindingData.viewController;
    // 获取所属视图控制器
    UIViewController<IMYViewControllerProtocol> *parentPage = (id)bindingData.insideVC;
    if (!parentPage) {
        parentPage = showInPage;
    }
    
    // 获取父视图 曝光信息
    IMYUTExposureStatus *pageStatus = [parentPage imyut_exposureStatus];
    IMYUTExposureStatus *showInPageStatus = [showInPage imyut_exposureStatus];

    const IMYUTExposureType exposureType = eventInfo.type;
    NSMutableSet *exposuredIds = pageStatus.exposuredIds;
    NSMutableSet *showingIds = pageStatus.showingIds;
    const BOOL enableSDKUpload = !showInPageStatus.disableSDKUpload && !pageStatus.disableSDKUpload;

    // 无VC 的特殊情况，
    BOOL isBail = (!showingIds && eventInfo.visible);
    // 可见 => 不可见
    [eventInfo endVisible];
    // 曝光回调逻辑
    if ([showingIds containsObject:eventName] || isBail) {
        [showingIds removeObject:eventName];
        if (exposureType == IMYUTExposureTypeTimeAndUnique ||
            exposureType == IMYUTExposureTypeRealAndUnique) {
            // 终止监听 & 曝光完成
            [exposuredIds addObject:eventName];
            if (eventInfo.ableToClean) {
                [pageStatus.ableToCleanIds addObject:eventName];
            }
        }
        if (exposureType != IMYUTExposureTypeUnique &&
            exposureType != IMYUTExposureTypeRepeat) {
            // 上报统计
            [self trackExposureEventWithView:view
                                  showInPage:showInPage
                                   eventInfo:eventInfo
                                  pageStatus:pageStatus
                                      upload:YES && enableSDKUpload];
        }
#ifdef DEBUG
        [self setVisible:NO forView:view];
        if (eventInfo.cannotExposesBlock) {
            eventInfo.cannotExposesBlock(view, eventInfo.eventValue);
        }
#endif
    }
}

- (void)trackExposureEventWithView:(UIView *)view
                        showInPage:(UIViewController *)showInPage
                         eventInfo:(IMYUTEventInfo *)eventInfo
                        pageStatus:(IMYUTExposureStatus *)pageStatus
                            upload:(BOOL)upload {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    params[@"pageName"] = ((id<IMYGAEventProtocol>)showInPage).ga_pageName ?: view.imyut_bindingData.pageName;
    params[@"listIndex"] = [view imyut_cellIndexString];
    params[@"eventName"] = eventInfo.eventName;
    params[@"detailInfo"] = eventInfo.eventValue ?: @{};

    // 服务端做数据过滤
    uint64_t nowTime = IMYDateTimeIntervalSince1970() * 1000;
    params[@"event_time"] = @(nowTime);
    params[@"page_time"] = @(pageStatus.pageTime);

    // 曝光时长
    if (eventInfo.type != IMYUTExposureTypeUnique && eventInfo.type != IMYUTExposureTypeRepeat) {
        params[@"time"] = @{ @"begin": @(eventInfo.begin),
                             @"end": @(eventInfo.end),
                             @"diff": @(eventInfo.time) };
    }
    NSDictionary *exposureDict = [params copy];
    if (eventInfo.exposuredBlock) {
        eventInfo.exposuredBlock(view, exposureDict);
        // 有曝光回调，并且没有开启并发上报，则不进行 框架内的 whmd-bg 上报
        if (upload && !eventInfo.forcePostEvent) {
            upload = NO;
        }
    }
    if (upload) {
        [[IMYUserTrackManager sharedSingleton] userTracerOnExposureEvent:exposureDict];
    }
}

- (void)_listenExposureForView:(UIView *)view {
    // 还未添加到 window 上，则不进行任何条件判断
    if (!view.window) {
        return;
    }

    // 保证一个页面中，只有一个 eventName
    {
        IMYUTEventInfo *nowEventInfo = view.imyut_eventInfo;
        NSString *eventName = nowEventInfo.eventName;
        // 没有 eventName，则整个方法直接退出
        if (!eventName) {
            return;
        }
        IMYUTBindingData *bindingData = view.imyut_bindingData;
        // 获取所属视图控制器
        UIViewController<IMYViewControllerProtocol> *parentPage = (id)bindingData.insideVC;
        if (!parentPage) {
            parentPage = (id)bindingData.viewController;
        }
        IMYUTExposureStatus *pageStatus = [parentPage imyut_exposureStatus];
        IMYUTEventInfo *oldEventInfo = pageStatus.eventInfoMap[eventName];
        if (oldEventInfo != nowEventInfo && [oldEventInfo.eventName isEqualToString:eventName]) {
            // 如果旧的 eventInfo 保持着同名状态 ，则移除它的监听
            oldEventInfo.eventName = nil;
        }
        pageStatus.eventInfoMap[eventName] = nowEventInfo;
    }

    // 已加到监听列表中
    if ([_listenViews containsObject:view]) {
        return;
    }

#ifdef DEBUG
    [self setVisible:NO forView:view];
#endif

    // 尝试曝光
    BOOL isFinished = [self exposureViewFinished:view];
    if (isFinished) {
        // 已结束监听， 无需添加到列表中
        return;
    }

    [_listenViews addObject:view];
    _shouldRunning = YES;
}

- (void)runloop {
    // 主循环还不能启动，等Launch启动后，再跑
    if (!IMYUTMainRunning) {
        return;
    }
    // 没控件需要监听
    if (!_shouldRunning) {
        return;
    }
    // 更新最后一次用户操作时间
    _lastRunningTime = CACurrentMediaTime();
    if (_isRunning) {
        return;
    }
    _isRunning = YES;

    // 开始循环遍历
    [self _runloop];
}

- (void)_runloop {
    // 100 ~ 200ms 执行一次，曝光检测
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(_interval * NSEC_PER_MSEC)), dispatch_get_main_queue(), ^{
        // 以美柚首页为标准，目前耗时在1ms以内
        [self _runloopAction];
    });
}

- (void)_runloopAction {
    if (_listenViews.count == 0) {
        // 没有待监听控件，暂停循环
        _shouldRunning = NO;
        _isRunning = NO;
        return;
    }
    NSArray *allObjects = [_listenViews allObjects];
    for (UIView *view in allObjects) {
        BOOL isFinished = [self exposureViewFinished:view];
        if (isFinished) {
            // 已结束监听， 无需添加到列表中
            [_listenViews removeObject:view];
        }
    }
    double diff = CACurrentMediaTime() - _lastRunningTime;
    if (diff < 3) {
        // 继续监听，有些惯性滚动
        [self _runloop];
    } else {
        // 用户没操作3秒了，大部分滚动也停止了， 所以先暂停监听
        _isRunning = NO;
    }
}

- (void)runloopForView:(UIView *)view {
    if (view.window) {
        // 加入到 检测队列
        if (![_listenViews containsObject:view]) {
            [_listenViews addObject:view];
            _shouldRunning = YES;
        }
    } else {
        if ([_listenViews containsObject:view]) {
            [self forceStopVisibleForView:view];
        }
    }
}

@end
