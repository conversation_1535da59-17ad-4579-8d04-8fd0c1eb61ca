//
//  IMYNetworkPerformanceMetrics.h
//  IMYPublic
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface IMYNetworkPerformanceMetrics : NSObject

@property (nonatomic, strong) NSString *requestId;
@property (nonatomic, strong) NSString *url;
@property (nonatomic, strong) NSString *method;
@property (nonatomic, assign) NSTimeInterval dnsLookupDurationMs;
@property (nonatomic, assign) NSTimeInterval tcpConnectDurationMs;
@property (nonatomic, assign) NSTimeInterval sslHandshakeDurationMs;
@property (nonatomic, assign) NSTimeInterval requestSendDurationMs;
@property (nonatomic, assign) NSTimeInterval timeToFirstByteMs;
@property (nonatomic, assign) NSTimeInterval responseReceiveDurationMs;
@property (nonatomic, assign) NSTimeInterval totalRequestDurationMs;
@property (nonatomic, assign) NSInteger httpStatusCode;
@property (nonatomic, assign) NSInteger responseSizeBytes;
@property (nonatomic, strong, nullable) NSString *errorCode;
@property (nonatomic, strong, nullable) NSString *errorMessage;
@property (nonatomic, assign) NSTimeInterval timestampSec;

- (NSDictionary *)toDictionary;

@end

NS_ASSUME_NONNULL_END
