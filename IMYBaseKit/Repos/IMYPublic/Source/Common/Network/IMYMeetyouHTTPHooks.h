//
//  IMYMeetyouHTTPHooks.h
//  IMYPublic
//
//  Created by mario on 16/4/9.
//  Copyright © 2016年 meiyou. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "IMYNetworking.h"

@interface IMYMeetyouHTTPHooks : NSObject

+ (NSString *)userTokenInvalidNotification;

+ (NSString *)phoneInvalidNotification;

+ (NSString *)userTokenExpiredNotification;

///获取服务端当前时间戳（秒）
+ (RACSignal *)getCurrentTimestamp;

///是否美柚自己域名
+ (BOOL)isMeiYouSite:(NSString *)urlString;

+ (void)addMeetYouHosts:(NSArray *)hosts;

+ (void)addDefaultHeaders:(NSDictionary *)headers;

+ (NSString *)currentPageName;

+ (NSString *)currentPageSource;

+ (NSString *)currentPageHistory;

+ (NSString *)currentMainTab;

/// 当前的授权头，XDS开头为正式授权头，VDS开头为虚拟授权头
+ (NSDictionary *)currentAuthorizationMap;

+ (uint64_t)currentPageTime;

/// 获取该 url 的statinfo请求头
+ (NSDictionary *)statInfoHeadersForUrlString:(NSString *)urlString;

/// 加密statinfo请求头
+ (NSDictionary *)statInfoEncryptHeadersForInfoDict:(NSDictionary *)infoDict urlHost:(NSString *)urlHost;

/// 是否 v2 接口
+ (BOOL)isV2Buildable:(IMYHTTPBuildable *)buildable;
+ (BOOL)isV2URLWithHost:(NSString *)host andPath:(NSString *)path;

@end

@interface IMYMeetyouHTTPHooks (Hooks)

/// add hooks
+ (void)addDidFailHook:(void (^)(NSError *))didFailHook forKey:(NSString *)key;
+ (void)addDidSuccessHook:(void (^)(IMYHTTPResponse *))didSuccessHook forKey:(NSString *)key;
+ (void)addQueriesHook:(NSDictionary * (^)(NSString *urlString))quriesHook forKey:(NSString *)key;
+ (void)addHeadersHook:(NSDictionary * (^)(NSString *urlString))headersHook forKey:(NSString *)key;
+ (void)addStatInfoHook:(NSDictionary * (^)(NSString *urlString, NSDictionary *apiHeaders))statinfoHook forKey:(NSString *)key;
+ (void)addWillRequestHook:(void (^)(NSMutableURLRequest *))willRequestHook forKey:(NSString *)key;

/// default hooks
+ (NSMutableDictionary *)headersWithUrlString:(NSString *)urlString headers:(NSDictionary *)headers;
+ (NSMutableDictionary *)queriesWithUrlString:(NSString *)urlString;
+ (BOOL)gzipEncodingWithUrlString:(NSString *)urlString;
+ (void)didFailHook:(NSError *)error requestTime:(NSTimeInterval const)requestTime;
+ (void)didSuccessHook:(IMYHTTPResponse *)response requestTime:(NSTimeInterval const)requestTime;
+ (void)willRequestHook:(NSMutableURLRequest *)request;
+ (NSSet<NSString *> *)acceptableContentTypesHook:(IMYHTTPSerializerType)type;

#pragma mark - 网络性能监控方法

+ (void)addNetworkPerformanceMonitoringHook;

+ (void)handleTaskMetrics:(NSURLSessionTaskMetrics *)metrics
                     task:(NSURLSessionTask *)task
                    error:(NSError *)error NS_AVAILABLE_IOS(10_0);

@end

@interface IMYMeetyouHTTPHooks (Deprecated)

+ (void)addDidFailHook:(void (^)(NSError *))didFailHook __deprecated_msg("使用 addDidFailHook:forKey:");
+ (void)addDidSuccessHook:(void (^)(IMYHTTPResponse *))didSuccessHook __deprecated_msg("使用 addDidSuccessHook:forKey:");
+ (void)addQueriesHook:(NSDictionary * (^)(NSString *urlString))quriesHook __deprecated_msg("使用 addQueriesHook:forKey:");
+ (void)addHeadersHook:(NSDictionary * (^)(NSString *urlString))headersHook __deprecated_msg("使用 addHeadersHook:forKey:");
+ (void)addWillRequestHook:(void (^)(NSMutableURLRequest *))willRequestHook __deprecated_msg("使用 addWillRequestHook:forKey:");

@end

@interface IMYMeetyouHTTPHooks (Compatibility)

+ (void)addRequestHeadersToRequest:(NSURLRequest *)urlRequest;

@end
