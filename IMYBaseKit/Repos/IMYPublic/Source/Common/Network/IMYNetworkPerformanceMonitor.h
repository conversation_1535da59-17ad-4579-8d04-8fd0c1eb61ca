//
//  IMYNetworkPerformanceMonitor.h
//  IMYPublic
//

#import <Foundation/Foundation.h>
#import "IMYNetworkPerformanceMetrics.h"

NS_ASSUME_NONNULL_BEGIN

@interface IMYNetworkPerformanceMonitor : NSObject

+ (IMYNetworkPerformanceMetrics *)extractMetricsFromTaskMetrics:(NSURLSessionTaskMetrics *)metrics
                                                            task:(NSURLSessionTask *)task
                                                           error:(nullable NSError *)error NS_AVAILABLE_IOS(10_0);

+ (void)logPerformanceMetrics:(IMYNetworkPerformanceMetrics *)metrics;

+ (void)reportPerformanceMetrics:(IMYNetworkPerformanceMetrics *)metrics;

@end

NS_ASSUME_NONNULL_END
