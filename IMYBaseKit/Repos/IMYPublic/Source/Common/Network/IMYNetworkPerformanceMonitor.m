//
//  IMYNetworkPerformanceMonitor.m
//  IMYPublic
//

#import "IMYNetworkPerformanceMonitor.h"
#import <IMYBaseKit/IMYPublic.h>

@implementation IMYNetworkPerformanceMonitor

+ (IMYNetworkPerformanceMetrics *)extractMetricsFromTaskMetrics:(NSURLSessionTaskMetrics *)metrics
                                                            task:(NSURLSessionTask *)task
                                                           error:(NSError *)error {
    IMYNetworkPerformanceMetrics *performanceMetrics = [[IMYNetworkPerformanceMetrics alloc] init];

    performanceMetrics.url = task.originalRequest.URL.absoluteString;
    performanceMetrics.method = task.originalRequest.HTTPMethod;

    if (error) {
        performanceMetrics.errorCode = [@(error.code) stringValue];
        performanceMetrics.errorMessage = error.localizedDescription;
    }

    if ([task.response isKindOfClass:[NSHTTPURLResponse class]]) {
        NSHTTPURLResponse *httpResponse = (NSHTTPURLResponse *)task.response;
        performanceMetrics.httpStatusCode = httpResponse.statusCode;
    }

    if (task.response.expectedContentLength > 0) {
        performanceMetrics.responseSizeBytes = task.response.expectedContentLength;
    }

    for (NSURLSessionTaskTransactionMetrics *transMetric in metrics.transactionMetrics) {
        if (transMetric.domainLookupStartDate && transMetric.domainLookupEndDate) {
            performanceMetrics.dnsLookupDurationMs = [transMetric.domainLookupEndDate timeIntervalSinceDate:transMetric.domainLookupStartDate] * 1000;
        }

        if (transMetric.connectStartDate && transMetric.connectEndDate) {
            performanceMetrics.tcpConnectDurationMs = [transMetric.connectEndDate timeIntervalSinceDate:transMetric.connectStartDate] * 1000;
        }

        if (transMetric.secureConnectionStartDate && transMetric.secureConnectionEndDate) {
            performanceMetrics.sslHandshakeDurationMs = [transMetric.secureConnectionEndDate timeIntervalSinceDate:transMetric.secureConnectionStartDate] * 1000;
        }

        if (transMetric.requestStartDate && transMetric.requestEndDate) {
            performanceMetrics.requestSendDurationMs = [transMetric.requestEndDate timeIntervalSinceDate:transMetric.requestStartDate] * 1000;
        }

        if (transMetric.requestStartDate && transMetric.responseStartDate) {
            performanceMetrics.timeToFirstByteMs = [transMetric.responseStartDate timeIntervalSinceDate:transMetric.requestStartDate] * 1000;
        }

        if (transMetric.responseStartDate && transMetric.responseEndDate) {
            performanceMetrics.responseReceiveDurationMs = [transMetric.responseEndDate timeIntervalSinceDate:transMetric.responseStartDate] * 1000;
        }

        if (transMetric.fetchStartDate && transMetric.responseEndDate) {
            performanceMetrics.totalRequestDurationMs = [transMetric.responseEndDate timeIntervalSinceDate:transMetric.fetchStartDate] * 1000;
        }

        break;
    }

    return performanceMetrics;
}

+ (void)logPerformanceMetrics:(IMYNetworkPerformanceMetrics *)metrics {
    NSLog(@"Network Performance - ID: %@", metrics.requestId);
    NSLog(@"URL: %@", metrics.url);
    NSLog(@"Method: %@", metrics.method);
    NSLog(@"Status: %ld", (long)metrics.httpStatusCode);
    NSLog(@"DNS: %.3fms, TCP: %.3fms, SSL: %.3fms, TTFB: %.3fms, Total: %.3fms",
          metrics.dnsLookupDurationMs,
          metrics.tcpConnectDurationMs,
          metrics.sslHandshakeDurationMs,
          metrics.timeToFirstByteMs,
          metrics.totalRequestDurationMs);
    
}

+ (void)reportPerformanceMetrics:(IMYNetworkPerformanceMetrics *)metrics {
    NSDictionary *reportData = [metrics toDictionary];
}

@end