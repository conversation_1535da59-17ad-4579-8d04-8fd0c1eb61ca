//
//  IMYNetworkPerformanceMetrics.m
//  IMYPublic
//

#import "IMYNetworkPerformanceMetrics.h"

@implementation IMYNetworkPerformanceMetrics

- (instancetype)init {
    if (self = [super init]) {
        _requestId = [[NSUUID UUID] UUIDString];
        _timestampSec = [[NSDate date] timeIntervalSince1970];
        _url = @"";
        _method = @"";
        _errorCode = @"";
        _errorMessage = @"";
        _dnsLookupDurationMs = 0;
        _tcpConnectDurationMs = 0;
        _sslHandshakeDurationMs = 0;
        _requestSendDurationMs = 0;
        _timeToFirstByteMs = 0;
        _responseReceiveDurationMs = 0;
        _totalRequestDurationMs = 0;
        _httpStatusCode = 0;
        _responseSizeBytes = 0;
    }
    return self;
}

- (NSDictionary *)toDictionary {
    return @{
        @"request_id": self.requestId ?: @"",
        @"url": self.url ?: @"",
        @"method": self.method ?: @"",
        @"timings_ms": @{
            @"dns_lookup_duration": @(self.dnsLookupDurationMs),
            @"tcp_connect_duration": @(self.tcpConnectDurationMs),
            @"ssl_handshake_duration": @(self.sslHandshakeDurationMs),
            @"request_send_duration": @(self.requestSendDurationMs),
            @"time_to_first_byte": @(self.timeToFirstByteMs),
            @"response_receive_duration": @(self.responseReceiveDurationMs),
            @"total_request_duration": @(self.totalRequestDurationMs)
        },

        @"http_status_code": @(self.httpStatusCode),
        @"error_code": self.errorCode ?: @"",
        @"error_message": self.errorMessage ?: @"",
        @"response_size_bytes": @(self.responseSizeBytes),
        @"timestamp_sec": @(self.timestampSec)
    };
}



- (NSString *)description {
    return [NSString stringWithFormat:@"<%@: %p> requestId=%@ url=%@ method=%@ totalDuration=%.3fms httpStatus=%ld",
            NSStringFromClass([self class]),
            self,
            self.requestId,
            self.url,
            self.method,
            self.totalRequestDurationMs,
            (long)self.httpStatusCode];
}

@end