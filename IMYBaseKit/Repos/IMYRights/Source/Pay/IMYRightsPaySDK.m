//
//  IMYRightsPaySDK.m
//  IMYBaseKit
//
//  Created by ljh on 2024/1/30.
//

#import "IMYRightsPaySDK.h"
#import "IMYPublic.h"
#import <StoreKit/StoreKit.h>
#import <IOC-Protocols/IOCAppInfo.h>

static NSString * const kAllProductIDKey = @"rights_iap_product_ids";

@interface IMYRightsPaySDK () <SKProductsRequestDelegate,SKPaymentTransactionObserver>

/// 所有历史支付的 IAP Product ID
@property (nonatomic, strong) NSMutableSet *allPayProductIdSet;

/// 已查询到的所有商品信息
@property (atomic, copy) NSArray<SKProduct *> *allProducts;

@property (atomic, copy) NSArray<NSString *> *productRequestIds;
@property (atomic, copy) void(^productsCompletedBlock)(NSArray<SKProduct *> *);

/// 支付完成回调
@property (atomic, copy) void(^payCompletedBlock)(NSDictionary *result, NSError *error);
/// 支付过程回调
@property (atomic, copy) void(^payProgressBlock)(NSDictionary *result, NSInteger state);

/// 是否处于恢复购买中
@property (atomic, assign) BOOL isRestorePaying;
/// 恢复购买是否执行验证过
@property (atomic, assign) BOOL isRestoreCalled;
/// 是否执行过订单校验
@property (atomic, assign) BOOL isReceiptVerified;

/// 支付信息
@property (atomic, copy) NSString *planId;
@property (atomic, copy) NSString *priceId;
@property (atomic, copy) NSString *renewalId;
@property (atomic, copy) NSDictionary *appendParams;

/// 订单信息
@property (atomic, copy) NSString *myTransactionId;
@property (atomic, copy) NSString *myUUID;
@property (atomic, copy) NSString *myProductId;

/// 当前创建订单的信息
@property (atomic, copy) NSDictionary *currentOrderData;
@property (atomic, copy) NSDictionary *currentVerifyData;

/// 正在验证的ID
@property (atomic, assign) BOOL iapReceiptVerifying;

/// 重试时间
@property (atomic, assign) NSInteger retryOutBoxDelay;
@property (atomic, assign) NSInteger retryOutBoxCount;

/// 最近一次的校验信息
@property (atomic, copy) NSDictionary *lastVerifyInfos;

/// 最近一次的loading text
@property (atomic, copy) NSString *lastShowToastText;

/// 优惠劵
@property (atomic, copy) NSDictionary *offer_sign;

@end

@implementation IMYRightsPaySDK

+ (instancetype)sharedInstance {
    static id instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [self new];
    });
    return instance;
}

+ (NSString *)payVerifyCompletedNotification {
    return @"meetyou.righs.verify.completed";
}

- (instancetype)init {
    self = [super init];
    if (self) {
        // 发送存储的支付结果
        [[[RACSignal merge:@[[IMYPublicAppHelper shareAppHelper].useridChangedSignal, IMYNetState.networkChangedSignal]] throttle:1] subscribeNext:^(id  _Nullable x) {
            [self outBoxSendVerifyData];
        }];
        
        NSArray *productIds = [[IMYKV defaultKV] arrayForKey:kAllProductIDKey];
        self.allPayProductIdSet = [NSMutableSet setWithArray:productIds];
        
        [[SKPaymentQueue defaultQueue] addTransactionObserver:self];
    }
    return self;
}

- (void)requestProductWithIds:(NSArray<NSString *> *)productIds
               completedBlock:(void (^)(NSArray<SKProduct *> *))completedBlock {
    // 无需查询苹果服务器
    if (!productIds.count) {
        if (completedBlock) {
            completedBlock(nil);
        }
        return;
    }
    NSMutableArray *findResults = [NSMutableArray array];
    NSMutableArray *needRequestIds = [NSMutableArray arrayWithArray:productIds];
    // 遍历已查询到的商品信息
    NSArray *allProducts = self.allProducts;
    for (SKProduct *product in allProducts) {
        if ([productIds containsObject:product.productIdentifier]) {
            // 移除待请求的 productId
            [needRequestIds removeObject:product.productIdentifier];
            [findResults addObject:product];
        }
    }
    
    // 无需查询苹果服务器
    if (!needRequestIds.count) {
        if (completedBlock) {
            completedBlock(findResults.copy);
        }
        return;
    }
    
    // 保存回调Block
    self.productsCompletedBlock = completedBlock;
    self.productRequestIds = productIds;
    
    // 向苹果服务器查询商品信息
    NSSet *set = [NSSet setWithArray:needRequestIds];
    SKProductsRequest *request = [[SKProductsRequest alloc] initWithProductIdentifiers:set];
    request.delegate = self;
    [request start];
}

- (NSArray<SKProduct *> *)products {
    NSArray *allProducts = self.allProducts;
    return allProducts;
}

- (BOOL)isPaying {
    return self.payCompletedBlock != nil;
}

- (void)showLoadingHUDText:(NSString *)showText {
    self.lastShowToastText = showText ?: @"";
    UIWindow *window = [UIApplication sharedApplication].delegate.window;
    [UIWindow imy_setOnceLoadingFullBGColor:[UIColor colorWithWhite:0 alpha:0.4]];
    if (showText.length > 0) {
        [window imy_showLoadingHUDWithText:showText];
    } else {
        [window imy_showLoadingHUD];
    }
    // 1秒后检测 loading 是否存在
    [self delayRecheckLoadingHUD];
}

- (void)hideLoadingHUD {
    self.lastShowToastText = nil;
    UIWindow *window = [UIApplication sharedApplication].delegate.window;
    [window imy_hideHUD];
}

/// 由于登录会引起各种View重构，所以只能定时检测 + 重启唤起
- (void)delayRecheckLoadingHUD {
    [NSObject imy_asyncBlock:^{
        if (!self.lastShowToastText) {
            // 已经被关闭
            return;
        }
        UIWindow *window = [UIApplication sharedApplication].delegate.window;
        MBProgressHUD *hud = [MBProgressHUD HUDForView:window];
        if (hud != nil) {
            // loading hud 还存在 1秒后 继续检测
            [self delayRecheckLoadingHUD];
            return;
        }
        // loading hud 被关闭，重新唤起
        [self showLoadingHUDText:self.lastShowToastText];
    } onQueue:dispatch_get_main_queue() afterSecond:1 forKey:@"RightsPayRecheckLoadingHUD"];
}

/// 直接购买
- (void)payWithSubPlanId:(NSString * const)planId
                 priceId:(NSString * const)priceId
               renewalId:(NSString * const)renewalId
            appendParams:(NSDictionary * const)appendParams
           progressBlock:(void (^ const)(NSDictionary *, NSInteger))progressBlock
          completedBlock:(void (^ const)(NSDictionary *, NSError *))completedBlock {
    if (!NSThread.isMainThread) {
        // 必须要在主线程调用
        imy_asyncMainBlock(^{
            [self payWithSubPlanId:planId
                           priceId:priceId
                         renewalId:renewalId
                      appendParams:appendParams
                     progressBlock:progressBlock
                    completedBlock:completedBlock];
        });
        return;
    }
    
#ifdef DEBUG
    // 判断 bundle id 是否正确
    NSString *appBundleId = [NSBundle mainBundle].bundleIdentifier;
    // 使用海南开发者证书打包，无法进行苹果支付
    if ([appBundleId hasSuffix:@".hainan"]) {
        [UIWindow imy_showTextHUD:@"海南证书无法进行苹果支付..."];
        if (completedBlock) {
            NSError *error = [NSError errorWithDomain:@"海南证书无法进行苹果支付..." code:-1 userInfo:nil];
            completedBlock(nil, error);
        }
        return;
    }
#endif
    
    if (!SKPaymentQueue.canMakePayments) {
        if (completedBlock) {
            NSError *error = [NSError errorWithDomain:@"您的设备暂不支持购买，请查阅苹果系统中「内容与隐私限制」相关支付限制" code:-1 userInfo:nil];
            completedBlock(nil, error);
        }
        // 设备不支持购买
        [IMYErrorTraces postWithType:IMYErrorTraceTypeOthers pageName:IMYMeetyouHTTPHooks.currentPageName category:IMYErrorTraceCategoryRights message:@"rights-device-nopay" detail:@{
            @"planId" : planId ?: @"",
            @"priceId" : priceId ?: @"",
            @"renewalId" : renewalId ?: @"",
        }];
        return;
    }
    
    // 购买中，直接返回错误
    if (self.isPaying) {
        if (completedBlock) {
            NSError *error = [NSError errorWithDomain:@"正在购买中，请稍后重试..." code:-1 userInfo:nil];
            completedBlock(nil, error);
        }
        // 设备购买中
        [IMYErrorTraces postWithType:IMYErrorTraceTypeOthers pageName:IMYMeetyouHTTPHooks.currentPageName category:IMYErrorTraceCategoryRights message:@"rights-device-paying" detail:@{
            @"planId" : planId ?: @"",
            @"priceId" : priceId ?: @"",
            @"renewalId" : renewalId ?: @"",
        }];
        return;
    }
    
    // 保存支付信息
    self.planId = planId;
    self.priceId = priceId;
    self.renewalId = renewalId;
    self.appendParams = appendParams;
    self.payCompletedBlock = completedBlock;
    self.payProgressBlock = progressBlock;
    
    // 重置订单信息
    self.currentOrderData = nil;
    self.currentVerifyData = nil;
    self.offer_sign = nil;
    
    // 正常购买流程
    self.isRestorePaying = NO;
    
    // 重置重试次数
    self.retryOutBoxCount = 0;
    self.iapReceiptVerifying = NO;
    
    // 接口请求
    RACSignal *getOrderSignal = nil;
    
    if (self.renewalId.length > 0) {
        // 续费优惠订单：显示loading
        [self showLoadingHUDText:nil];
        
        NSMutableDictionary *params = [NSMutableDictionary dictionaryWithDictionary:self.appendParams];
        params[@"user_promotion_id"] = @(self.renewalId.integerValue);
        
        getOrderSignal = [IMYServerRequest postPath:@"v3/sub/renewal_order" host:sub_seeyouyima_com params:params headers:nil];
    } else {
        // 正常下单：显示loading
        if (IMYRightsSDK.isSubAuditReview) {
            [self showLoadingHUDText:nil];
        } else {
            [self showLoadingHUDText:@"正在连接苹\n果支付..."];
        }
        
        NSMutableDictionary *params = [NSMutableDictionary dictionaryWithDictionary:self.appendParams];
        params[@"sub_plan_id"] = @(self.planId.integerValue);
        params[@"sub_plan_pricing_config_id"] = @(self.priceId.integerValue);
        params[@"pay_type"] = @1;
        
        getOrderSignal = [IMYServerRequest postPath:@"v3/sub/order" host:sub_seeyouyima_com params:params headers:nil];
    }
    
    // 正式接口请求
    [getOrderSignal subscribeNext:^(IMYHTTPResponse *x) {
        NSDictionary *data = x.responseObject;
        // 服务器创建订单成功
        self.myTransactionId = data[@"transaction_id"] ?: @"";
        self.myUUID = data[@"uuid"] ?: @"";
        self.myProductId = data[@"iap_id"] ?: @"";
        
        // iOS12.2 以上才支持优惠券
        if (@available(iOS 12.2, *)) {
            self.offer_sign = data[@"offer_sign"];
        }
        
        // 存储订单信息，返回外部使用
        self.currentOrderData = data;
        
        // 下单成功
        [IMYErrorTraces postWithType:IMYErrorTraceTypeOthers pageName:IMYMeetyouHTTPHooks.currentPageName category:IMYErrorTraceCategoryRights message:@"rights-order-success" detail:@{
            @"planId" : planId ?: @"",
            @"priceId" : priceId ?: @"",
            @"renewalId" : renewalId ?: @"",
            @"prodId" : self.myProductId ?: @"",
            @"uuid" : self.myUUID ?: @"",
            @"offer" : self.offer_sign ?: @"",
        } needRetry:YES];
        
        // 开始IAP支付
        [self startIAPWithProductId:self.myProductId];
        
    } error:^(NSError * const error) {
        // 创建订单失败
        NSString *errorDomain = @"创建订单失败!";
        NSInteger errorCode = -2;
        NSDictionary *errorUserInfo = @{
            NSUnderlyingErrorKey : error,
        };
        NSError *outError = [NSError errorWithDomain:errorDomain code:errorCode userInfo:errorUserInfo];
        [self handlePayCompletedWithResult:nil error:outError];
        
        // 下单失败
        [IMYErrorTraces postWithType:IMYErrorTraceTypeNoneUI pageName:IMYMeetyouHTTPHooks.currentPageName category:IMYErrorTraceCategoryRights message:@"rights-order-fails" detail:@{
            @"planId" : planId ?: @"",
            @"priceId" : priceId ?: @"",
            @"renewalId" : renewalId ?: @"",
            @"error" : @{
                @"code" : @(error.code),
                @"reason" : error.localizedFailureReason ?: error.domain ?: @"",
                @"body" : [error.af_responseData imy_jsonObject] ?: @{},
            }
        } needRetry:YES];
    }];
}

/// 恢复购买
- (void)restoreWithAppendParams:(NSDictionary *)appendParams
                  progressBlock:(void (^)(NSDictionary *, NSInteger))progressBlock
                 completedBlock:(void (^)(NSDictionary *, NSError *))completedBlock {
    if (!NSThread.isMainThread) {
        // 必须要在主线程调用
        imy_asyncMainBlock(^{
            [self restoreWithAppendParams:appendParams
                            progressBlock:progressBlock
                           completedBlock:completedBlock];
        });
        return;
    }
    
#ifdef DEBUG
    // 判断 bundle id 是否正确
    NSString *appBundleId = [NSBundle mainBundle].bundleIdentifier;
    // 使用海南开发者证书打包，无法进行苹果支付
    if ([appBundleId hasSuffix:@".hainan"]) {
        [UIWindow imy_showTextHUD:@"海南证书无法进行苹果支付..."];
        if (completedBlock) {
            NSError *error = [NSError errorWithDomain:@"海南证书无法进行苹果支付..." code:-1 userInfo:nil];
            completedBlock(nil, error);
        }
        return;
    }
#endif
    
    if (!SKPaymentQueue.canMakePayments) {
        if (completedBlock) {
            NSError *error = [NSError errorWithDomain:@"您的设备暂不支持购买，请查阅苹果系统中「内容与隐私限制」相关支付限制" code:-1 userInfo:nil];
            completedBlock(nil, error);
        }
        // 设备不支持购买
        [IMYErrorTraces postWithType:IMYErrorTraceTypeOthers pageName:IMYMeetyouHTTPHooks.currentPageName category:IMYErrorTraceCategoryRights message:@"rights-device-nopay" detail:@{
            @"restore" : @"1",
        }];
        return;
    }
    
    // 购买中，直接返回错误
    if (self.isPaying) {
        if (completedBlock) {
            NSError *error = [NSError errorWithDomain:@"正在购买中，请稍后重试..." code:-1 userInfo:nil];
            completedBlock(nil, error);
        }
        // 设备购买中
        [IMYErrorTraces postWithType:IMYErrorTraceTypeOthers pageName:IMYMeetyouHTTPHooks.currentPageName category:IMYErrorTraceCategoryRights message:@"rights-device-paying" detail:@{
            @"restore" : @"1",
        }];
        return;
    }
    
    // 保存支付信息
    self.planId = nil;
    self.priceId = nil;
    self.renewalId = nil;
    self.appendParams = appendParams;
    self.payCompletedBlock = completedBlock;
    self.payProgressBlock = progressBlock;
    
    // 重置订单信息
    self.currentOrderData = nil;
    self.currentVerifyData = nil;
    self.offer_sign = nil;
    self.myUUID = nil;
    self.myTransactionId = nil;
    self.myProductId = nil;
    
    // 恢复购买流程
    self.isRestorePaying = YES;
    self.isRestoreCalled = NO;
    
    // 重置重试次数
    self.retryOutBoxCount = 0;
    self.iapReceiptVerifying = NO;
    
    // 显示loading
    [self showLoadingHUDText:@"正在为您查询及\n恢复订单权益..."];
    
    // 结束所有交易
    SKPaymentQueue *queue = [SKPaymentQueue defaultQueue];
    NSMutableArray *nowPaymentsInfo = [NSMutableArray array];
    for (SKPaymentTransaction *transaction in queue.transactions) {
        // 目前队列中的订单
        [nowPaymentsInfo addObject:@{
            @"id" : transaction.transactionIdentifier,
            @"state" : @(transaction.transactionState),
            @"product" : transaction.payment.productIdentifier,
        }];
        // 结束IAP支付流程
        [queue finishTransaction:transaction];
    }
    
    // 开始恢复
    [IMYErrorTraces postWithType:IMYErrorTraceTypeOthers pageName:IMYMeetyouHTTPHooks.currentPageName category:IMYErrorTraceCategoryRights message:@"rights-restore-begin" detail:@{
        @"nows" : nowPaymentsInfo
    }];
    
    // 需要先删除本地缓存的Verify数据，不然客户端自动重试后，会覆盖最新的套餐值
    [self outBoxDeleteVerifyData];
    
    // 调用苹果的恢复购买
    [queue restoreCompletedTransactions];
}

#pragma mark - 查询商品

- (void)productsRequest:(SKProductsRequest *)request didReceiveResponse:(SKProductsResponse *)response {
    // 历史商品的查询信息
    NSArray *oldProducts = self.allProducts;
    NSMutableArray<SKProduct *> *allProducts = [NSMutableArray arrayWithArray:response.products];
    [allProducts addObjectsFromArray:oldProducts];
    self.allProducts = allProducts;
    
    // 返回需要的查询结果
    if (self.productsCompletedBlock) {
        NSMutableArray *findResults = [NSMutableArray array];
        for (SKProduct *product in allProducts) {
            if ([self.productRequestIds containsObject:product.productIdentifier]) {
                [findResults addObject:product];
            }
        }
        self.productsCompletedBlock(findResults.copy);
        self.productsCompletedBlock = nil;
        self.productRequestIds = nil;
    }
}

#pragma mark - IAP购买流程

- (void)startIAPWithProductId:(NSString *)productId {
    NSArray *productIdArray = productId.length > 0 ? @[productId] : nil;
    [self requestProductWithIds:productIdArray completedBlock:^(NSArray<SKProduct *> * _Nonnull results) {
        SKProduct *product = results.firstObject;
        if (product && [IMYRightsPaySDK hasLocalCheckIntroOffer] && self.offer_sign.count > 0) {
            [IMYHIVE_BINDER(IOCAppInfo) hasIntroOfferWithProductId:product.productIdentifier
                                                        completion:^(NSInteger const status) {
                [self startPaymentWithProduct:product hasIntroOffer:status];
            }];
        } else {
            [self startPaymentWithProduct:product hasIntroOffer:0];
        }
    }];
}

+ (BOOL)hasLocalCheckIntroOffer {
    return YES;
}

/// hasIntroOffer 判断用户是否有首购优惠：0：未知，1：可享受，2：无优惠
- (void)startPaymentWithProduct:(SKProduct *)product hasIntroOffer:(NSInteger const)hasIntroOffer {
    if (!product) {
        NSString *errorDomain = nil;
        if (![NSLocale.currentLocale.countryCode isEqualToString:@"CN"]) {
            errorDomain = IMYString(@"苹果系统支付失败，您可以尝试切换网络后重试、改用中国大陆地区Apple账号购买");
        } else {
            errorDomain = IMYString(@"苹果系统支付失败，您可以尝试切换网络后重试");
        }
        NSError *iapError = [NSError errorWithDomain:errorDomain code:-3 userInfo:nil];
        [self handlePayCompletedWithResult:nil error:iapError];
        // 未找到商品
        [IMYErrorTraces postWithType:IMYErrorTraceTypeNoneUI pageName:IMYMeetyouHTTPHooks.currentPageName category:IMYErrorTraceCategoryRights message:@"rights-product-fails" detail:@{
            @"prodId" : self.myProductId ?: @"",
            @"uuid" : self.myUUID ?: @"",
            @"country" : NSLocale.currentLocale.countryCode ?: @"",
        } needRetry:YES];
        return;
    }
    NSString * const productId = product.productIdentifier;
    // 记录所有支付的商品id，用于过滤会员订阅的流程
    if (![self.allPayProductIdSet containsObject:productId]) {
        [self.allPayProductIdSet addObject:productId];
        [[IMYKV defaultKV] setArray:self.allPayProductIdSet.allObjects forKey:kAllProductIDKey];
    }
    
    // 需要先删除本地缓存的Verify数据，不然客户端自动重试后，会覆盖最新的套餐值
    [self outBoxDeleteVerifyData];
    
    // 调用苹果支付
    SKMutablePayment *payment = [SKMutablePayment paymentWithProduct:product];
    NSString *applicationUsername = self.myUUID;
    if (applicationUsername.length > 0) {
        payment.applicationUsername = applicationUsername;
    }
    
    NSDictionary *offer_sign = self.offer_sign;
    if (offer_sign.count > 0 && hasIntroOffer != 1) {
        NSString *offer_id = offer_sign[@"offer_id"];
        NSString *key_id = offer_sign[@"key_id"];
        NSInteger timestamp = [offer_sign[@"timestamp"] integerValue];
        NSString *signature = offer_sign[@"signature"];
        NSString *nonce = offer_sign[@"nonce"];
        if (offer_id.length > 0 && key_id.length > 0 && timestamp > 0 && signature.length > 0 && nonce.length > 0) {
            SKPaymentDiscount *discount = [[SKPaymentDiscount alloc] initWithIdentifier:offer_id keyIdentifier:key_id nonce:[[NSUUID alloc] initWithUUIDString:nonce] signature:signature timestamp:@(timestamp)];
            payment.paymentDiscount = discount;
        }
    }
    
    [[SKPaymentQueue defaultQueue] addPayment:payment];
    
    // 开始支付
    [IMYErrorTraces postWithType:IMYErrorTraceTypeOthers pageName:IMYMeetyouHTTPHooks.currentPageName category:IMYErrorTraceCategoryRights message:@"rights-pay-begin" detail:@{
        @"uuid" : self.myUUID ?: @"",
        @"iapPid" : productId ?: @"",
        @"offer_sign" : offer_sign ?: @"",
        @"introOffer" : @(hasIntroOffer),
    } needRetry:YES];
}

- (BOOL)paymentQueue:(SKPaymentQueue *)queue shouldAddStorePayment:(SKPayment *)payment forProduct:(SKProduct *)product {
    if (IMYPublicAppHelper.shareAppHelper.hasLogin) {
        return YES;
    }
    return NO;
}

- (void)paymentQueue:(SKPaymentQueue *)queue updatedTransactions:(NSArray *)transactions {
    // 处于恢复购买中，不执行任何操作
    if (self.isRestorePaying) {
        return;
    }
    for (SKPaymentTransaction *transaction in transactions) {
        NSString * const productId = transaction.payment.productIdentifier;
        // 过滤非会员权益SDK 发起的支付，可能来自其他支付模块
        if (![self.allPayProductIdSet containsObject:productId]) {
            continue;
        }
        switch (transaction.transactionState) {
            case SKPaymentTransactionStatePurchasing: {
                // 支付中
                [self handlePayProgressWithState:SKPaymentTransactionStatePurchasing];
            } break;
            case SKPaymentTransactionStateDeferred: {
                // 支付中，等待外部操作
                [self handlePayProgressWithState:SKPaymentTransactionStateDeferred];
            } break;
            case SKPaymentTransactionStateFailed: {
                // 交易失败
                [self handlePaymentFailedWithTransaction:transaction];
            } break;
            case SKPaymentTransactionStatePurchased:
            case SKPaymentTransactionStateRestored: {
                // 交易完成
                [self handlePaymentPurchasedWithTransaction:transaction];
            } break;
        }
        NSLog(@"iap-event-sdk-state === %ld", transaction.transactionState);
    }
}

- (void)paymentQueue:(SKPaymentQueue *)queue restoreCompletedTransactionsFailedWithError:(NSError *)iapError {
    if (!self.isRestorePaying) {
        // 处于正常购买流程
        return;
    }
    
    // 封装错误
    NSString *errorDomain = nil;
    NSInteger errorCode = 0;
    NSDictionary *errorUserInfo = nil;
    BOOL const isCancelled = (iapError.code == SKErrorPaymentCancelled);
    if(!isCancelled) {
        errorDomain = @"恢复购买失败!";
        errorCode = -100;
    } else {
        errorDomain = @"用户取消!";
        errorCode = -101;
    }
    if (iapError != nil) {
        errorUserInfo = @{
            NSUnderlyingErrorKey : iapError
        };
    }
    // 传递给外部的错误信息
    NSError *outError = [NSError errorWithDomain:errorDomain code:errorCode userInfo:errorUserInfo];
    [self handlePayCompletedWithResult:nil error:outError];
    
    // 恢复完成
    NSUInteger const errorType = (isCancelled ? IMYErrorTraceTypeOthers : IMYErrorTraceTypeNoneUI);
    NSString * const errorMsg = (isCancelled ? @"rights-restore-cancel" : @"rights-restore-fails");
    [IMYErrorTraces postWithType:errorType pageName:IMYMeetyouHTTPHooks.currentPageName category:IMYErrorTraceCategoryRights message:errorMsg detail:@{
        @"error" : @{
            @"code" : @(iapError.code),
            @"reason" : iapError.localizedFailureReason ?: iapError.domain ?: @"",
        }
    }];
    
    // 结束所有订单
    for (SKPaymentTransaction *trans in queue.transactions) {
        [queue finishTransaction:trans];
    }
}

- (BOOL)isTransactionFinished:(SKPaymentTransaction *)trans {
    return trans.transactionState == SKPaymentTransactionStatePurchased || trans.transactionState == SKPaymentTransactionStateRestored;
}

- (void)paymentQueueRestoreCompletedTransactionsFinished:(SKPaymentQueue *)queue {
    if (!self.isRestorePaying) {
        // 处于正常购买流程
        return;
    }
    if (self.isRestoreCalled) {
        // 已经完成回调
        return;
    }
    self.isRestoreCalled = YES;
    
    /// 按时间排序，最新的在最后面
    NSArray<SKPaymentTransaction *> *sortedArray = [queue.transactions sortedArrayUsingComparator:^NSComparisonResult(SKPaymentTransaction *obj1, SKPaymentTransaction *obj2) {
        if (![self isTransactionFinished:obj1] && [self isTransactionFinished:obj2]) {
            return NSOrderedAscending;
        }
        if ([self isTransactionFinished:obj1] && ![self isTransactionFinished:obj2]) {
            return NSOrderedDescending;
        }
        return [obj1.transactionDate compare:obj2.transactionDate];
    }];
    
    SKPaymentTransaction *lastTransaction = sortedArray.lastObject;
    if (![self isTransactionFinished:lastTransaction]) {
        // 非支付完成的订单
        lastTransaction = nil;
    }
    
    if (!lastTransaction) {
        // 无订单数据，封装错误
        NSString *errorDomain = @"用户未购买过!";
        NSInteger errorCode = -301;
        // 传递给外部的错误信息
        NSError *outError = [NSError errorWithDomain:errorDomain code:errorCode userInfo:nil];
        [self handlePayCompletedWithResult:nil error:outError];
        
        // 恢复完成
        [IMYErrorTraces postWithType:IMYErrorTraceTypeOthers pageName:IMYMeetyouHTTPHooks.currentPageName category:IMYErrorTraceCategoryRights message:@"rights-restore-completed" detail:@{
            @"error" : @{
                @"code" : @(errorCode),
                @"reason" : errorDomain,
            }
        }];
    } else {
        // 恢复最新订单
        [self handlePaymentPurchasedWithTransaction:lastTransaction];
    }
    
    // 结束所有订单
    for (SKPaymentTransaction *trans in queue.transactions) {
        if (trans != lastTransaction) {
            [queue finishTransaction:trans];
        }
    }
}

#pragma mark - 处理各种支付回调

- (void)handlePaymentPurchasedWithTransaction:(SKPaymentTransaction *)transaction {
    if (self.iapReceiptVerifying) {
        // 正在验证订单
        return;
    }
    
    if (!self.payCompletedBlock && self.isReceiptVerified && transaction.originalTransaction) {
        // 属于订阅订单，当前生命周期已经校验过，并且无用户操作，则不再执行服务端校验
        [[SKPaymentQueue defaultQueue] finishTransaction:transaction];
        return;
    }
    
    // 如果当前为续费优惠订单，则需要过滤其他订单回调
    if (@available(iOS 12.2, *)) {
        BOOL isPaymentDiscount = (transaction.payment.paymentDiscount.identifier.length > 0);
        BOOL isPaymentUUIDEqual = [self.myUUID isEqualToString:transaction.payment.applicationUsername];
        if (self.payCompletedBlock && self.renewalId.length > 0 && self.offer_sign.count > 0 &&
            !isPaymentDiscount && !isPaymentUUIDEqual ) {
            [[SKPaymentQueue defaultQueue] finishTransaction:transaction];
            return;
        }
    }
    
    self.iapReceiptVerifying = YES;
    
    // 当前交易id
    NSString * const iapTransactionId = transaction.transactionIdentifier;
    // 当前商品id
    NSString * const iapProductId = transaction.payment.productIdentifier;
    // 订阅原始交易id
    NSString * const iapOriginalTransactionId = transaction.originalTransaction.transactionIdentifier;
    
    // 交易凭证：服务端无用，为了提高性能，写死 X
    NSString *receiptBase64 = @"X";
    if ([self needGetReceiptBase64Data]) {
        // 交易凭证
        NSData *transactionReceipt = nil;
        if (!transactionReceipt.length) {
            transactionReceipt = [NSData dataWithContentsOfURL:[NSBundle mainBundle].appStoreReceiptURL];
        }
        if (!transactionReceipt.length) {
            transactionReceipt = transaction.transactionReceipt;
        }
        // 获取首次交易信息
        if (transaction.originalTransaction) {
            if (!transactionReceipt.length) {
                transactionReceipt = transaction.originalTransaction.transactionReceipt;
            }
        }
        receiptBase64 = [transactionReceipt base64EncodedStringWithOptions:0];
    }
    
    // 本地无数据的情况下，获取苹果存储的UUID
    if (!self.myUUID.length) {
        NSString *nowUUID = transaction.payment.applicationUsername;
        NSString *origUUID = transaction.originalTransaction.payment.applicationUsername;
        if (nowUUID.length > 0) {
            self.myUUID = nowUUID;
        } else if (origUUID.length > 0) {
            self.myUUID = origUUID;
        }
    }
    
    if (!self.myProductId.length) {
        NSString *nowProdID = transaction.payment.productIdentifier;
        NSString *origProdID = transaction.originalTransaction.payment.productIdentifier;
        if (nowProdID.length > 0) {
            self.myProductId = nowProdID;
        } else if (origProdID.length > 0) {
            self.myProductId = origProdID;
        }
    }
    
    // 有概率 loading hud 被外部隐藏了，这边重新唤起一次
    if (self.payCompletedBlock) {
        imy_asyncMainExecuteBlock(^{
            [self showLoadingHUDText:@"等待结果..."];
        });
    }
    
    // 生成接口请求参数
    NSMutableDictionary *jsonMap = [NSMutableDictionary dictionary];
    NSString * const accountUid = IMYHIVE_BINDER(IOCAppInfo).getAccountModelUserID;
    jsonMap[@"uid"] = (accountUid.length > 0 ? accountUid : [IMYPublicAppHelper shareAppHelper].userid);
    jsonMap[@"uuid"] = self.myUUID ?: @"";
    jsonMap[@"sub_order_id"] = self.myTransactionId ?: @"";
    jsonMap[@"transaction_id"] = iapOriginalTransactionId ?: @"";
    jsonMap[@"receipt"] = receiptBase64 ?: @"";
    jsonMap[@"transaction_order_id"] = iapTransactionId ?: @"";
    jsonMap[@"product_id"] = iapProductId ?: @"";
    jsonMap[@"channel_type"] = @1;
    if (self.isRestorePaying) {
        jsonMap[@"user_restore"] = @1;
    }
    jsonMap[@"is_restore"] = (transaction.transactionState == SKPaymentTransactionStateRestored ? @1 : @0);
    jsonMap[@"renewalId"] = self.renewalId ?: @"";
    
    // 写入磁盘，下次启动可以直接复用
    [self outBoxWriteVerifyData:jsonMap];
    
    // 支付成功
    [IMYErrorTraces postWithType:IMYErrorTraceTypeOthers pageName:IMYMeetyouHTTPHooks.currentPageName category:IMYErrorTraceCategoryRights message:@"rights-pay-success" detail:@{
        @"uuid" : self.myUUID ?: @"",
        @"renewalId" : self.renewalId ?: @"",
        @"iapUUID" : transaction.payment.applicationUsername ?: @"",
        @"iapPid" : iapProductId ?: @"",
        @"iapTid" : iapTransactionId ?: @"",
        @"iapTorigid" : iapOriginalTransactionId ?: @"",
        @"iapState" : @(transaction.transactionState),
        @"iapDate" : transaction.transactionDate.imy_getDateTimeString ?: @"",
        @"restore" : @(self.isRestorePaying),
    } needRetry:YES];
    
    // 调用验证接口
    [self startSubVerifyWithData:jsonMap
                  completedBlock:^(NSDictionary *result, NSError * const error) {
        // 只有单次下单，才保留验证结果
        if (self.payCompletedBlock) {
            self.currentVerifyData = result;
        }
        [self handlePayCompletedWithResult:@{
            @"isVerifying" : @YES
        } error:error];
        
        // 购买流程结束
        self.iapReceiptVerifying = NO;
        
        // 已执行过订单校验
        self.isReceiptVerified = YES;
    }];
    
    // 完成IAP支付流程
    [[SKPaymentQueue defaultQueue] finishTransaction:transaction];
}

- (BOOL)needGetReceiptBase64Data {
    return NO;
}

- (void)startSubVerifyWithData:(NSDictionary * const)jsonMap
                completedBlock:(void (^)(NSDictionary *, NSError *))completedBlock {
    // 获取票据，临时存储
    NSMutableDictionary * const jsonPlus = [jsonMap mutableCopy];
    
    // 修正当前请求Body内的uid参数
    NSString * const accountUid = IMYHIVE_BINDER(IOCAppInfo).getAccountModelUserID;
    jsonPlus[@"uid"] = (accountUid.length > 0 ? accountUid : [IMYPublicAppHelper shareAppHelper].userid);
    
    // 对票据以外的数据进行加密
    NSString * const receipt = [jsonPlus objectForKey:@"receipt"];
    [jsonPlus removeObjectForKey:@"receipt"];
    
    // 优惠续费订单判断，并改为 user_promotion_id 传给服务端
    NSString * const renewalId = [jsonPlus objectForKey:@"renewalId"];
    [jsonPlus removeObjectForKey:@"renewalId"];
    if (renewalId.length > 0) {
        jsonPlus[@"user_promotion_id"] = @(renewalId.integerValue);
    }
    
    // 获取苹果订单号
    NSString *iapTid = jsonMap[@"transaction_order_id"];
    if (!iapTid.length) {
        iapTid = jsonMap[@"transaction_id"];
    }
    
    // 参数加密
    NSString *verifyData = [NSString imy_encodeScriptWithString:jsonPlus.imy_jsonString
                                                         aesKey:@"bdc574e1e4f1580491d0ab57598fcce1"];
    
    NSMutableDictionary * const params = [NSMutableDictionary dictionaryWithDictionary:self.appendParams];
    params[@"verify_data"] = verifyData ?: @"";
    params[@"receipt"] = receipt ?: @"";
#ifdef DEBUG
    // Debug 增加明文参数
    params[@"params"] = [jsonPlus copy];
#endif
    
    // 如果是重试执行，修正当前的 myProductId 和 myUUID
    if (!self.myProductId.length) {
        self.myProductId = jsonMap[@"product_id"];
    }
    if (!self.myUUID.length) {
        self.myUUID = jsonMap[@"uuid"];
    }
    if (!self.renewalId.length && renewalId.length > 0) {
        self.renewalId = renewalId;
    }
    
    // 存储最后一次的校验信息
    self.lastVerifyInfos = @{
        @"prodId" : self.myProductId ?: @"",
        @"uuid" : self.myUUID ?: @"",
        @"restore" : @(self.isRestorePaying),
        @"iapTid" : iapTid ?: @"",
        @"uid" : jsonPlus[@"uid"] ?: @"",
    };
    
    // 调用鉴权接口，会自动重试一次。
    [self postSubVerifyWithParams:params
                       retryCount:0
                   completedBlock:completedBlock];
}

- (void)postSubVerifyWithParams:(NSDictionary * const)params
                     retryCount:(NSInteger const)retryCount
                 completedBlock:(void (^)(NSDictionary *, NSError *))completedBlock {
    // 获取苹果订单号
    NSString * const iapTid = self.lastVerifyInfos[@"iapTid"] ?: @"";
    
    // 判断续费优惠走不同接口
    RACSignal *postVerifySignal = nil;
    if (self.renewalId.length > 0) {
        postVerifySignal = [IMYPublicServerRequest postPath:@"v3/sub/renewal_verify"
                                                       host:sub_seeyouyima_com
                                                     params:params
                                                    headers:nil];
    } else {
        postVerifySignal = [IMYPublicServerRequest postPath:@"v3/sub/verify"
                                                       host:sub_seeyouyima_com
                                                     params:params
                                                    headers:nil];
    }
    
    [postVerifySignal subscribeNext:^(IMYHTTPResponse *x) {
        // 删除本地缓存数据
        [self outBoxDeleteVerifyData];
        // 返回成功
        NSDictionary * const data = x.responseObject;
        if (completedBlock) {
            completedBlock(data, nil);
        }
        // 判断服务端返回值是否正确，无订单id，则获取完整body数据和请求头中的监测ID
        NSString *resInfo = data[@"transaction_id"];
        if (!resInfo.length) {
            NSString *trace_id = [x.response valueForHTTPHeaderField:@"trace-id"];
            NSString *body_str = [data imy_jsonString];
            resInfo = [NSString stringWithFormat:@"trace_id=%@;body=%@;", trace_id, body_str];
        }
        // 验证成功
        [IMYErrorTraces postWithType:IMYErrorTraceTypeOthers pageName:IMYMeetyouHTTPHooks.currentPageName category:IMYErrorTraceCategoryRights message:@"rights-verify-success" detail:@{
            @"uuid" : self.myUUID ?: @"",
            @"iapTid" : iapTid ?: @"",
            @"restore" : @(self.isRestorePaying),
            @"uid" : self.lastVerifyInfos[@"uid"] ?: @"",
            @"resInfo" : resInfo ?: @"",
        } needRetry:YES];
        // 重置重试时间
        self.retryOutBoxDelay = 0;
        self.retryOutBoxCount = 0;
    } error:^(NSError * const error) {
        // 递增重试时间: 2、4、6、8、10 等，最多20次
        self.retryOutBoxDelay = MIN(10, self.retryOutBoxDelay + 2);
        self.retryOutBoxCount += 1;
        
        // 是否正在迁移中
        BOOL isGotoTransferring = NO;
        // 允许重试
        BOOL canRetryPost = YES;
        
        // 属于服务器正常，属于其他逻辑错误，删除本地缓存数据，不在重试
        if (error.af_httpResponse.statusCode == 200) {
            NSDictionary * const apiBody = [error.af_responseData imy_jsonObject];
            NSInteger const apiCodeInt = [apiBody[@"code"] integerValue];
            NSDictionary * const apiData = apiBody[@"data"];
            NSString * const gotoURI = apiData[@"uri"];
            if (apiCodeInt == 4011 && gotoURI.length > 0) {
                // 删除待发送区票据信息
                [self outBoxDeleteVerifyData];
                // 需要执行账号迁移
                isGotoTransferring = YES;
                imy_asyncMainExecuteBlock(^{
                    [self setupGotoTransferringURI:gotoURI apiData:apiData completedBlock:completedBlock];
                });
            } else {
                // 服务器指定code 需要重试
                if (apiCodeInt == 4007 || apiCodeInt == 4008 || apiCodeInt == 4009) {
                    [self outBoxSendVerifyData];
                } else {
                    // 其他业务code 删除待发送数据
                    [self outBoxDeleteVerifyData];
                    canRetryPost = NO;
                }
            }
        } else {
            // 如果属于 服务器/网络 错误，则再发送次
            [self outBoxSendVerifyData];
        }
        
        if (!isGotoTransferring) {
            // 属于用户操作的支付校验，2秒后重试一次，最多3次
            if (self.payCompletedBlock && retryCount < 2 && canRetryPost) {
                imy_asyncBlock(2, ^{
                    [self postSubVerifyWithParams:params
                                       retryCount:retryCount + 1
                                   completedBlock:completedBlock];
                });
            } else {
                // 返回验证失败
                NSString *errorDomain = @"购买验证失败!";
                NSInteger errorCode = -201;
                NSDictionary *errorUserInfo = @{
                    NSUnderlyingErrorKey : error,
                };
                NSError *outError = [NSError errorWithDomain:errorDomain code:errorCode userInfo:errorUserInfo];
                if (completedBlock) {
                    completedBlock(nil, outError);
                }
            }
        }
        
        // 验证失败
        [IMYErrorTraces postWithType:IMYErrorTraceTypeNoneUI
                            pageName:IMYMeetyouHTTPHooks.currentPageName
                            category:IMYErrorTraceCategoryRights
                             message:(isGotoTransferring ? @"rights-verify-transfer" : @"rights-verify-fails")
                              detail:@{
            @"prodId" : self.myProductId ?: @"",
            @"uuid" : self.myUUID ?: @"",
            @"iapTid" : iapTid ?: @"",
            @"restore" : @(self.isRestorePaying),
            @"uid" : self.lastVerifyInfos[@"uid"] ?: @"",
            @"error" : @{
                @"code" : @(error.code),
                @"reason" : error.localizedFailureReason ?: error.domain ?: @"",
                @"body" : [error.af_responseData imy_jsonObject] ?: @{},
            }
        } needRetry:YES];
    }];
}

- (void)setupGotoTransferringURI:(NSString * const)gotoURI
                         apiData:(NSString * const)apiData
                  completedBlock:(void (^)(NSDictionary *, NSError *))apiCompletedBlock {
    
    // 非用户操作，直接返回失败
    if (!self.payCompletedBlock) {
        NSString *message = @"已取消会员权益迁移，\n请稍后再试";
        NSError *outError = [NSError errorWithDomain:message code:-1 userInfo:nil];
        apiCompletedBlock(nil, outError);
        return;
    }
    
    // 先隐藏历史Loading
    [self hideLoadingHUD];
    
    // 显示提示Toast
    NSString *showHudText = apiData[@"toast"];
    if (showHudText.length > 0) {
        [UIWindow imy_showTextHUD:showHudText];
    }
    
    // 先禁止用户操作，因为需要底层等待2秒进行跳转判断
    UIWindow * const mainWindow = [UIApplication sharedApplication].delegate.window;
    mainWindow.userInteractionEnabled = NO;
    
    // 获取当前页面
    UIViewController * const currentTopVC = [UIViewController imy_currentTopViewController];
    
    // 执行跳转协议
    [[IMYURIManager sharedInstance] runActionWithString:gotoURI];
    
    // 2秒后判断跳转是否正常
    imy_asyncMainBlock(2, ^{
        // 放开用户操作
        mainWindow.userInteractionEnabled = YES;
        
        // 无页面跳转，直接当做失败处理
        UIViewController * const apiNewTopVC = [UIViewController imy_currentTopViewController];
        if (currentTopVC == apiNewTopVC) {
            NSString *message = @"已取消会员权益迁移，\n请稍后再试";
            NSError *outError = [NSError errorWithDomain:message code:-1 userInfo:nil];
            apiCompletedBlock(nil, outError);
            return;
        }
        
        // 全局属性, 用于接收权益迁移的回调
        static IMYStashObject *kNewActionMap = nil;
        static dispatch_once_t onceToken;
        dispatch_once(&onceToken, ^{
            kNewActionMap = [IMYStashObject new];
            
            // 增加回调监听
            [[IMYURIManager sharedInstance] addForPath:@"rights/sdk/transferred" withActionBlock:^(IMYURIActionBlockObject *actionObject) {
                // 只执行一次回调
                UIViewController * const transferVC = kNewActionMap.obj1_weak;
                void (^transferredBlock)(NSDictionary *, NSError *) = kNewActionMap.obj1_copy;
                if (!transferVC || !transferredBlock) {
                    return;
                }
                kNewActionMap.obj1_weak = nil;
                kNewActionMap.obj1_copy = nil;
                // 页面已经被退出
                if (transferVC.imy_isPop) {
                    return;
                }
                
                BOOL isOK = [actionObject.uri.params[@"suceess"] boolValue];
                BOOL isGoback = [actionObject.uri.params[@"goback"] boolValue];
                NSString *message = actionObject.uri.params[@"message"];
                NSDictionary *bodyData = actionObject.uri.params[@"apidata"];
                if (!message.length) {
                    message = @"已取消会员权益迁移，\n请稍后再试";
                }
                // 如果有执行pop，会等pop动画结束后在回调
                void (^callbackBlock)(void) = ^{
                    NSMutableDictionary *resultMap = [NSMutableDictionary dictionary];
                    if ([bodyData isKindOfClass:NSDictionary.class] && bodyData.count > 0) {
                        [resultMap addEntriesFromDictionary:bodyData];
                    }
                    if (isOK) {
                        transferredBlock(resultMap, nil);
                    } else {
                        NSError *outError = [NSError errorWithDomain:message code:-1 userInfo:nil];
                        transferredBlock(resultMap, outError);
                    }
                };
                
                if (isGoback) {
                    [transferVC imy_pop:YES];
                    imy_asyncMainBlock(0.3, callbackBlock);
                } else {
                    callbackBlock();
                }
            }];
            
            // 页面切换通知
            [[NSNotificationCenter defaultCenter] addObserverForName:IMYPublicBaseViewController.IMYViewControllerDidActiveChangedNotification object:nil queue:nil usingBlock:^(NSNotification * _Nonnull notification) {
                IMYPublicBaseViewController *activedVC = notification.object;
                if (!activedVC.isViewActived) {
                    // 过滤不显示的事件
                    return;
                }
                
                UIViewController * const transferVC = kNewActionMap.obj1_weak;
                void (^transferredBlock)(NSDictionary *, NSError *) = kNewActionMap.obj1_copy;
                if (!transferVC || !transferredBlock) {
                    return;
                }
                if (transferVC.imy_isPop && transferVC) {
                    // 页面已经被Pop，但是回调还没执行，需要手动补发一个错误
                    kNewActionMap.obj1_weak = nil;
                    kNewActionMap.obj1_copy = nil;
                    
                    NSString *message = @"已取消会员权益迁移，\n请稍后再试";
                    NSError *outError = [NSError errorWithDomain:message code:-1 userInfo:nil];
                    transferredBlock(nil, outError);
                }
            }];
        });
        
        // 赋值给全局对象
        kNewActionMap.obj1_weak = apiNewTopVC;
        kNewActionMap.obj1_copy = apiCompletedBlock;
    });
}

- (void)handlePaymentFailedWithTransaction:(SKPaymentTransaction *)transaction {
    // 苹果支付错误
    NSError * const iapError = transaction.error;
    // 先获取续费优惠ID
    NSString * const renewalId = self.renewalId;
    // 封装错误
    NSString *errorDomain = nil;
    NSInteger errorCode = 0;
    NSDictionary *errorUserInfo = nil;
    BOOL const isCancelled = (iapError.code == SKErrorPaymentCancelled);
    if(!isCancelled) {
        if (iapError.code == SKErrorPaymentNotAllowed ||
            iapError.code == SKErrorClientInvalid) {
            errorDomain = @"您的设备暂不支持购买，请查阅苹果系统中「内容与隐私限制」相关支付限制";
        } else {
            errorDomain = @"苹果系统支付失败，您可以尝试切换网络后重试";
        }
        errorCode = -100;
    } else {
        errorDomain = @"用户取消!";
        errorCode = -101;
    }
    if (iapError != nil) {
        errorUserInfo = @{
            NSUnderlyingErrorKey : iapError
        };
    }
    
    // 传递给外部的错误信息
    NSError *outError = [NSError errorWithDomain:errorDomain code:errorCode userInfo:errorUserInfo];
    [self handlePayCompletedWithResult:nil error:outError];
    
    // 结束IAP支付流程
    [[SKPaymentQueue defaultQueue] finishTransaction:transaction];
    
    // 支付失败
    NSUInteger const errorType = (isCancelled ? IMYErrorTraceTypeOthers : IMYErrorTraceTypeNoneUI);
    NSString * const errorMsg = (isCancelled ? @"rights-pay-cancel" : @"rights-pay-fails");
    [IMYErrorTraces postWithType:errorType pageName:IMYMeetyouHTTPHooks.currentPageName category:IMYErrorTraceCategoryRights message:errorMsg detail:@{
        @"uuid" : self.myUUID ?: @"",
        @"renewalId" : renewalId ?: @"",
        @"iapPid" : transaction.payment.productIdentifier ?: @"",
        @"iapTid" : transaction.transactionIdentifier ?: @"",
        @"iapTorigid" : transaction.originalTransaction.transactionIdentifier ?: @"",
        @"iapState" : @(transaction.transactionState),
        @"restore" : @(self.isRestorePaying),
        @"country" : NSLocale.currentLocale.countryCode ?: @"",
        @"error" : @{
            @"code" : @(iapError.code),
            @"reason" : iapError.localizedFailureReason ?: iapError.domain ?: @"",
        },
        @"offer_sign" : self.offer_sign ?: @"",
    } needRetry:YES];
    
    // 正常下单：支付失败上报
    if (!renewalId.length) {
        NSMutableDictionary *params = [NSMutableDictionary dictionary];
        
        NSString * const accountUid = IMYHIVE_BINDER(IOCAppInfo).getAccountModelUserID;
        params[@"uid"] = (accountUid.length > 0 ? accountUid : [IMYPublicAppHelper shareAppHelper].userid);
        params[@"sub_plan_id"] = @(self.planId.integerValue);
        params[@"sub_plan_pricing_config_id"] = @(self.priceId.integerValue);
        params[@"channel_type"] = @1;
        params[@"user_promotion_id"] = self.appendParams[@"user_promotion_id"];
        params[@"amount"] = self.appendParams[@"amount"];
        params[@"reason"] = [NSString stringWithFormat:@"%ld", iapError.code];
        params[@"timestamp"] = [NSNumber numberWithLong:IMYDateTimeIntervalSince1970()];
        
        NSDictionary *jsonPlus = [params copy];
        NSString *verifyData = [NSString imy_encodeScriptWithString:jsonPlus.imy_jsonString
                                                             aesKey:@"bdc574e1e4f1580491d0ab57598fcce1"];
        // 移除加密字段，添加公开字段
        [params removeAllObjects];
        params[@"verify_data"] = verifyData;
        params[@"scene_key"] = self.appendParams[@"scene_key"];
        params[@"main_tab"] = self.appendParams[@"main_tab"];
#ifdef DEBUG
        // Debug 增加明文参数
        params[@"params"] = jsonPlus;
#endif
        [[IMYServerRequest postPath:@"v3/sub/order_report" host:sub_seeyouyima_com params:params headers:nil] subscribeNext:^(id  _Nullable x) {
           //...
        }];
    }
}

- (void)handlePayCompletedWithResult:(NSDictionary * const)result error:(NSError * const)error {
    imy_asyncMainBlock(^{
        if (!self.payCompletedBlock) {
            return;
        }
        // 隐藏支付 loading
        [self hideLoadingHUD];
        // 有外部回调则追加接口结果
        NSMutableDictionary *infoMap = [NSMutableDictionary dictionary];
        // 追加业务参数
        [infoMap addEntriesFromDictionary:result];
        // 创建的订单信息：https://apidoc.seeyouyima.com/doc/65a8ede57f3e781e049b3e1e
        [infoMap setValue:self.currentOrderData forKey:@"order"];
        // 支付验证成功：https://apidoc.seeyouyima.com/doc/65a74f977f3e781e049b3168
        [infoMap setValue:self.currentVerifyData forKey:@"verify"];
        // 返回接口数据
        self.payCompletedBlock(infoMap, error);
        // 清空外部回调
        self.payCompletedBlock = nil;
        self.payProgressBlock = nil;
        self.isRestorePaying = NO;
        
        // 清空订单信息
        self.planId = nil;
        self.priceId = nil;
        self.renewalId = nil;
        
        // 继续发送待检测数据
        [self outBoxSendVerifyData];
    });
}

- (void)handlePayProgressWithState:(SKPaymentTransactionState)state {
    imy_asyncMainBlock(^{
        if (!self.payProgressBlock) {
            return;
        }
        // 有外部回调则追加接口结果
        NSMutableDictionary *infoMap = [NSMutableDictionary dictionary];
        // 创建的订单信息：https://apidoc.seeyouyima.com/doc/65a8ede57f3e781e049b3e1e
        [infoMap setValue:self.currentOrderData forKey:@"order"];
        // 返回接口数据
        self.payProgressBlock(infoMap, state);
    });
}

#pragma mark - 支付信息待发送区

- (void)outBoxSendVerifyData {
    [NSObject imy_asyncBlock:^{
        if (self.isPaying || self.iapReceiptVerifying) {
            // 正在支付中，等待下次触发
            self.retryOutBoxDelay = MIN(10, self.retryOutBoxDelay + 2);
            [self outBoxSendVerifyData];
            return;
        }
        // 最多重试20次
        if (self.retryOutBoxCount > 20) {
            return;
        }
        self.iapReceiptVerifying = YES;
        NSString * const filePath = self.outBoxDataFilePath;
        NSDictionary * const jsonMap = [NSDictionary dictionaryWithContentsOfFile:filePath];
        BOOL needSendData = YES;
        if (jsonMap.count > 0) {
            // 获取苹果订单号
            NSString *iapTid = jsonMap[@"transaction_order_id"];
            if (!iapTid.length) {
                iapTid = jsonMap[@"transaction_id"];
            }
            if (iapTid.length > 0 && !iapTid.imy_isPureInt) {
                // 订单号不是纯数字，有概率是黑产刷单，删除超过1个月的缓存数据
                NSDictionary *attributes = [[NSFileManager defaultManager] attributesOfItemAtPath:filePath error:nil];
                NSDate *date = attributes[NSFileModificationDate] ?: attributes[NSFileCreationDate];
                if (date && labs([date distanceInDaysToDate:NSDate.date]) > 30) {
                    [self outBoxDeleteVerifyData];
                    needSendData = NO;
                }
            }
        }
        if (needSendData && jsonMap.count > 0) {
            [self startSubVerifyWithData:jsonMap
                          completedBlock:^(NSDictionary *result, NSError * const error) {
                // 发一个全局通知
                if (!error) {
                    [[NSNotificationCenter defaultCenter] postNotificationName:IMYRightsPaySDK.payVerifyCompletedNotification object:nil];
                }
                self.iapReceiptVerifying = NO;
            }];
        } else {
            self.iapReceiptVerifying = NO;
        }
    } onQueue:dispatch_get_global_queue(0, 0) afterSecond:self.retryOutBoxDelay forKey:@"IMYRightsPaySDK.sendVerifyDataByOutBox"];
}

- (NSString *)outBoxDataFilePath {
    NSString *fileName = [NSString stringWithFormat:@"data-%@.plist", [IMYPublicAppHelper shareAppHelper].userid];
    NSString *filePath = [NSString imy_pathForName:fileName atDocumentsDirs:@"rights-outbox"];
    return filePath;
}

- (void)outBoxDeleteVerifyData {
    NSString * const filePath = self.outBoxDataFilePath;
    const BOOL isOK = [[NSFileManager defaultManager] removeItemAtPath:filePath error:nil];
    if (!isOK) {
        // 删除失败，尝试写入空白文件
        [@{} writeToFile:filePath atomically:YES];
    }
}

- (void)outBoxWriteVerifyData:(NSDictionary *)jsonMap {
    NSString * const filePath = self.outBoxDataFilePath;
    const BOOL isOK = [jsonMap writeToFile:filePath atomically:YES];
    if (!isOK) {
        // 写入失败，尝试删除文件，再试一次
        [[NSFileManager defaultManager] removeItemAtPath:filePath error:nil];
        [jsonMap writeToFile:filePath atomically:YES];
    }
}

#pragma mark - 测试

IMY_KYLIN_FUNC_PREMAIN_ASYNC {
    [IMYRightsPaySDK setupTestURI];
}

+ (void)setupTestURI {
    [[IMYURIManager shareURIManager] addPathActionBlock:^(IMYURIActionBlockObject *actionObject) {
        NSString *productId = actionObject.uri.params[@"productId"];
        if (productId.length > 0) {
            [UIWindow imy_showLoadingHUD];
            [IMYRightsPaySDK sharedInstance].payCompletedBlock = ^(NSDictionary *result, NSError *error) {
                //...
            };
            [[IMYRightsPaySDK sharedInstance] startIAPWithProductId:productId];
        }
    } forPath:@"rights/pay/test"];
}

@end
