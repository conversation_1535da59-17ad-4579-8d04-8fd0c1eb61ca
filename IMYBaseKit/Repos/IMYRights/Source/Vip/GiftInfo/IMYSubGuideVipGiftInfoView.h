//
//  IMYSubGuideVipGiftInfoView.h
//  IMYBaseKit
//
//  Created by ljh on 2024/7/1.
//

#import <UIKit/UIKit.h>
#import "IMYBaseKit.h"
#import "IMYSubGuideVipInfo.h"

NS_ASSUME_NONNULL_BEGIN

@interface IMYSubGuideVipGiftInfoView : UIView

/// 按宽度生成UI
+ (instancetype)giftInfoViewWithBoxWidth:(CGFloat)width;

/// 场景Key
@property (nonatomic, copy) NSString *sceneKey;

/// 显示样式，0：默认样式，1：大卡片样式
@property (nonatomic, assign) NSInteger showStyle;

/// 修改当前价格
- (void)updateWithCurrentPricing:(IMYSubGuidePricingListItem *)currentPricing;

/// 只更新选中的赠礼信息
- (void)onlyUpdateSelectedItems:(IMYSubGuidePricingListItem *)currentPricing;

/// 修改箭头位置
- (void)updateWithArrowX:(CGFloat)itemCenterX;
@property (nonatomic, assign) CGFloat arrowItemWidth;

/// 赠礼信息
@property (nonatomic, strong, readonly) IMYSubGuidePricingListItem *currentPricing;
@property (nonatomic, strong, readonly) IMYSubGuidePricingGiftInfoModel *giftInfo;
/// 选中的赠礼项
@property (nonatomic, copy, readonly) NSArray<IMYSubGuidePricingGiftInfoListItem *> *selectedItems;

/// 选中项修改
@property (nonatomic, copy) void(^onSelectItemDidChanged)(void);

@end

NS_ASSUME_NONNULL_END
